import branch from 'branch-sdk';
import { generateBranchUrl } from '@utils/branchUtils';

const BRANCH_KEY = import.meta.env.VITE_BRANCH_KEY as string;

if (!BRANCH_KEY) throw new Error('Branch Key is not provided. Please add VITE_BRANCH_KEY in .env');
const options = { no_journeys: true };

branch.init(BRANCH_KEY, options, (err: string | null, data: any) => {
  if (err) {
    console.error('Branch.io Initialization Error:', err);
    return;
  }
  generateBranchUrl(data);
  console.log('Branch.io Initialized Successfully', data);
});

export default branch;
