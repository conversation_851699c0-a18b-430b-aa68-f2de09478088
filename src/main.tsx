import React from 'react';
import ReactDOM from 'react-dom/client';
import { RouterProvider } from 'react-router-dom';
import branch from 'branch-sdk';
import { ROUTE_ACTIONS, ROUTE_VARIABLES } from '@user/lib/constants';
import { generateBranchUrl } from '@utils/branchUtils';
import { getDeeplinkPath, safeJsonParse } from '@utils/utils';

import { router } from './routes';

import './globals.css';

const BRANCH_KEY = import.meta.env.VITE_BRANCH_KEY as string;
const isDevelopment = import.meta.env.DEV;

if (!BRANCH_KEY) throw new Error('Branch Key is not provided. Please add VITE_BRANCH_KEY in .env');

// Enhanced initialization with retry logic

const processBranchData = (data: any) => {
  try {
    const response = typeof data === 'string' ? safeJsonParse(data) : data;

    console.log('Processing Branch data:', response);

    const deeplinkPath = getDeeplinkPath(response);
    if (deeplinkPath) {
      const formattedPath = deeplinkPath.startsWith('/') ? deeplinkPath : `/${deeplinkPath}`;
      console.log('Redirecting to:', formattedPath);
      localStorage.setItem('deep_link_path', formattedPath);
    } else {
      console.log('No $deeplink_path found in Branch data');
    }

    // Only generate Branch URL if we have meaningful data
    if (response && Object.keys(response).length > 0) {
      generateBranchUrl(response);
    }
  } catch (error) {
    console.error('Branch data processing failed:', error);
  }
};

const initializeBranch = (retryCount = 0, maxRetries = 3) => {
  const options = {
    no_journeys: true,
    debug: isDevelopment,
    tracking_disabled: false,
    // Force initialization even if no referring link
    branch_match_id: isDevelopment ? Date.now().toString() : undefined,
  };

  console.log(`Branch.io initialization attempt ${retryCount + 1}/${maxRetries + 1}`);

  branch.init(BRANCH_KEY, options, function branchCallback(err: string | null, data: any) {
    if (err) {
      console.error(`Branch.io Initialization Error (attempt ${retryCount + 1}):`, err);
      // Retry logic
      if (retryCount < maxRetries) {
        console.log(`Retrying Branch.io initialization in 2 seconds...`);
        setTimeout(() => {
          initializeBranch(retryCount + 1, maxRetries);
        }, 1000);
        return;
      }
      console.error('Branch.io initialization failed after all retries');
      // Continue with app initialization even if Branch fails
      return;
    }

    console.log('Branch.io Initialized Successfully', data);

    // Check if data exists and has expected structure
    if (!data || (typeof data === 'object' && Object.keys(data).length === 0)) {
      console.warn('Branch.io initialized but no data received - this is normal for direct visits');

      // For local testing, simulate some data
      if (isDevelopment) {
        console.log('Creating mock Branch data for local testing');
        const mockData = {
          '+clicked_branch_link': false,
          '+is_first_session': true,
          '+match_guaranteed': false,
          '~creation_source': 'Web SDK',
          '~id': Date.now().toString(),
          '~referring_link': null,
        };
        processBranchData(mockData);
      }
    } else {
      processBranchData(data);
    }
  });
};

// Handle routing
const { DASHBOARD } = ROUTE_VARIABLES;
const { VIEW } = ROUTE_ACTIONS;

if (localStorage.getItem('deep_link_path')) {
  const path = localStorage.getItem('deep_link_path') ?? '';
  console.log('deeplink path found', path);
  localStorage.removeItem('deep_link_path');
  window.location.pathname = path;
} else if (window.location.pathname === '/') {
  window.location.pathname = `/${DASHBOARD}/${VIEW}`;
}
// clevertap.init(CLEVERTAP_KEY, 'in1');
// Render the app
ReactDOM.createRoot(document.getElementById('root') as HTMLElement).render(
  <React.StrictMode>
    <RouterProvider router={router} />
  </React.StrictMode>
);

// Start initialization
initializeBranch();
