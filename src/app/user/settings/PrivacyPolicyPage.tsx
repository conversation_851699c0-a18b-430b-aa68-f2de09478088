import { useEffect } from 'react';
import { Flex, Stack, Text, useToast } from '@chakra-ui/react';
import parse from 'html-react-parser';

import { GenericSettingsPage } from './GenericSettingsPage';
import { useFetchAppSettingData } from '@lib/contentLibrary/hooks/useFetchAppSettingData';

export function PrivacyPolicyPage() {
  const { data: appSettingData, error: appSettingError } = useFetchAppSettingData();
  const toast = useToast();

  // Scroll to top when component mounts
  useEffect(() => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  }, []);
  if (appSettingError) {
    toast({
      title: 'Something went wrong! Try again later.',
      status: 'error',
      duration: 3000,
      isClosable: true,
    });
  }
  const { section_legal_privacy_title, section_legal_privacy_content } = appSettingData || {};
  if (!section_legal_privacy_content) {
    toast({
      title: 'Something went wrong! Try again later.',
      status: 'error',
      duration: 3000,
      isClosable: true,
    });
    return null;
  }

  return (
    <GenericSettingsPage
      title={section_legal_privacy_title}
      width="100%"
      maxWidth="800px"
    >
      <Stack margin="32px 12px 60px 12px">
        <Text
          fontSize="2xl"
          fontWeight="bold"
        >
          {section_legal_privacy_title}
        </Text>
        <Flex
          direction="column"
          gap="24px"
          color="fluentHealthText.200"
        >
          {parse(section_legal_privacy_content)}
        </Flex>
      </Stack>
    </GenericSettingsPage>
  );
}
