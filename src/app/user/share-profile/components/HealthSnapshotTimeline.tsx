import React from 'react';
import { Flex, Text } from '@chakra-ui/react';

import EmptyState from './EmptyState';

export default function HealthSnapshotTimeline() {
  return (
    <Flex direction="column">
      <Text
        fontSize="13"
        textTransform="uppercase"
        fontWeight="medium"
        letterSpacing="1.56px"
        lineHeight="16px"
        mt="20px"
      >
        My Health Snapshot
      </Text>

      <EmptyState
        message="No health details added yet"
        margin="16px 0px 0px 0px"
      />
      <Text
        fontSize="13"
        textTransform="uppercase"
        fontWeight="medium"
        letterSpacing="1.56px"
        lineHeight="16px"
        mt="20px"
      >
        My Health Timeline
      </Text>

      <EmptyState
        message="No timeline entries yet"
        margin="16px 0px 0px 0px"
      />
    </Flex>
  );
}
