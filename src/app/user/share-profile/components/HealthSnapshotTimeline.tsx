import React from 'react';
import { Flex, Text } from '@chakra-ui/react';

import HealthSnapshot from './HealthSnapshot';
import HealthTimeline from './HealthTimeline';

export default function HealthSnapshotTimeline() {
  return (
    <Flex
      direction="column"
      gap={4}
    >
      <Text
        fontSize="13"
        textTransform="uppercase"
        fontWeight="medium"
        letterSpacing="1.56px"
        lineHeight="16px"
      >
        My Health Snapshot
      </Text>

      <HealthSnapshot />
      <Text
        fontSize="13"
        textTransform="uppercase"
        fontWeight="medium"
        letterSpacing="1.56px"
        lineHeight="16px"
        mt="10px"
      >
        My Health Timeline
      </Text>
      <HealthTimeline />
    </Flex>
  );
}
