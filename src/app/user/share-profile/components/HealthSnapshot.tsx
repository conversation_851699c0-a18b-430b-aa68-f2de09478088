import React from 'react';
import { Badge, Box, Divider, Flex, HStack, Text, VStack } from '@chakra-ui/react';

import EmptyState from './EmptyState';

interface Symptom {
  name: string;
  startDate: string;
  endDate: string;
  severity: 'Mild' | 'Severe';
}

interface Condition {
  name: string;
  startDate: string;
  endDate: string;
}

interface HealthSnapshotProps {
  symptoms?: Symptom[];
  conditions?: Condition[];
}

// Reusable section component
interface SectionProps {
  title: string;
  children: React.ReactNode;
}

const Section = ({ title, children }: SectionProps) => (
  <Box mb={6}>
    <HStack spacing={2} mb={3}>
      <Text fontWeight="semibold" fontSize="md">
        {title}
      </Text>
    </HStack>
    <VStack
      spacing={4}
      align="stretch"
      p={3}
      borderRadius="lg"
      bg="linear-gradient(180deg, #FFF6E9 0%, #FFFFFF 100%)"
    >
      {children}
    </VStack>
  </Box>
);

// Reusable symptom item component
const SymptomItem = ({ symptom, showDivider }: { symptom: Symptom; showDivider: boolean }) => (
  <Box p={3}>
    <Flex justify="space-between" align="center" mb={1}>
      <Text fontWeight="semibold">{symptom.name}</Text>
      <Badge
        colorScheme={symptom.severity === 'Mild' ? 'purple' : 'red'}
        variant="subtle"
        borderRadius="full"
        px={2}
      >
        {symptom.severity}
      </Badge>
    </Flex>
    <Text fontSize="sm" color="gray.600">
      {symptom.startDate} to {symptom.endDate}
    </Text>
    {showDivider && <Divider mt={2} />}
  </Box>
);

// Reusable condition item component
const ConditionItem = ({ condition }: { condition: Condition }) => (
  <Box p={3}>
    <Text fontWeight="semibold">{condition.name}</Text>
    <Text fontSize="sm" color="gray.600">
      {condition.startDate} to {condition.endDate}
    </Text>
  </Box>
);

export default function HealthSnapshot({ symptoms = [], conditions = [] }: HealthSnapshotProps) {
  const isEmpty = symptoms.length === 0 && conditions.length === 0;

  if (isEmpty) {
    return (
      <Flex direction="column">
        <EmptyState message="No health details added yet" />
      </Flex>
    );
  }

  return (
    <VStack
      spacing={6}
      align="stretch"
      p={4}
      bgGradient="linear(196deg, #FFF2DF 0%, #DADCFF 100%)"
      overflowY="auto"
    >
      {/* Recent Symptoms Section */}
      {symptoms.length > 0 && (
        <Section title="Recent Symptoms (last 6 months)">
          {symptoms.map((symptom, index) => (
            <SymptomItem
              key={index}
              symptom={symptom}
              showDivider={index < symptoms.length - 1}
            />
          ))}
        </Section>
      )}

      {/* Chronic Conditions Section */}
      {conditions.length > 0 && (
        <Section title="Chronic Conditions">
          {conditions.map((condition, index) => (
            <ConditionItem key={index} condition={condition} />
          ))}
        </Section>
      )}
    </VStack>
  );
}
