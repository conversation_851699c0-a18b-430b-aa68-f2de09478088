import React from 'react';
import { Bad<PERSON>, <PERSON>, Divider, Flex, HStack, Icon, Text, VStack } from '@chakra-ui/react';

import EmptyState from './EmptyState';

interface Symptom {
  name: string;
  startDate: string;
  endDate: string;
  severity: 'Mild' | 'Severe';
}

interface Condition {
  name: string;
  startDate: string;
  endDate: string;
}

interface HealthSnapshotProps {
  symptoms?: Symptom[];
  conditions?: Condition[];
}

export default function HealthSnapshot({ symptoms = [], conditions = [] }: HealthSnapshotProps) {
  const isEmpty = symptoms.length === 0 && conditions.length === 0;

  if (isEmpty) {
    return (
      <Flex direction="column">
        <EmptyState message="No health details added yet" />
      </Flex>
    );
  }

  return (
    <Flex
      direction="column"
      p={4}
      bgGradient="linear(196deg, #FFF2DF 0%, #DADCFF 100%)"
      overflowY="auto"
    >
      {/* Recent Symptoms Section */}
      {symptoms.length > 0 && (
        <Box mb={6}>
          <HStack
            spacing={2}
            mb={3}
          >
            {/* <Icon as={MdLocalHospital} color="purple.500" boxSize={5} /> */}
            <Text
              fontWeight="semibold"
              fontSize="md"
            >
              Recent Symptoms (last 6 months)
            </Text>
          </HStack>

          <VStack
            spacing={4}
            align="stretch"
            p={3}
            borderRadius="lg"
            bg="linear-gradient(180deg, #FFF6E9 0%, #FFFFFF 100%)"
          >
            {symptoms.map((symptom, index) => (
              <Box
                key={index}
                p={3}
              >
                <Flex
                  justify="space-between"
                  align="center"
                  mb={1}
                >
                  <Text fontWeight="semibold">{symptom.name}</Text>
                  <Badge
                    colorScheme={symptom.severity === 'Mild' ? 'purple' : 'red'}
                    variant="subtle"
                    borderRadius="full"
                    px={2}
                  >
                    {symptom.severity}
                  </Badge>
                </Flex>
                <Text
                  fontSize="sm"
                  color="gray.600"
                >
                  {symptom.startDate} to {symptom.endDate}
                </Text>
                {index < symptoms.length - 1 && <Divider mt={2} />}
              </Box>
            ))}
          </VStack>
        </Box>
      )}

      {/* Chronic Conditions Section */}
      {conditions.length > 0 && (
        <Box>
          <HStack
            spacing={2}
            mb={3}
          >
            {/* <Icon as={FiClock} color="orange.400" boxSize={5} /> */}
            <Text
              fontWeight="semibold"
              fontSize="md"
            >
              Chronic Conditions
            </Text>
          </HStack>

          <VStack
            spacing={4}
            align="stretch"
            p={3}
            borderRadius="lg"
            bg="linear-gradient(180deg, #FFF6E9 0%, #FFFFFF 100%)"
          >
            {conditions.map((condition, index) => (
              <Box
                key={index}
                p={3}
              >
                <Text fontWeight="semibold">{condition.name}</Text>
                <Text
                  fontSize="sm"
                  color="gray.600"
                >
                  {condition.startDate} to {condition.endDate}
                </Text>
              </Box>
            ))}
          </VStack>
        </Box>
      )}
    </Flex>
  );
}
