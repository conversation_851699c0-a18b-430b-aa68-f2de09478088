import React from 'react';
import { <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Flex, HStack, Text, VStack } from '@chakra-ui/react';
import { ReactComponent as SymptomsIcon } from '@assets/icons/symptoms-icon.svg';
import { ReactComponent as ConditionIcon } from '@assets/icons/condition-icon.svg';
import { ReactComponent as MedicationSupplementsIcon } from '@assets/icons/medication-supplements-icon.svg';
import { ReactComponent as AllergiesIcon } from '@assets/icons/allergies-icon.svg';
import { ReactComponent as ReproductiveHealthIcon } from '@assets/icons/reproductive-health-icon.svg';
import { ReactComponent as FamilyHistoryIcon } from '@assets/icons/family-history-icon.svg';
import { ReactComponent as LifestyleBasicsIcon } from '@assets/icons/lifestyle-basics-icon.svg';

import EmptyState from './EmptyState';
const symptoms: DataPros[] = [
  { name: 'Tension Headache', startDate: '20-Jun-2025', endDate: 'Present', severity: 'Mild' },
  { name: 'Fatigue', startDate: '15-Mar-2024', endDate: '30-Mar-2024', severity: 'Severe' },
  { name: 'Fatigue', startDate: '15-Mar-2024', endDate: '30-Mar-2024', severity: 'Severe' },
  { name: 'Fatigue', startDate: '15-Mar-2024', endDate: '30-Mar-2024', severity: 'Severe' },
  { name: 'Fatigue', startDate: '15-Mar-2024', endDate: '30-Mar-2024', severity: 'Severe' },
];
const conditions: DataPros[] = [
  { name: 'Type 2 diabetes', startDate: '29-Jun-2025', endDate: 'Present' },
  { name: 'Type 2 diabetes', startDate: '29-Jun-2025', endDate: 'Present' },
  { name: 'Type 2 diabetes', startDate: '29-Jun-2025', endDate: 'Present' },
  { name: 'Type 2 diabetes', startDate: '29-Jun-2025', endDate: 'Present' },
];
const medicationsSupplements: DataPros[] = [
  { name: 'Vitamin D3', startDate: '29-Jun-2025', endDate: 'Present' },
  { name: 'Vitamin D3', startDate: '29-Jun-2025', endDate: 'Present' },
  { name: 'Vitamin D3', startDate: '29-Jun-2025', endDate: 'Present' },
  { name: 'Vitamin D3', startDate: '29-Jun-2025', endDate: 'Present' },
];
const activeAllergies: DataPros[] = [
  { name: 'Vitamin D3', startDate: '29-Jun-2025', endDate: 'Present' },
  { name: 'Vitamin D3', startDate: '29-Jun-2025', endDate: 'Present' },
  { name: 'Vitamin D3', startDate: '29-Jun-2025', endDate: 'Present' },
  { name: 'Vitamin D3', startDate: '29-Jun-2025', endDate: 'Present' },
];
const reproductiveHealth: DataPros[] = [
  { name: 'Vitamin D3', startDate: '29-Jun-2025', endDate: 'Present' },
  { name: 'Vitamin D3', startDate: '29-Jun-2025', endDate: 'Present' },
  { name: 'Vitamin D3', startDate: '29-Jun-2025', endDate: 'Present' },
  { name: 'Vitamin D3', startDate: '29-Jun-2025', endDate: 'Present' },
];
const familyHistory: DataPros[] = [
  { name: 'Vitamin D3', startDate: '29-Jun-2025', endDate: 'Present' },
  { name: 'Vitamin D3', startDate: '29-Jun-2025', endDate: 'Present' },
  { name: 'Vitamin D3', startDate: '29-Jun-2025', endDate: 'Present' },
  { name: 'Vitamin D3', startDate: '29-Jun-2025', endDate: 'Present' },
];
const lifestyleBasics: DataPros[] = [
  { name: 'Vitamin D3', startDate: '29-Jun-2025', endDate: 'Present' },
  { name: 'Vitamin D3', startDate: '29-Jun-2025', endDate: 'Present' },
  { name: 'Vitamin D3', startDate: '29-Jun-2025', endDate: 'Present' },
  { name: 'Vitamin D3', startDate: '29-Jun-2025', endDate: 'Present' },
];
interface DataPros {
  name: string;
  startDate: string;
  endDate: string;
  severity?: string;
}

// Reusable section component
interface SectionProps {
  title: string;
  icon: React.ReactNode;
  children: React.ReactNode;
}

const Section = ({ title, icon, children }: SectionProps) => (
  <Flex
    direction="column"
    mb={'20px'}
  >
    <HStack
      spacing={2}
      mb={3}
    >
      {icon}
      <Text
        fontWeight="400"
        fontSize="16px"
        letterSpacing="-0.32px"
        lineHeight="22px"
        fontFamily="Apercu Pro"
        fontStyle="normal"
        align={'left'}
      >
        {title}
      </Text>
    </HStack>
    <VStack
      spacing={4}
      align="stretch"
      p={3}
      borderRadius="lg"
      bg="linear-gradient(180deg, #FFF6E9 0%, #FFFFFF 100%)"
    >
      {children}
    </VStack>
  </Flex>
);

// Reusable symptom item component
const DataItem = ({ data, showDivider }: { data: DataPros; showDivider: boolean }) => (
  <Box p={3}>
    <Flex
      justify="space-between"
      mb={1}
    >
      <Text fontWeight="semibold">{data.name}</Text>
      {data.severity && (
        <Badge
          colorScheme={data.severity === 'Mild' ? 'purple' : 'red'}
          variant="subtle"
          borderRadius="full"
          px={2}
        >
          {data.severity}
        </Badge>
      )}
    </Flex>
    <Text
      fontSize="sm"
      align="left"
      color="gray.600"
    >
      {data.startDate} to {data.endDate}
    </Text>
    {showDivider && <Divider mt={2} />}
  </Box>
);

export default function HealthSnapshot() {
  const isEmpty = symptoms.length === 0 && conditions.length === 0 && medicationsSupplements.length === 0;

  if (isEmpty) {
    return (
      <Flex direction="column">
        <EmptyState message="No health details added yet" />
      </Flex>
    );
  }

  return (
    <VStack
      align="stretch"
      p={4}
      bgGradient="linear(196deg, #FFF2DF 0%, #DADCFF 100%)"
      overflowY="auto"
    >
      {/* Recent Symptoms Section */}
      {symptoms.length > 0 && (
        <Section
          title="Recent Symptoms (last 6 months)"
          icon={<SymptomsIcon />}
        >
          {symptoms.slice(0, 3).map((symptom, index) => (
            <DataItem
              key={index}
              data={symptom}
              showDivider={index < symptoms.length - 1}
            />
          ))}
          {symptoms?.length > 3 && (
            <Button
              size="sm"
              mt={2}
              // onClick={() => setShowAll((prev) => !prev)}
              variant="link"
              colorScheme="purple"
            >
              View More
            </Button>
          )}
        </Section>
      )}

      {/* Chronic Conditions Section */}
      {conditions.length > 0 && (
        <Section
          title="Chronic Conditions"
          icon={<ConditionIcon />}
        >
          {conditions.slice(0, 3).map((condition, index) => (
            <DataItem
              key={index}
              data={condition}
              showDivider={index < conditions.length - 1}
            />
          ))}
          {conditions?.length > 3 && (
            <Button
              size="sm"
              mt={2}
              // onClick={() => setShowAll((prev) => !prev)}
              variant="link"
              colorScheme="purple"
            >
              View More
            </Button>
          )}
        </Section>
      )}
      {/* Ongoing Medications and Supplements Section */}
      {medicationsSupplements.length > 0 && (
        <Section
          title="Ongoing Medications and Supplements"
          icon={<MedicationSupplementsIcon />}
        >
          {medicationsSupplements.slice(0, 3).map((medicationsSupplement, index) => (
            <DataItem
              key={index}
              data={medicationsSupplement}
              showDivider={index < medicationsSupplements.length - 1}
            />
          ))}
          {medicationsSupplements?.length > 3 && (
            <Button
              size="sm"
              mt={2}
              // onClick={() => setShowAll((prev) => !prev)}
              variant="link"
              colorScheme="purple"
            >
              View More
            </Button>
          )}
        </Section>
      )}
      {/* Active Allergies Section */}
      {activeAllergies.length > 0 && (
        <Section
          title="Active Allergies"
          icon={<AllergiesIcon />}
        >
          {activeAllergies.slice(0, 3).map((medicationsSupplement, index) => (
            <DataItem
              key={index}
              data={medicationsSupplement}
              showDivider={index < activeAllergies.length - 1}
            />
          ))}
          {activeAllergies?.length > 3 && (
            <Button
              size="sm"
              mt={2}
              // onClick={() => setShowAll((prev) => !prev)}
              variant="link"
              colorScheme="purple"
            >
              View More
            </Button>
          )}
        </Section>
      )}
      {/* Reproductive Health Section */}
      {reproductiveHealth.length > 0 && (
        <Section
          title="Reproductive Health"
          icon={<ReproductiveHealthIcon />}
        >
          {reproductiveHealth.slice(0, 3).map((medicationsSupplement, index) => (
            <DataItem
              key={index}
              data={medicationsSupplement}
              showDivider={index < reproductiveHealth.length - 1}
            />
          ))}
          {reproductiveHealth?.length > 3 && (
            <Button
              size="sm"
              mt={2}
              // onClick={() => setShowAll((prev) => !prev)}
              variant="link"
              colorScheme="purple"
            >
              View More
            </Button>
          )}
        </Section>
      )}
      {/* Family History Section */}
      {familyHistory.length > 0 && (
        <Section
          title="Family History"
          icon={<FamilyHistoryIcon />}
        >
          {familyHistory.slice(0, 3).map((medicationsSupplement, index) => (
            <DataItem
              key={index}
              data={medicationsSupplement}
              showDivider={index < familyHistory.length - 1}
            />
          ))}
          {familyHistory?.length > 3 && (
            <Button
              size="sm"
              mt={2}
              // onClick={() => setShowAll((prev) => !prev)}
              variant="link"
              colorScheme="purple"
            >
              View More
            </Button>
          )}
        </Section>
      )}
      {/* Lifestyle Basics Section */}
      {lifestyleBasics.length > 0 && (
        <Section
          title="Lifestyle Basics"
          icon={<LifestyleBasicsIcon />}
        >
          {lifestyleBasics.slice(0, 3).map((medicationsSupplement, index) => (
            <DataItem
              key={index}
              data={medicationsSupplement}
              showDivider={index < lifestyleBasics.length - 1}
            />
          ))}
          {lifestyleBasics?.length > 3 && (
            <Button
              size="sm"
              mt={2}
              // onClick={() => setShowAll((prev) => !prev)}
              variant="link"
              colorScheme="purple"
            >
              View More
            </Button>
          )}
        </Section>
      )}
    </VStack>
  );
}
