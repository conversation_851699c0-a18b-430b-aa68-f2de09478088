import React from 'react';
import { Box, Heading, Text, VStack } from '@chakra-ui/react';

export default function HealthSnapshot() {
  return (
    <Box
      borderRadius="16px 20px"
      borderTop="2px solid "
      borderColor="iris.500"
      boxShadow="md"
      mt="16px"
      p={4}
      textAlign="center"
      maxW="md"
      bgColor="periwinkle.100"
      mx="auto"
    >
      <Heading
        as="h3"
        size="sm"
        mb={3}
        fontWeight="500"
        color="gray.500"
      >
        My Health Snapshot
      </Heading>

      <VStack
        bg="white"
        borderRadius="4px"
        p={6}
        spacing={4}
        boxShadow="sm"
      >
        <Text
          fontSize="20px"
          fontFamily="P22 Mackinac"
          color="charcoal.70"
        >
          No health details added yet
        </Text>
      </VStack>
    </Box>
  );
}
