import React from 'react';
import { <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>r, Flex, HStack, Text, VStack } from '@chakra-ui/react';

import EmptyState from './EmptyState';

import { ReactComponent as SymptomsIcon } from '@assets/icons/symptoms-icon.svg';
import { ReactComponent as ConditionIcon } from '@assets/icons/condition-icon.svg';
import { ReactComponent as MedicationSupplementsIcon } from '@assets/icons/medication-supplements-icon.svg';
import { ReactComponent as AllergiesIcon } from '@assets/icons/allergies-icon.svg';
import { ReactComponent as ReproductiveHealthIcon } from '@assets/icons/reproductive-health-icon.svg';
import { ReactComponent as FamilyHistoryIcon } from '@assets/icons/family-history-icon.svg';
import { ReactComponent as LifestyleBasicsIcon } from '@assets/icons/lifestyle-basics-icon.svg';

interface HealthData {
  name: string;
  startDate: string;
  endDate: string;
  severity?: 'Mild' | 'Moderate' | 'Severe';
}

interface HealthSection {
  id: string;
  title: string;
  icon: React.ReactNode;
  data: HealthData[];
}

// Severity color mapping
const SEVERITY_COLORS = {
  Mild: { bg: 'iris.100', color: 'iris.600' },
  Moderate: { bg: 'beige.100', color: 'papaya.600' },
  Severe: { bg: 'papaya.10', color: 'red.100' },
} as const;
// Consolidated health data configuration
const healthSections: HealthSection[] = [
  {
    id: 'symptoms',
    title: 'Recent Symptoms (last 6 months)',
    icon: (
      <SymptomsIcon
        width="20px"
        height="20px"
      />
    ),
    data: [
      { name: 'Tension Headache', startDate: '20-Jun-2025', endDate: 'Present', severity: 'Mild' },
      { name: 'Fatigue', startDate: '15-Mar-2024', endDate: '30-Mar-2024', severity: 'Severe' },
      { name: 'Fatigue', startDate: '15-Mar-2024', endDate: '30-Mar-2024', severity: 'Moderate' },
      { name: 'Fatigue', startDate: '15-Mar-2024', endDate: '30-Mar-2024', severity: 'Severe' },
    ],
  },
  {
    id: 'conditions',
    title: 'Chronic Conditions',
    icon: (
      <ConditionIcon
        width="20px"
        height="20px"
      />
    ),
    data: [
      { name: 'Tension Headache', startDate: '20-Jun-2025', endDate: 'Present', severity: 'Mild' },
      { name: 'Fatigue', startDate: '15-Mar-2024', endDate: '30-Mar-2024', severity: 'Severe' },
      { name: 'Fatigue', startDate: '15-Mar-2024', endDate: '30-Mar-2024', severity: 'Moderate' },
      { name: 'Fatigue', startDate: '15-Mar-2024', endDate: '30-Mar-2024', severity: 'Severe' },
    ],
  },
  {
    id: 'medications',
    title: 'Ongoing Medications and Supplements',
    icon: (
      <MedicationSupplementsIcon
        width="20px"
        height="20px"
      />
    ),
    data: [
      { name: 'Tension Headache', startDate: '20-Jun-2025', endDate: 'Present', severity: 'Mild' },
      { name: 'Fatigue', startDate: '15-Mar-2024', endDate: '30-Mar-2024', severity: 'Severe' },
      { name: 'Fatigue', startDate: '15-Mar-2024', endDate: '30-Mar-2024', severity: 'Moderate' },
      { name: 'Fatigue', startDate: '15-Mar-2024', endDate: '30-Mar-2024', severity: 'Severe' },
    ],
  },
  {
    id: 'allergies',
    title: 'Active Allergies',
    icon: (
      <AllergiesIcon
        width="20px"
        height="20px"
      />
    ),
    data: [
      { name: 'Tension Headache', startDate: '20-Jun-2025', endDate: 'Present', severity: 'Mild' },
      { name: 'Fatigue', startDate: '15-Mar-2024', endDate: '30-Mar-2024', severity: 'Severe' },
      { name: 'Fatigue', startDate: '15-Mar-2024', endDate: '30-Mar-2024', severity: 'Moderate' },
      { name: 'Fatigue', startDate: '15-Mar-2024', endDate: '30-Mar-2024', severity: 'Severe' },
    ],
  },
  {
    id: 'reproductive',
    title: 'Reproductive Health',
    icon: (
      <ReproductiveHealthIcon
        width="20px"
        height="20px"
      />
    ),
    data: [
      { name: 'Tension Headache', startDate: '20-Jun-2025', endDate: 'Present', severity: 'Mild' },
      { name: 'Fatigue', startDate: '15-Mar-2024', endDate: '30-Mar-2024', severity: 'Severe' },
      { name: 'Fatigue', startDate: '15-Mar-2024', endDate: '30-Mar-2024', severity: 'Moderate' },
      { name: 'Fatigue', startDate: '15-Mar-2024', endDate: '30-Mar-2024', severity: 'Severe' },
    ],
  },
  {
    id: 'family',
    title: 'Family History',
    icon: (
      <FamilyHistoryIcon
        width="20px"
        height="20px"
      />
    ),
    data: [
      { name: 'Tension Headache', startDate: '20-Jun-2025', endDate: 'Present', severity: 'Mild' },
      { name: 'Fatigue', startDate: '15-Mar-2024', endDate: '30-Mar-2024', severity: 'Severe' },
      { name: 'Fatigue', startDate: '15-Mar-2024', endDate: '30-Mar-2024', severity: 'Moderate' },
      { name: 'Fatigue', startDate: '15-Mar-2024', endDate: '30-Mar-2024', severity: 'Severe' },
    ],
  },
  {
    id: 'lifestyle',
    title: 'Lifestyle Basics',
    icon: (
      <LifestyleBasicsIcon
        width="20px"
        height="20px"
      />
    ),
    data: [
      { name: 'Tension Headache', startDate: '20-Jun-2025', endDate: 'Present', severity: 'Mild' },
      { name: 'Fatigue', startDate: '15-Mar-2024', endDate: '30-Mar-2024', severity: 'Severe' },
      { name: 'Fatigue', startDate: '15-Mar-2024', endDate: '30-Mar-2024', severity: 'Moderate' },
      { name: 'Fatigue', startDate: '15-Mar-2024', endDate: '30-Mar-2024', severity: 'Severe' },
    ],
  },
];
// Reusable severity badge component
function SeverityBadge({ severity }: { severity: HealthData['severity'] }) {
  if (!severity) return null;

  const colors = SEVERITY_COLORS[severity];
  return (
    <Box
      px={2}
      py={1}
      borderRadius="full"
      bg={colors.bg}
      color={colors.color}
      fontSize="xs"
      fontWeight="medium"
    >
      {severity}
    </Box>
  );
}

// Reusable data item component
function DataItem({ data, showDivider }: { data: HealthData; showDivider: boolean }) {
  return (
    <Box>
      <Flex
        justify="space-between"
        mb={1}
      >
        <Text
          fontSize="16px"
          fontStyle="normal"
          fontWeight="400"
          lineHeight="21px"
          letterSpacing="-0.32px"
          color="gray.500"
        >
          {data.name}
        </Text>
        <SeverityBadge severity={data.severity} />
      </Flex>
      <Text
        fontSize="12px"
        fontStyle="normal"
        fontWeight="400"
        lineHeight="15px"
        letterSpacing="-0.24px"
        color="charcoal.70"
        align="left"
      >
        {data.startDate} to {data.endDate}
      </Text>
      {showDivider && <Divider mt={2} />}
    </Box>
  );
}

// Reusable section component
interface SectionProps {
  title: string;
  icon: React.ReactNode;
  children: React.ReactNode;
}

function Section({ title, icon, children }: SectionProps) {
  return (
    <Flex
      direction="column"
      mb="20px"
    >
      <HStack
        spacing={2}
        mb={3}
      >
        {icon}
        <Text
          fontWeight="400"
          fontSize="16px"
          letterSpacing="-0.32px"
          lineHeight="22px"
          fontFamily="Apercu Pro"
          align="left"
        >
          {title}
        </Text>
      </HStack>
      <VStack
        spacing={4}
        align="stretch"
        p={3}
        borderRadius="lg"
        bg="linear-gradient(180deg, #FFF6E9 0%, #FFFFFF 100%)"
      >
        {children}
      </VStack>
    </Flex>
  );
}

export default function HealthSnapshot() {
  const hasData = healthSections.some((section) => section.data.length > 0);

  if (!hasData) {
    return (
      <Flex direction="column">
        <EmptyState message="No health details added yet" />
      </Flex>
    );
  }

  return (
    <VStack
      spacing={6}
      align="stretch"
      p={4}
      bgGradient="linear(196deg, #FFF2DF 0%, #DADCFF 100%)"
      overflowY="auto"
    >
      {healthSections
        .filter((section) => section.data.length > 0)
        .map((section) => (
          <Section
            key={section.id}
            title={section.title}
            icon={section.icon}
          >
            {section.data.slice(0, 3).map((item, index) => (
              <DataItem
                key={`${item.name}`}
                data={item}
                showDivider={index < section.data.length - 1}
              />
            ))}
            {section?.data?.length > 3 && (
              <Button
                size="sm"
                mt={2}
                // onClick={() => setShowAll((prev) => !prev)}
                variant="link"
                color="iris.500"
              >
                View More
              </Button>
            )}
          </Section>
        ))}
    </VStack>
  );
}
