import { useState } from 'react';
import { Box, Flex } from '@chakra-ui/react';
import { useIsTablet } from '@components/ui/hooks/device.hook';

import { TabLink } from 'src/components/ui/Tab';
import MyHealthHistory from './MyHealthHistory';
import BasicInfo from './BasicInfo';
import HealthSnapshotTimeline from './HealthSnapshotTimeline';

function MobileProfileTabs() {
  const [selectedTab, setSelectedTab] = useState(0);
  return (
    <Flex
      direction="column"
      w="100%"
      mt="16px"
    >
      <Flex
        px="20px"
        gap="8px"
        overflowX="auto"
        className="hide-scrollbar"
      >
        <TabLink
          to=""
          onClick={() => {
            setSelectedTab(0);
          }}
          isActive={selectedTab === 0}
        >
          Health Snapshot
        </TabLink>
        <TabLink
          to=""
          onClick={() => {
            setSelectedTab(1);
          }}
          isActive={selectedTab === 1}
        >
          My Health History
        </TabLink>
        <TabLink
          to=""
          onClick={() => {
            setSelectedTab(2);
          }}
          isActive={selectedTab === 2}
        >
          Basic Info
        </TabLink>
      </Flex>
      <Box
        p="20px"
        bgColor="periwinkle.100"
      >
        {selectedTab === 0 && <HealthSnapshotTimeline />}
        {selectedTab === 1 && <MyHealthHistory />}
        {selectedTab === 2 && <BasicInfo />}
      </Box>
    </Flex>
  );
}

export default function ProfileTabs() {
  const [selectedTab, setSelectedTab] = useState(0);
  const isTablet = useIsTablet();

  if (isTablet) {
    return <MobileProfileTabs />;
  }
  return (
    <Flex
      direction="column"
      w="100%"
      mt="16px"
    >
      <Flex
        px="20px"
        gap="8px"
      >
        <TabLink
          to=""
          onClick={() => {
            setSelectedTab(0);
          }}
          isActive={selectedTab === 0}
        >
          My Health History
        </TabLink>
        <TabLink
          to=""
          onClick={() => {
            setSelectedTab(1);
          }}
          isActive={selectedTab === 1}
        >
          Basic Info
        </TabLink>
      </Flex>
      <Box
        p="20px"
        bgColor="periwinkle.100"
        borderRadius="20px"
      >
        {selectedTab === 0 && <MyHealthHistory />}
        {selectedTab === 1 && <BasicInfo />}
      </Box>
    </Flex>
  );
}
