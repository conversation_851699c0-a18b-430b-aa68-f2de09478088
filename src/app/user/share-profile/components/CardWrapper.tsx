import React from 'react';
import { Box, Text } from '@chakra-ui/react';

interface CardWrapperProps {
  title: string;
  children: React.ReactNode;
}

function CardWrapper({ title, children }: CardWrapperProps) {
  return (
    <Box
      borderRadius="16px 20px"
      borderTop="2px solid "
      borderColor="iris.500"
      boxShadow="md"
      mt="16px"
      p={4}
      textAlign="center"
      maxW="md"
      bgColor="periwinkle.100"
      mx="auto"
    >
      <Text
        fontSize="16px"
        fontWeight="500"
        color="gray.500"
        mb="13px"
      >
        {title}
      </Text>

      {children}
    </Box>
  );
}

export default CardWrapper;
