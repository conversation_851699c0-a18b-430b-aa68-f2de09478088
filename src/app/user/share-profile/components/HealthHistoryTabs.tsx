import { useState } from 'react';
import { Box, Flex } from '@chakra-ui/react';

import { recordProfileEvents } from '@user/lib/events-analytics-manager';

import { TabLink } from 'src/components/ui/Tab';
import { useAnalyticsService } from '@lib/state';
import { FamilyHistoryPage } from '../../profile/family-history/FamilyHistoryPage';
// import { BasicInfoPage } from '../../profile/basic-info/BasicInfoPage';

export default function HealthHistoryTabs() {
  const { trackEventInFlow } = useAnalyticsService();
  const [selectedTab, setSelectedTab] = useState(0);

  return (
    <Flex
      direction="column"
      w="100%"
      mt="16px"
    >
      <Flex
        px="20px"
        gap="8px"
      >
        <TabLink
          to={''}
          onClick={() => {
            setSelectedTab(0);
            recordProfileEvents(trackEventInFlow, {
              EventName: 'MyProfileTabInteracted',
              pi_tab_name: 'Family History',
            });
          }}
          isActive={selectedTab === 0}
        >
          My Health History
        </TabLink>
        <TabLink
          to={''}
          onClick={() => {
            setSelectedTab(1);
            recordProfileEvents(trackEventInFlow, {
              EventName: 'MyProfileTabInteracted',
              pi_tab_name: 'Basic Info',
            });
          }}
          isActive={selectedTab === 1}
        >
          Basic Info
        </TabLink>
      </Flex>
      <Box
        p="20px"
        bgColor="periwinkle.100"
        borderRadius="40px"
      >
        {selectedTab === 0 && <FamilyHistoryPage />}
        {/* {selectedTab === 1 && <BasicInfoPage />} */}
      </Box>
    </Flex>
  );
}
