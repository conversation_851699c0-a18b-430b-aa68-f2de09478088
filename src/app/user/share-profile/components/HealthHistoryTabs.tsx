import React, { useState } from 'react';
// import Symptoms from './HealthHistorySections/Symptoms';
// import Conditions from './HealthHistorySections/Conditions';
// import FamilyMembers from './FamilyMembers';
// import BasicInfo from './BasicInfo';

export default function HealthHistoryTabs() {
  const [tab, setTab] = useState<'history' | 'basic'>('history');

  return (
    <div className="bg-white rounded-lg shadow p-4 h-full">
      <div className="flex space-x-4 border-b mb-4">
        <button
          onClick={() => setTab('history')}
          className={`py-2 ${tab === 'history' ? 'border-b-2 border-blue-600 font-semibold' : ''}`}
        >
          My Health History
        </button>
        <button
          onClick={() => setTab('basic')}
          className={`py-2 ${tab === 'basic' ? 'border-b-2 border-blue-600 font-semibold' : ''}`}
        >
          Basic Info
        </button>
      </div>

      {/* {tab === 'history' ? (
        <div className="space-y-2 text-sm">
          <Symptoms />
          <Conditions />
          <FamilyMembers />
        </div>
      ) : (
        <BasicInfo />
      )} */}
    </div>
  );
}
