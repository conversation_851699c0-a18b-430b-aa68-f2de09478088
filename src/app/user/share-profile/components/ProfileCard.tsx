import React from 'react';
import { Box, Flex, Image, Text } from '@chakra-ui/react';

import { ProfileBioCard } from './ProfileBioCard';

import ProfileIcon from '@assets/icons/profile-icon.svg';

function ProfileCard(props: any) {
  return (
    <Box
      bg="fluentHealth.500"
      color="white"
      borderRadius="28px"
      marginTop="16px"
      minHeight="266px"
      w="100%"
    >
      <Flex>
        {/* Left: Icon and Name */}
        <Flex
          flexDir="column"
          align="center"
          justify="center"
          width="30%"
          borderRight={{ md: '1px solid white' }}
        >
          <Image
            src={ProfileIcon}
            width="92px"
            height="92px"
            mb="15px"
          />
          <Text fontSize={{ base: 'lg', md: '2xl' }}>{props?.userName || '--'}</Text>
        </Flex>

        {/* Right: Grid Details */}
        <ProfileBioCard />
      </Flex>
    </Box>
  );
}

export default ProfileCard;
