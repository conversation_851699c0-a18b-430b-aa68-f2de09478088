import React from 'react';
import {
  Box,
  Grid,
  Text,
  Flex,
  Image,
} from '@chakra-ui/react';
import ProfileIcon from '@assets/icons/profile-icon.svg';

const ProfileCard = () => {
  return (
    <Box
      bg="fluentHealth.500"
      color="white"
      borderRadius="2xl"
      marginTop="16px"
      p={{ base: 4, md: 6 }}
      w="100%"
    >
      <Grid
        templateColumns={{ base: '1fr', md: '1fr 4fr' }}
        gap={4}
        alignItems="center"
      >
        {/* Left: Icon and Name */}
        <Flex flexDir="column" align={{ base: 'center', md: 'flex-start' }} width="328px">
          <Image
            src={ProfileIcon}
            width="92px"
            height="92px"
            mb={2}
          />
          <Text fontSize={{ base: 'lg', md: '2xl' }}>
            Dipti Siddamsettiwar
          </Text>
        </Flex>

        {/* Right: Grid Details */}
        <Grid
          templateColumns={{ base: 'repeat(2, 1fr)', md: 'repeat(3, 1fr)' }}
          gap={4}
        >
          <Box>
            <Text fontSize="sm" opacity={0.8}>Height</Text>
            <Text fontWeight="medium">5'6”</Text>
          </Box>
          <Box>
            <Text fontSize="sm" opacity={0.8}>Weight</Text>
            <Text fontWeight="medium">75 kg</Text>
          </Box>
          <Box>
            <Text fontSize="sm" opacity={0.8}>Age</Text>
            <Text fontWeight="medium">30</Text>
          </Box>
          <Box>
            <Text fontSize="sm" opacity={0.8}>Blood group</Text>
            <Text fontWeight="medium">A+</Text>
          </Box>
          <Box>
            <Text fontSize="sm" opacity={0.8}>Gender</Text>
            <Text fontWeight="medium">Female</Text>
          </Box>
          <Box>
            <Text fontSize="sm" opacity={0.8}>Sex assigned at birth</Text>
            <Text fontWeight="medium">Prefer not to say</Text>
          </Box>
          <Box gridColumn={{ base: 'span 2', md: 'span 3' }}>
            <Text fontSize="sm" opacity={0.8}>Ethnicity</Text>
            <Text fontWeight="medium">South Asian (Indo/Pak)</Text>
          </Box>
        </Grid>
      </Grid>
    </Box>
  );
};

export default ProfileCard;
