import React from 'react';
import { Box, Flex, Image, Text } from '@chakra-ui/react';

import { ProfileBioCard } from './ProfileBioCard';

import ProfileIcon from '@assets/icons/profile-icon.svg';
import { useIsTablet } from '@components/ui/hooks/device.hook';

function MobileProfilePage(props: any) {
  return (
    <Box
      position="relative"
      bgColor="fluentHealth.500"
      bgImage="/mobile-background-profile-cover.png"
      bgPos="center"
      mt="5"
      borderRadius="4xl"
      w="100%"
      h="332px"
    >
      {/* Left: Icon and Name */}
      <Flex
        flexDir="column"
        align="center"
        justify="center"
        width="30%"
        borderRight={{ md: '1px solid white' }}
      >
        <Image
          src={ProfileIcon}
          width="92px"
          height="92px"
          mb="15px"
        />
        <Text fontSize={{ base: 'lg', md: '2xl' }} color="white">{props?.userName}</Text>
      </Flex>
    </Box>
  );
}

function ProfileCard(props: any) {
  const isTablet = useIsTablet();

  if (isTablet) {
    return <MobileProfilePage userName={props?.userName || '--'} />;
  }
  return (
    <Box
      bg="fluentHealth.500"
      color="white"
      borderRadius="28px"
      marginTop="16px"
      minHeight="266px"
      w="100%"
    >
      <Flex>
        {/* Left: Icon and Name */}
        <Flex
          flexDir="column"
          align="center"
          justify="center"
          width="30%"
          borderRight={{ md: '1px solid white' }}
        >
          <Image
            src={ProfileIcon}
            width="92px"
            height="92px"
            mb="15px"
          />
          <Text fontSize={{ base: 'lg', md: '2xl' }}>{props?.userName || '--'}</Text>
        </Flex>

        {/* Right: Grid Details */}
        <ProfileBioCard />
      </Flex>
    </Box>
  );
}

export default ProfileCard;
