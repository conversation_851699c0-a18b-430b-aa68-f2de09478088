import React from 'react';
import { Box, Flex, Image, Text, Button } from '@chakra-ui/react';
import { useIsTablet } from '@components/ui/hooks/device.hook';

import { ProfileBioCard } from './ProfileBioCard';

import ProfileIcon from '@assets/icons/profile-icon.svg';

function MobileProfilePage(props: any) {
  return (
    <Box
      position="relative"
      bgColor="fluentHealth.500"
      bgImage="/mobile-background-profile-cover.png"
      bgPos="center"
      mt="5"
      borderRadius="4xl"
      w="100%"
      h="332px"
    >
      {/* Left: Icon and Name */}
      <Flex
        flexDir="column"
        align="center"
        justify="center"
        width="100%"
        height="100%"
        position="absolute"
        top="0"
        left="0"
      >
        <Image
          src={ProfileIcon}
          width="92px"
          height="92px"
          mb="15px"
        />
        <Text
          fontSize={{ base: 'lg', md: '2xl' }}
          color="white"
        >
          {props?.userName}
        </Text>
        <Button
          variant="outlined"
          border="1px solid"
          backgroundColor="linear-gradient(348deg, #BEF0E4 7.66%, #FFF2DF 90.11%)"
          borderColor="#E5F9F4"
          padding={'8px 40px'}
        >
          View health timeline
        </Button>
      </Flex>
    </Box>
  );
}

function ProfileCard(props: any) {
  const isTablet = useIsTablet();

  if (isTablet) {
    return <MobileProfilePage userName={props?.userName || '--'} />;
  }
  return (
    <Box
      bg="fluentHealth.500"
      color="white"
      borderRadius="28px"
      marginTop="16px"
      minHeight="266px"
      w="100%"
    >
      <Flex>
        {/* Left: Icon and Name */}
        <Flex
          flexDir="column"
          align="center"
          justify="center"
          width="30%"
          borderRight={{ md: '1px solid white' }}
        >
          <Image
            src={ProfileIcon}
            width="92px"
            height="92px"
            mb="15px"
          />
          <Text fontSize={{ base: 'lg', md: '2xl' }}>{props?.userName || '--'}</Text>
        </Flex>

        {/* Right: Grid Details */}
        <ProfileBioCard />
      </Flex>
    </Box>
  );
}

export default ProfileCard;
