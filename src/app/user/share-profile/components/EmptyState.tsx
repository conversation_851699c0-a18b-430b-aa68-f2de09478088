import React from 'react';
import { VStack, Text } from '@chakra-ui/react';

interface EmptyStateProps {
  message: string;
}

function EmptyState({ message }: EmptyStateProps) {
  return (
    <VStack
      bg="white"
      borderRadius="4px"
      p={6}
      spacing={4}
      boxShadow="sm"
    >
      <Text
        fontSize="20px"
        fontFamily="P22 Mackinac"
        color="charcoal.70"
      >
        {message}
      </Text>
    </VStack>
  );
}

export default EmptyState;
