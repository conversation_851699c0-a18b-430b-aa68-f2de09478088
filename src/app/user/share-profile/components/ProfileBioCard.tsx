// Package modules
import { PropsWithChildren } from 'react';
import { ChakraProps, Flex, Grid, GridItem, Heading, Skeleton } from '@chakra-ui/react';
// Local modules
import { ActiveCard } from '@src/types/profile';

import { evalConditions } from '@lib/utils/utils';
import { heightOptions } from '@lib/utils/observationsUtils';
import { ProfileBioCardItem } from '../../profile/components/ProfileBioCard';

export function ProfileBioCardSkeleton() {
  const skeletons = Array.from({ length: 6 }, (_, index) => (
    <Skeleton
      key={index}
      width="calc(50% - 1px)"
      height="62px"
      startColor="fluentHealthSecondary.300"
      endColor="fluentHealthSecondary.500"
    />
  ));

  return (
    <Flex
      wrap="wrap"
      gap="2px"
      bgColor="white"
      border="1px solid"
      borderColor="fluentHealthSecondary.300"
      boxShadow="0px 1px 4px rgba(73, 90, 228, 0.12)"
      px="4px"
      py="4px"
    >
      {skeletons}
    </Flex>
  );
}

function ProfileBioCardItemValue(props: PropsWithChildren<ChakraProps>) {
  return (
    <Heading
      fontSize={{ base: 'md', md: 'xl' }}
      lineHeight="short"
      color="white"
      {...props}
    />
  );
}

// eslint-disable-next-line complexity
export function ProfileBioCard() {
  return (
    <Grid
      width="70%"
      templateColumns="repeat(3, 1fr)"
      templateRows="repeat(3, 88px)"
    >
      <ProfileBioCardItem
        title="Height"
        // label={height ? heightObj.subValue : ''}
        borderBottom="1px solid"
        borderRight="1px solid"
        options={heightOptions}
        // defaultValue={heightObj.value.split(' ')[0]}
        // unit={heightObj.unit}
        cardType={ActiveCard.Height}
        // isInputField={enumUnit.CM}
      >
        {evalConditions([
          [
            'OR',
            <Flex
              direction="row"
              paddingRight="6"
              height="14"
              justifyContent="space-between"
            >
              <Heading
                fontSize={{ base: 'md', md: 'xl' }}
                lineHeight="short"
                color="white"
              >
                {/* {`${height ? heightObj.value : ''}`} */}
              </Heading>
            </Flex>,
          ],
          // ['AND', isPublicMode, !height, <ProfileBioCardItemNoneValue />],
        ])}
      </ProfileBioCardItem>
      <ProfileBioCardItem
        title="Weight"
        // label={weight ? weightObj.subValue : ''}
        borderBottom="1px solid"
        borderRight="1px solid"
        // defaultValue={weightObj.value.split(' ')[0]}
        isInputField
        // unit={weightObj.unit}
        showClearButton
        cardType={ActiveCard.Weight}
      >
        {evalConditions([
          [
            'OR',
            <Flex
              direction="row"
              paddingRight="6"
              height="14"
              justifyContent="space-between"
            >
              <Heading
                fontSize={{ base: 'md', md: 'xl' }}
                lineHeight="short"
                color="white"
              >
                {/* {weight ? weightObj.value : ''} */}
              </Heading>
            </Flex>,
          ],
        ])}
      </ProfileBioCardItem>
      <ProfileBioCardItem
        title="Age"
        // label={patient?.birthDate ? dayjs(patient.birthDate).format('DD/MM/YYYY') : undefined}
        borderBottom="1px solid"
      >
        {evalConditions([
          ['OR', <ProfileBioCardItemValue>{/* {getAge(patient?.birthDate)} */}</ProfileBioCardItemValue>],
        ])}
      </ProfileBioCardItem>
      <ProfileBioCardItem
        title="Blood group"
        borderBottom="1px solid"
        borderRight="1px solid"
        // options={bloodGroup.map((group) => group.label).filter((label): label is string => typeof label === 'string')}
        // defaultValue={bloodType || ''}
        cardType={ActiveCard.BloodType}
      >
        {evalConditions([
          [
            'OR',
            <Flex
              direction="row"
              paddingRight="6"
              height="14"
              justifyContent="space-between"
            >
              {/* <ProfileBioCardItemValue textTransform="uppercase">{bloodType}</ProfileBioCardItemValue> */}
            </Flex>,
          ],
        ])}
      </ProfileBioCardItem>
      <ProfileBioCardItem
        title="Gender"
        borderRight="1px solid"
        borderBottom="1px solid"
        // options={genderIdentityOptions}
        // defaultValue={genderIdentity || ''}
        cardType={ActiveCard.Gender}
      >
        {evalConditions([
          [
            'OR',
            <Flex
              direction="row"
              paddingRight="6"
              height="14"
              justifyContent="space-between"
            >
              <ProfileBioCardItemValue
                isTruncated
                maxWidth="120px"
                textTransform="capitalize"
              >
                {/* {genderIdentity} */}
              </ProfileBioCardItemValue>
            </Flex>,
          ],
        ])}
      </ProfileBioCardItem>
      <ProfileBioCardItem
        title="Sex assigned at birth"
        borderBottom="1px solid"
        // options={sexAssignedAtBirthValueSet.map((item) => item.label)}
        // defaultValue={patientGenderAtBirth}
        cardType={ActiveCard.PatientGenderAtBirth}
      >
        {evalConditions([
          [
            'OR',
            <Flex
              direction="row"
              paddingRight="6"
              height="14"
              justifyContent="space-between"
            >
              <ProfileBioCardItemValue
                isTruncated
                maxWidth="120px"
              >
                {/* {patientGenderAtBirth} */}
              </ProfileBioCardItemValue>
            </Flex>,
          ],
        ])}
      </ProfileBioCardItem>
      <GridItem colSpan={3}>
        <ProfileBioCardItem
          height="88px"
          title="Ethnicity"
          // options={ethnicityGrp
          //   .map((group) => group.label)
          //   .filter((label): label is string => typeof label === 'string')}
          // defaultValue={
          //   typeof ethnicity === 'string' ? ethnicity : ethnicity !== undefined ? String(ethnicity) : undefined
          // }
          cardType={ActiveCard.Ethnicity}
        >
          {evalConditions([
            [
              'OR',
              <Flex
                direction="row"
                paddingRight="6"
                height="14"
                justifyContent="space-between"
              >
                {/* <ProfileBioCardItemValue isTruncated>{ethnicity}</ProfileBioCardItemValue> */}
              </Flex>,
            ],
          ])}
        </ProfileBioCardItem>
      </GridItem>
    </Grid>
  );
}
