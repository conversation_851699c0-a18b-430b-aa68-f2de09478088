import React from 'react';
import { Flex, Text } from '@chakra-ui/react';

import EmptyState from './EmptyState';

export default function BasicInfo() {
  return (
    <Flex direction="column">
      <Text
        fontSize="13"
        textTransform="uppercase"
        fontWeight="medium"
        letterSpacing="1.56px"
        lineHeight="16px"
      >
        ADDITIONAL DETAILS
      </Text>
      <Text
        color="iris.500"
        fontSize="13"
        fontWeight="medium"
        letterSpacing="1.56px"
        lineHeight="16px"
        mt="20px"
      >
        Emergency Contacts
      </Text>
      <EmptyState
        message="Nothing here yet"
        margin="16px 0px 0px 0px"
      />
      <Text
        fontSize="13"
        fontWeight="medium"
        color="iris.500"
        letterSpacing="1.56px"
        lineHeight="16px"
        mt="20px"
      >
        Alternative Medical Decision-Maker
      </Text>
      <EmptyState
        message="Nothing here yet"
        margin="16px 0px 0px 0px"
      />
      <Text
        color="iris.500"
        fontSize="13"
        fontWeight="medium"
        letterSpacing="1.56px"
        lineHeight="16px"
        mt="20px"
      >
        Health insurance
      </Text>
      <EmptyState
        message="Nothing here yet"
        margin="16px 0px 0px 0px"
      />
    </Flex>
  );
}
