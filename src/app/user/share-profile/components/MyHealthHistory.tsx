import React from 'react';
import { Card, Flex, Text, VStack } from '@chakra-ui/react';
import { MY_HEALTH_PROFILE_MAP_SHARE } from '@user/lib/constants';
import { useParams } from 'react-router-dom';

import EmptyState from './EmptyState';
import { DrawerSection } from '../../profile/components/ProfileSidebarCTAs';

export default function MyHealthHistory() {
  const params = useParams();
  const { ehrId, subEhrId = '', action } = params as any;

  return (
    <Flex direction="column">
      <Text
        fontSize="13"
        textTransform="uppercase"
        fontWeight="medium"
        letterSpacing="1.56px"
        lineHeight="16px"
        mb="8px"
      >
        My Health History
      </Text>
      <Card
        bgColor="transparent"
        borderRadius="xl"
        boxShadow="0px"
        w="full"
      >
        <VStack alignItems="left">
          {Object.entries(MY_HEALTH_PROFILE_MAP_SHARE).map(([key, value]) => (
            <DrawerSection
              key={key}
              route={value.route}
              name={value.name}
              active={ehrId}
              subEhrId={subEhrId || ''}
              action={action}
            />
          ))}
        </VStack>
      </Card>
      <Text
        fontSize="13"
        textTransform="uppercase"
        fontWeight="medium"
        letterSpacing="1.56px"
        lineHeight="16px"
        mt="20px"
      >
        FAMILY MEMBERS
      </Text>

      <EmptyState
        message="No family members added"
        margin="16px 0px 0px 0px"
      />
    </Flex>
  );
}
