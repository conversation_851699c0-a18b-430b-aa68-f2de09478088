import React from 'react';
import { Box, Container, Grid, GridItem } from '@chakra-ui/react';
import { useIsTablet } from '@components/ui/hooks/device.hook';

import ProfileCard from './components/ProfileCard';
import HealthSnapshot from './components/HealthSnapshot';
import HealthTimeline from './components/HealthTimeline';
import ProfileTabs from './components/ProfileTabs';
import CardWrapper from './components/CardWrapper';

function MobileShareProfilePage() {
  return (
    <Box>
      <ProfileCard />
      <ProfileTabs />
    </Box>
  );
}

function ShareProfilePage() {
  const isTablet = useIsTablet();

  if (isTablet) {
    return <MobileShareProfilePage />;
  }
  return (
    <Box>
      <Container
        maxW="1104px"
        centerContent
      >
        <ProfileCard />
        <Grid
          templateColumns={{ base: '1fr', lg: 'repeat(3, 1fr)' }}
          gap={4}
          w="full"
        >
          <GridItem>
            <CardWrapper title="My Health Snapshot">
              <HealthSnapshot />
            </CardWrapper>
          </GridItem>
          <GridItem>
            <CardWrapper title="My Health Timeline">
              <HealthTimeline />
            </CardWrapper>
          </GridItem>
          <GridItem>
            <ProfileTabs />
          </GridItem>
        </Grid>
      </Container>
    </Box>
  );
}

export default ShareProfilePage;
