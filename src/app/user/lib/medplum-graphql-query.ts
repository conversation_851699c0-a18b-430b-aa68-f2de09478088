import { Patient, Questionnaire } from 'src/gql/graphql';
import {
  DOCUMENT_REFERENCE_QUERY_FRAGMENT,
  FHIR_CODE_SYSTEM_URL,
  FHIR_STRUCTURE_DEFINITION_URL,
  FHIR_VALUE_SET_URL,
  MEDPLUM_QUESTIONNAIRE,
} from '@lib/constants';
import { identifierUrn } from 'src/app/medical-records/lib/constants';

const patientInfo = {
  getPatient(patientId: Patient['id']) {
    const query = /* GraphQL */ `
    query Patient {
      Patient(id: "${patientId}") {
        id
        photo {
          id
          size
          url
        }
        gender
        birthDate
        meta {
          lastUpdated
        }
        link {
          type
          other {
            reference
            display
            resource {
              ... on RelatedPerson {
                id
                active
                relationship {
                  coding {
                    system
                    code
                    display
                  }
                }
                telecom {
                  system
                  use
                  value
                }
                name {
                  given
                  prefix
                  family
                }
                patient {
                  id
                  resource {
                    ... on Patient {
                      id
                      birthDate
                      deceasedBoolean
                      ethnicity: extension(
                        url: "${FHIR_STRUCTURE_DEFINITION_URL}/PatientEthnicity"
                      ) {
                          url
                          valueCode
                      }
                      bloodType: ObservationList(
                        code: "http://loinc.org|883-9"
                        category: "http://terminology.hl7.org/CodeSystem/observation-category|laboratory"
                        _reference: patient
                        _sort: "-date"
                        _count: 1
                      ) {
                        id
                        status
                        valueCodeableConcept {
                          text
                          coding {
                            code
                            display
                            system
                          }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
      extension {
        ...ExtensionFields
        extension {
            ...ExtensionFields
            extension {
                ...ExtensionFields
                extension {
                    ...ExtensionFields
                    extension {
                        ...ExtensionFields
                    }
                }
            }
        }
    }
        height: ObservationList(
          code: "http://loinc.org|8302-2"
          category: "http://terminology.hl7.org/CodeSystem/observation-category|vital-signs"
          _reference: patient
          _sort: "-date"
          _count: 1
        ) {
          status
          valueQuantity {
            value
            unit
          }
        }
        weight: ObservationList(
          code: "http://loinc.org|29463-7"
          category: "http://terminology.hl7.org/CodeSystem/observation-category|vital-signs"
          _reference: patient
          _sort: "-date"
          _count: 1
        ) {
          status
          valueQuantity {
            value
            unit
          }
        }
        reminders: CommunicationRequestList(_reference: patient, category: "reminder", _count:100) {
          id
          status
          occurrenceDateTime
        }
        ethnicity: extension(
          url: "${FHIR_STRUCTURE_DEFINITION_URL}/PatientEthnicity"
        ) {
            url
            valueCode
        }
        genderIdentity: extension(
          url: "${FHIR_STRUCTURE_DEFINITION_URL}/PatientGenderIdentity"
        ) {
            url
            valueCode
        }
        bloodType: ObservationList(
          code: "http://loinc.org|883-9"
          category: "http://terminology.hl7.org/CodeSystem/observation-category|laboratory"
          _reference: patient
          _sort: "-date"
          _count: 1
        ) {
          id
          status
          valueCodeableConcept {
            text
            coding {
              code
              display
              system
            }
          }
        }
        communication {
          preferred
          language {
            coding {
              code
              display
              system
            }
          }
        }
        generalPractitioner {
          reference
          id
        }
        address {
          id
          line
          postalCode
          state
          city
          country
          district
        }
        telecom {
          use
          system
          value
        }
        name {
          given
          family
        }
      }
    }

    fragment ExtensionFields on Extension {
      url
      valueCode
      valueString
      valueBoolean
      valueDateTime
      valueCoding {
          code
          display
          system
      }
  }
    `;
    return query;
  },
  getPatientSettingsAll(patientId: Patient['id']) {
    const query = /* GraphQL */ `
      query BasicList {
        BasicList(patient: "${patientId}") {
          id
          subject {
            reference
          }
          identifier {
            value
          }
          code {
            coding {
              system
              code
              display
            }
          }
          extension {
            url
            extension {
              url
              extension {
                url
                valueCoding {
                  code
                  display
                  system
                }
                valueString
                valueBoolean
                extension {
                  url
                  valueCoding {
                    code
                    display
                    system
                  }
                  valueString
                  valueBoolean
                  extension {
                    url
                    valueCoding {
                      code
                      display
                      system
                    }
                    valueString
                    valueBoolean
                  }
                }
              }
            }
          }
        }
      }
    `;
    return query;
  },
  recordsCompleted(patientId: any) {
    const query = `query recordsCompleted {
        TaskList(
          _filter: "status eq 'completed'"
          identifier: "${identifierUrn}"
          subject: "${patientId}"
          _sort: "-_lastUpdated"
          _count: 1000
        ) {
          id
          identifier {
            value
          }
          businessStatus {
            coding {
              code
              display
              system
            }
          }
          status
            focus {
            reference
            resource {
              ... on DocumentReference {
                id
              }
            }
          }
      }
    }`;
    return query;
  },
};
const valueSet = {
  getAllPatient() {
    const query = /* GraphQL */ `
      query ValueSetPatient {
        languagesValueSet: ValueSetList(url: "${FHIR_VALUE_SET_URL}/UserLanguages") {
          id
          compose {
            include {
              concept {
                code
                display
              }
            }
          }
        }
        ethnicityValueSet: ValueSetList(url: "${FHIR_VALUE_SET_URL}/FACT-eth") {
          id
          compose {
            include {
              concept {
                code
                display
              }
            }
          }
        }
        fmRelationshipValueSet: ValueSetList(url: "${FHIR_VALUE_SET_URL}/FamilyMember") {
          id
          compose {
            include {
              concept {
                code
                display
              }
            }
          }
        }
        fmhRelationshipValueSet: ValueSetList(url: "${FHIR_VALUE_SET_URL}/FamilyMemberHistory") {
          id
          compose {
            include {
              concept {
                code
                display
              }
            }
          }
        }
        fmhHealthStatus: ValueSetList(url: "${FHIR_VALUE_SET_URL}/healthstatus") {
          id
          compose {
            include {
              concept {
                code
                display
              }
            }
          }
        }
        hpRelationshipValueSet: ValueSetList(url: "${FHIR_VALUE_SET_URL}/HealthcareProxy") {
          id
          compose {
            include {
              concept {
                code
                display
              }
            }
          }
        }
        ecRelationshipValueSet: ValueSetList(url: "${FHIR_VALUE_SET_URL}/EmergencyContact") {
          id
          compose {
            include {
              concept {
                code
                display
              }
            }
          }
        }
        genderIdentityValueSet: ValueSetList(url: "${FHIR_VALUE_SET_URL}/FACT-gn") {
          id
          compose {
            include {
              concept {
                code
                display
              }
            }
          }
        }
        sexAssignedAtBirthValueSet: ValueSetList(url: "${FHIR_VALUE_SET_URL}/sex-assigned-at-birth") {
           id
          compose {
            include {
              concept {
                code
                display
              }
            }
          }
        }
      }
    `;
    return query;
  },
  getAllQuestionnaire(url: string) {
    const query = /* GraphQL */ `
      query ValueSet {
        ValueSetList(url: "${url}", _count: 1) {
          id
          compose {
            include {
              system
              concept {
                code
                display
              }
            }
          }
        }
      }
    `;
    return query;
  },
  getAllCareTeam() {
    const query = /* GraphQL */ `
      query ValueSetCareTeam {
        specialtyValueSet: ValueSetList(url: "${FHIR_VALUE_SET_URL}/FACT-sty") {
          id
          compose {
            include {
              concept {
                code
                display
              }
            }
          }
        }
        citiesINValueSet: ValueSetList(url: "${FHIR_VALUE_SET_URL}/Cities-IN") {
          id
          compose {
            include {
              concept {
                code
                display
              }
            }
          }
        }
      }
    `;
    return query;
  },
};
// countriesValueSet: ValueSetList(url: "${FHIR_VALUE_SET_URL}/Countries") {
//   id
//   compose {
//     include {
//       concept {
//         code
//         display
//       }
//     }
//   }
// }
// citiesBDValueSet: ValueSetList(url: "${FHIR_VALUE_SET_URL}/Cities-BD") {
//   id
//   compose {
//     include {
//       concept {
//         code
//         display
//       }
//     }
//   }
// }
const emergencyContact = {
  getAll(patientId: Patient['id']) {
    const query = /* GraphQL */ `
      query EmergencyContact {
        RelatedPersonList(patient: "${patientId}", active: "true", relationship: "http://terminology.hl7.org/CodeSystem/v2-0131|C" _filter: "_tag ne ${FHIR_CODE_SYSTEM_URL}/FluentHealthUI|delete") {
          id
          active
          relationship {
            coding {
              system
              code
              display
            }
          }
          telecom {
            system
            use
            value
          }
          name {
            given
            prefix
            family
          }
          meta {
            lastUpdated
            tag {
              system
              code
              display
            }
          }
        }
      }
    `;
    return query;
  },
  createContact() {
    const query = /* GraphQL */ `
      mutation {
        RelatedPersonCreate(
          res: { name: { given: "dsshds" }, patient: { id: "b92cfbe5-0a18-42db-bba3-0ceaca8e81ef" } }
        ) {
          id
        }
      }
    `;
    return query;
  },
  updateContact() {
    const query = /* GraphQL */ `
      mutation {
        RelatedPersonUpdate(
          id: "cabdea0d-3983-4087-bd9f-0f419e55dba2"
          res: { patient: { id: "b92cfbe5-0a18-42db-bba3-0ceaca8e81ef" }, name: { given: "Rohit" } }
        ) {
          id
        }
      }
    `;
    return query;
  },
};

const healthcareProxy = {
  getAll(patientId: Patient['id']) {
    const query = /* GraphQL */ `
      query HealthcareProxy {
        RelatedPersonList(patient: "${patientId}", active: "true", relationship: "http://terminology.hl7.org/CodeSystem/v3-RoleCode|HPOWATT" _filter: "_tag ne ${FHIR_CODE_SYSTEM_URL}/FluentHealthUI|delete") {
          id
          active
          relationship {
            coding {
              system
              code
              display
            }
          }
          telecom {
            system
            use
            value
          }
          name {
            given
            prefix
            family
          }
          file: DocumentReferenceList(_reference: related) {
            id
            resourceType
            author {
              id
              display
            }
            content {
              id
              attachment {
                title
                data
                url
                size
                id
              }
            }
          }
        }
      }
    `;
    return query;
  },
  createContact() {
    const query = /* GraphQL */ `
      mutation {
        RelatedPersonCreate(
          res: { name: { given: "dsshds" }, patient: { id: "b92cfbe5-0a18-42db-bba3-0ceaca8e81ef" } }
        ) {
          id
        }
      }
    `;
    return query;
  },
  updateContact() {
    const query = /* GraphQL */ `
      mutation {
        RelatedPersonUpdate(
          id: "cabdea0d-3983-4087-bd9f-0f419e55dba2"
          res: { patient: { id: "b92cfbe5-0a18-42db-bba3-0ceaca8e81ef" }, name: { given: "Rohit" } }
        ) {
          id
        }
      }
    `;
    return query;
  },
};

const healthInsurance = {
  getAll(url: Questionnaire['url'], patientId: Patient['id']) {
    const query = /* GraphQL */ `
      query HealthInsuranceList {
        QuestionnaireResponseList(questionnaire: "${url}", patient: "${patientId}", _sort: "-_lastUpdated" _filter: "_tag ne ${FHIR_CODE_SYSTEM_URL}/FluentHealthUI|delete") {
            id
            questionnaire
            item {
              id
              text
              linkId
              answer {
                id
                valueString
                valueDate
                    valueBoolean
                          valueCoding {
                            system
                            code
                            display
                          }
                valueReference {
                  reference
                  resource {
                    ... on DocumentReference {
                      id
                      status
                      subject {
                        reference
                      }
                      content {
                        attachment {
                            title
                            url
                            contentType
                            data
                            size
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      `;
    return query;
  },

  getAllOrganization() {
    const query = /* GraphQL */ `
      query ValueSets {
        ValueSetList(url: "${FHIR_VALUE_SET_URL}/HealthInsuranceCompanies") {
          id
          compose {
            include {
              system
              concept {
                code
                display
              }
            }
          }
        }
      }
    `;
    return query;
  },
};

const questionnaireList = {
  getAll(url: Questionnaire['url']) {
    const query = /* GraphQL */ `
    query QuestionnaireList{
      QuestionnaireList(url: "${url}", _count: 1) {
        id
        name
        title
        item {
          id
          text
          linkId
          type
          prefix
          answerValueSet
          answerOption {
            id
            valueString
            valueDate
            valueTime
            valueInteger
            valueReference {
              reference
            }
            valueCoding {
              code
              display
              system
            }
          }
        }
      }
    }
  `;
    return query;
  },
};

const questionnaireResponseList = {
  getAll(url: Questionnaire['url'], patientId: Patient['id']) {
    const query = /* GraphQL */ `
    query QuestionnaireResponseList {
      QuestionnaireResponseList(questionnaire: "${url}", patient: "${patientId}", _sort: "-_lastUpdated" _filter: "_tag ne ${FHIR_CODE_SYSTEM_URL}/FluentHealthUI|delete") {
        id
        questionnaire
        item {
          id
          text
          linkId
          answer {
            id
            valueString
            valueDate
            valueBoolean
            valueDecimal
            valueTime
            valueInteger
            valueCoding {
              system
              code
              display
            }
            valueReference {
              reference
              resource {
                ... on QuestionnaireResponse {
                  id
                  item {
                    id
                    text
                    linkId
                    answer {
                      id
                      valueString
                      valueDate
                      valueBoolean
                      valueTime
                      valueDecimal
                      valueInteger
                      valueCoding {
                        system
                        code
                        display
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
    `;
    return query;
  },
};

const masterObservationList = {
  getAll(url: string, patientId: string): string {
    return `
      query GetObservationList {
        ObservationList(
          subject: "Patient/${patientId}",
          identifier: "${url}",
          _sort: "-date",
           _filter: "_tag ne ${FHIR_CODE_SYSTEM_URL}/FluentHealthUI|delete"
        ) {
          resourceType
          id
          identifier { value system }
          status
          code { coding { system code display } }
          subject { reference }
          effectiveDateTime
          valueQuantity { value unit system code }
          method { coding { system code display } }
          component { code { coding { system code display } } valueQuantity { value unit system code } valueCodeableConcept { coding { system code display } } }
          referenceRange { low { value unit system code } high { value unit system code } }
        }
      }
    `;
  },
};

const questionnaireResponsePSList = {
  getAll(patientId: Patient['id']) {
    const query = /* GraphQL */ `
      query QuestionnaireResponseList {
        PSAnnualPhysical:  QuestionnaireResponseList(questionnaire: "${MEDPLUM_QUESTIONNAIRE}/PSAnnualPhysical", patient: "${patientId}", _sort: "-_lastUpdated", _count: 1) {
          id
          item {
            id
            text
            linkId
            answer {
              id
              valueString
              valueDate
              valueBoolean
              valueCoding {
                system
                code
                display
              }
            }
          }
        }
        PSEyeExamination:  QuestionnaireResponseList(questionnaire: "${MEDPLUM_QUESTIONNAIRE}/PSEyeExamination", patient: "${patientId}", _sort: "-_lastUpdated", _count: 1) {
          id
          item {
            id
            text
            linkId
            answer {
              id
              valueString
              valueDate
              valueBoolean
              valueCoding {
                system
                code
                display
              }
            }
          }
        }
        PSDentalCheckupCleaning:  QuestionnaireResponseList(questionnaire: "${MEDPLUM_QUESTIONNAIRE}/PSDentalCheckupCleaning", patient: "${patientId}", _sort: "-_lastUpdated", _count: 1) {
          id
          item {
            id
            text
            linkId
            answer {
              id
              valueString
              valueDate
              valueBoolean
              valueCoding {
                system
                code
                display
              }
            }
          }
        }
        PSSkinSelfExamination:  QuestionnaireResponseList(questionnaire: "${MEDPLUM_QUESTIONNAIRE}/PSSkinSelfExamination", patient: "${patientId}", _sort: "-_lastUpdated", _count: 1) {
          id
          item {
            id
            text
            linkId
            answer {
              id
              valueString
              valueDate
              valueBoolean
              valueCoding {
                system
                code
                display
              }
            }
          }
        }
        PSBreastSelfEvaluation:  QuestionnaireResponseList(questionnaire: "${MEDPLUM_QUESTIONNAIRE}/PSBreastSelfEvaluation", patient: "${patientId}", _sort: "-_lastUpdated", _count: 1) {
          id
          item {
            id
            text
            linkId
            answer {
              id
              valueString
              valueDate
              valueBoolean
              valueCoding {
                system
                code
                display
              }
            }
          }
        }
        PSTesticularSelfExamination:  QuestionnaireResponseList(questionnaire: "${MEDPLUM_QUESTIONNAIRE}/PSTesticularSelfExamination", patient: "${patientId}", _sort: "-_lastUpdated", _count: 1) {
          id
          item {
            id
            text
            linkId
            answer {
              id
              valueString
              valueDate
              valueBoolean
              valueCoding {
                system
                code
                display
              }
            }
          }
        }
        PSCompleteBloodCount:  QuestionnaireResponseList(questionnaire: "${MEDPLUM_QUESTIONNAIRE}/PSCompleteBloodCount", patient: "${patientId}", _sort: "-_lastUpdated", _count: 1) {
          id
          item {
            id
            text
            linkId
            answer {
              id
              valueString
              valueDate
              valueBoolean
              valueCoding {
                system
                code
                display
              }
            }
          }
        }
        PSThyroidTest:  QuestionnaireResponseList(questionnaire: "${MEDPLUM_QUESTIONNAIRE}/PSThyroidTest", patient: "${patientId}", _sort: "-_lastUpdated", _count: 1) {
          id
          item {
            id
            text
            linkId
            answer {
              id
              valueString
              valueDate
              valueBoolean
              valueCoding {
                system
                code
                display
              }
            }
          }
        }
        PSBloodSugarTest:  QuestionnaireResponseList(questionnaire: "${MEDPLUM_QUESTIONNAIRE}/PSBloodSugarTest", patient: "${patientId}", _sort: "-_lastUpdated", _count: 1) {
          id
          item {
            id
            text
            linkId
            answer {
              id
              valueString
              valueDate
              valueBoolean
              valueCoding {
                system
                code
                display
              }
            }
          }
        }
        PSLipidProfile:  QuestionnaireResponseList(questionnaire: "${MEDPLUM_QUESTIONNAIRE}/PSLipidProfile", patient: "${patientId}", _sort: "-_lastUpdated", _count: 1) {
          id
          item {
            id
            text
            linkId
            answer {
              id
              valueString
              valueDate
              valueBoolean
              valueCoding {
                system
                code
                display
              }
            }
          }
        }
        PSAllergyTest:  QuestionnaireResponseList(questionnaire: "${MEDPLUM_QUESTIONNAIRE}/PSAllergyTest", patient: "${patientId}", _sort: "-_lastUpdated", _count: 1) {
          id
          item {
            id
            text
            linkId
            answer {
              id
              valueString
              valueDate
              valueBoolean
              valueCoding {
                system
                code
                display
              }
            }
          }
        }
        PSProstateSpecificAntigenPSA:  QuestionnaireResponseList(questionnaire: "${MEDPLUM_QUESTIONNAIRE}/PSProstateSpecificAntigenPSA", patient: "${patientId}", _sort: "-_lastUpdated", _count: 1) {
          id
          item {
            id
            text
            linkId
            answer {
              id
              valueString
              valueDate
              valueBoolean
              valueCoding {
                system
                code
                display
              }
            }
          }
        }
        PSPapSmear:  QuestionnaireResponseList(questionnaire: "${MEDPLUM_QUESTIONNAIRE}/PSPapSmear", patient: "${patientId}", _sort: "-_lastUpdated", _count: 1) {
          id
          item {
            id
            text
            linkId
            answer {
              id
              valueString
              valueDate
              valueBoolean
              valueCoding {
                system
                code
                display
              }
            }
          }
        }
        PSMammogram:  QuestionnaireResponseList(questionnaire: "${MEDPLUM_QUESTIONNAIRE}/PSMammogram", patient: "${patientId}", _sort: "-_lastUpdated", _count: 1) {
          id
          item {
            id
            text
            linkId
            answer {
              id
              valueString
              valueDate
              valueBoolean
              valueCoding {
                system
                code
                display
              }
            }
          }
        }
        PSElectrocardiogramECG:  QuestionnaireResponseList(questionnaire: "${MEDPLUM_QUESTIONNAIRE}/PSElectrocardiogramECG", patient: "${patientId}", _sort: "-_lastUpdated", _count: 1) {
          id
          item {
            id
            text
            linkId
            answer {
              id
              valueString
              valueDate
              valueBoolean
              valueCoding {
                system
                code
                display
              }
            }
          }
        }
        PS2DEchocardiogram:  QuestionnaireResponseList(questionnaire: "${MEDPLUM_QUESTIONNAIRE}/PS2DEchocardiogram", patient: "${patientId}", _sort: "-_lastUpdated", _count: 1) {
          id
          item {
            id
            text
            linkId
            answer {
              id
              valueString
              valueDate
              valueBoolean
              valueCoding {
                system
                code
                display
              }
            }
          }
        }
        PSTreadmillTestTMT:  QuestionnaireResponseList(questionnaire: "${MEDPLUM_QUESTIONNAIRE}/PSTreadmillTestTMT", patient: "${patientId}", _sort: "-_lastUpdated", _count: 1) {
          id
          item {
            id
            text
            linkId
            answer {
              id
              valueString
              valueDate
              valueBoolean
              valueCoding {
                system
                code
                display
              }
            }
          }
        }
        PSColonoscopy:  QuestionnaireResponseList(questionnaire: "${MEDPLUM_QUESTIONNAIRE}/PSColonoscopy", patient: "${patientId}", _sort: "-_lastUpdated", _count: 1) {
          id
          item {
            id
            text
            linkId
            answer {
              id
              valueString
              valueDate
              valueBoolean
              valueCoding {
                system
                code
                display
              }
            }
          }
        }
        PSSexuallyTransmittedInfectionsSTI:  QuestionnaireResponseList(questionnaire: "${MEDPLUM_QUESTIONNAIRE}/PSSexuallyTransmittedInfectionsSTI", patient: "${patientId}", _sort: "-_lastUpdated", _count: 1) {
          id
          item {
            id
            text
            linkId
            answer {
              id
              valueString
              valueDate
              valueBoolean
              valueCoding {
                system
                code
                display
              }
            }
          }
        }
      }
    `;
    return query;
  },
};
const careTeam = {
  getAll(patientId: Patient['id']) {
    const query = /* GraphQL */ `
      query CareTeamList {
        CareTeamList(patient: "${patientId}" _filter: "_tag ne ${FHIR_CODE_SYSTEM_URL}/FluentHealthUI|delete") {
          id
          managingOrganization {
            id
            reference
            display
          }
          name
          telecom {
            system
            value
            use
            rank
          }
          participant {
            member {
              reference
              resource {
                ... on Practitioner {
                  id
                  telecom {
                    value
                    system
                    use
                    rank
                  }
                  name {
                    given
                    family
                  }
                  address {
                    city
                    country
                  }
                  practitionerRole: PractitionerRoleList(_reference: practitioner) {
                    id
                    code {
                      coding {
                        system
                        code
                        display
                      }
                    }
                    specialty {
                      coding {
                        code
                        display
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    `;
    return query;
  },
};

const doctorList = {
  getAll(name: string) {
    const query = /* GraphQL */ `
      query PractitionerList {
        PractitionerList${name && name?.length ? `(name: "${name}")` : ''} {
          id
          name {
            prefix
            given
            family
          }
          telecom {
            use
            system
            value
          }
          address {
            city
            country
          }
          practitionerRole: PractitionerRoleList(_reference: practitioner) {
            id
            specialty {
              coding {
                display
              }
            }
          }
        }
      }
    `;
    return query;
  },
};
// const medication = {
//   getAll(patientId: Patient['id']) {
//     const query = /* GraphQL */ ``;
//     // const query = /* GraphQL */ `
//     // query MedicationStatementList {
//     //   MedicationStatementList(patient: "${patientId}") {
//     //     id
//     //     category {
//     //       coding {
//     //         code
//     //         display
//     //       }
//     //     }
//     //     dateAsserted
//     //     dosage {
//     //       timing {
//     //         repeat {
//     //           duration
//     //           dayOfWeek
//     //           durationUnit
//     //         }
//     //       }
//     //       doseAndRate {
//     //         doseRange {
//     //           low {
//     //             value
//     //             code
//     //           }
//     //           high {
//     //             value
//     //             code
//     //           }
//     //         }
//     //         doseQuantity {
//     //           unit
//     //           value
//     //         }
//     //       }
//     //     }
//     //     medicationReference {
//     //       resource {
//     //         ... on Medication {
//     //           manufacturer {
//     //             reference
//     //           }
//     //           code {
//     //             coding {
//     //               code
//     //               system
//     //             }
//     //             text
//     //           }
//     //         }
//     //       }
//     //       reference
//     //     }
//     //     file: DocumentReferenceList(_reference: related) {
//     //       resourceType
//     //       author {
//     //         id
//     //         display
//     //       }
//     //       content {
//     //         id
//     //         attachment {
//     //           title
//     //           data
//     //           url
//     //         }
//     //       }
//     //     }
//     //   }
//     // }`;
//     return query;
//   },
// };
// const supplement = {
//   getAll(patientId: Patient['id']) {
//     const query = /* GraphQL */ ``;
//     // const query = /* GraphQL */ `
//     //   query MedicationStatementList {
//     //     MedicationStatementList(patient: "${patientId}") {
//     //       id
//     //       category {
//     //         coding {
//     //           code
//     //           display
//     //         }
//     //       }
//     //       dateAsserted
//     //       dosage {
//     //         timing {
//     //           repeat {
//     //             duration
//     //             dayOfWeek
//     //             durationUnit
//     //           }
//     //         }
//     //         doseAndRate {
//     //           doseRange {
//     //             low {
//     //               value
//     //               code
//     //             }
//     //             high {
//     //               value
//     //               code
//     //             }
//     //           }
//     //           doseQuantity {
//     //             unit
//     //             value
//     //           }
//     //         }
//     //       }
//     //       medicationReference {
//     //         resource {
//     //           ... on Medication {
//     //             manufacturer {
//     //               reference
//     //             }
//     //             code {
//     //               coding {
//     //                 code
//     //                 system
//     //               }
//     //               text
//     //             }
//     //           }
//     //         }
//     //         reference
//     //       }
//     //       file: DocumentReferenceList(_reference: related) {
//     //         resourceType
//     //         author {
//     //           id
//     //           display
//     //         }
//     //         content {
//     //           id
//     //           attachment {
//     //             title
//     //             data
//     //             url
//     //           }
//     //         }
//     //       }
//     //     }
//     //   }
//     // `;
//     return query;
//   },
// };
const condition = {
  getAll(patientId: Patient['id']) {
    const query = /* GraphQL */ `
      query ConditionList {
        ConditionList(
          patient: "${patientId}",
          identifier: "cn",
          _sort: "-_lastUpdated",
          _filter: "_tag ne ${FHIR_CODE_SYSTEM_URL}/FluentHealthUI|delete"
        ) {
          id
          identifier { value system }
          code { coding { display code system } }
          onsetPeriod { start end }
          meta { lastUpdated tag { system code display } }
          note { text }
          extension { valueCodeableConcept { coding { system code display } } }
          clinicalStatus { coding { system code display } }
          evidence {
            detail {
              reference
              ${DOCUMENT_REFERENCE_QUERY_FRAGMENT}
            }
          }
        }
      }
    `;
    return query;
  },
};
// const surgery = {
//   getAll(patientId: Patient['id']) {
//     const query = /* GraphQL */ ``;
//     // const query = /* GraphQL */ `
//     //   query ProcedureList {
//     //     ProcedureList(patient: "${patientId}") {
//     //       id
//     //       code {
//     //         coding {
//     //           code
//     //           display
//     //         }
//     //       }
//     //       performedDateTime
//     //       file: DocumentReferenceList(_reference: related) {
//     //         resourceType
//     //         author {
//     //           id
//     //           display
//     //         }
//     //         content {
//     //           id
//     //           attachment {
//     //             title
//     //             data
//     //             url
//     //           }
//     //         }
//     //       }
//     //     }
//     //   }
//     // `;
//     return query;
//   },
// };
// const allergy = {
//   getAll(patientId: Patient['id']) {
//     const query = /* GraphQL */ ``;
//     // const query = /* GraphQL */ `
//     //   query AllergyIntoleranceList {
//     //     AllergyIntoleranceList(patient: "${patientId}") {
//     //       id
//     //       type
//     //       recordedDate
//     //       criticality
//     //       category
//     //       code {
//     //         coding {
//     //           display
//     //           code
//     //         }
//     //       }
//     //     }
//     //   }
//     // `;
//     return query;
//   },
// };
// const screening = {
//   getAll(patientId: Patient['id']) {
//     const query = /* GraphQL */ ``;
//     // const query = /* GraphQL */ `
//     //   query ObservationList {
//     //     ObservationList(patient: "${patientId}", category: "FACT-ps") {
//     //       id
//     //       status
//     //       category {
//     //         coding {
//     //           display
//     //           code
//     //         }
//     //       }
//     //       code {
//     //         coding {
//     //           display
//     //           code
//     //         }
//     //         id
//     //       }
//     //       subject {
//     //         display
//     //       }
//     //       effectivePeriod {
//     //         end
//     //         start
//     //       }
//     //       component {
//     //         code {
//     //           coding {
//     //             display
//     //             code
//     //           }
//     //         }
//     //         valueString
//     //         valueQuantity {
//     //           value
//     //           unit
//     //         }
//     //       }
//     //     }
//     //   }
//     // `;
//     return query;
//   },
// };

// const immunization = {
//   getAll(patientId: Patient['id']) {
//     const query = /* GraphQL */ ``;
//     // const query = /* GraphQL */ `
//     //   query ImmunizationList {
//     //     ImmunizationList(patient: "${patientId}") {
//     //       id
//     //       occurrenceString
//     //       occurrenceDateTime
//     //       vaccineCode {
//     //         coding {
//     //           display
//     //           code
//     //         }
//     //       }
//     //       recorded
//     //       note {
//     //         text
//     //       }
//     //       file: DocumentReferenceList(_reference: related) {
//     //         resourceType
//     //         author {
//     //           id
//     //           display
//     //         }
//     //         content {
//     //           id
//     //           attachment {
//     //             title
//     //             data
//     //             url
//     //           }
//     //         }
//     //       }
//     //     }
//     //   }
//     // `;
//     return query;
//   },
// };
// const patientSymptoms = {
//   getAll(patientId: Patient['id']) {
//     const query = /* GraphQL */ ``;
//     // const query = /* GraphQL */ `
//     //   query ObservationList {
//     //     ObservationList(patient: "${patientId}", category: "FACT-sym") {
//     //       id
//     //       status
//     //       category {
//     //         coding {
//     //           display
//     //           code
//     //         }
//     //       }
//     //       code {
//     //         coding {
//     //           display
//     //           code
//     //         }
//     //         id
//     //       }
//     //       subject {
//     //         display
//     //       }
//     //       effectivePeriod {
//     //         end
//     //         start
//     //       }
//     //       component {
//     //         code {
//     //           coding {
//     //             display
//     //             code
//     //           }
//     //         }
//     //         valueString
//     //         valueQuantity {
//     //           value
//     //           unit
//     //         }
//     //       }
//     //     }
//     //   }
//     // `;
//     return query;
//   },
// };
// const patientVital = {
//   getAll(patientId: Patient['id']) {
//     const query = /* GraphQL */ ``;
//     // const query = /* GraphQL */ `
//     //   query ObservationList {
//     //     ObservationList(patient: "${patientId}", category: "vital-signs") {
//     //       id
//     //       status
//     //       category {
//     //         coding {
//     //           display
//     //           code
//     //         }
//     //       }
//     //       code {
//     //         coding {
//     //           display
//     //           code
//     //         }
//     //         id
//     //       }
//     //       subject {
//     //         display
//     //       }
//     //       effectivePeriod {
//     //         end
//     //         start
//     //       }
//     //       component {
//     //         code {
//     //           coding {
//     //             display
//     //             code
//     //           }
//     //         }
//     //         valueString
//     //         valueQuantity {
//     //           value
//     //           unit
//     //         }
//     //       }
//     //     }
//     //   }
//     // `;
//     return query;
//   },
// };

const shareProfileRecord = {
  getShareProfileURL(entryId: string) {
    const query = /* GraphQL */ `
      query {
        Consent(id: "${entryId}") {
          id
          identifier {
            system
          }
        }
      }
    `;
    return query;
  },
  getShareRecordURL(entryId: string) {
    const query = /* GraphQL */ `
      query task {
        TaskList(_id: "${entryId}") {
          id
          output {
            valueString
          }
        }
      }
    `;
    return query;
  },
  getShareProfileData(entryId: string) {
    const query = /* GraphQL */ `
    query composition {
      CompositionList(_id: "${entryId}") {
        id
        section {
          title
          entry {
            display
            reference
            resource {
              ... on Bundle {
                id
                entry {
                    resource {
                        ... on DocumentReference {
                            id
                            content {
                                attachment {
                                    contentType
                                    data
                                }
                            }
                        }
                    }
                    fullUrl
                }
              }
              ... on Consent {
                id
                provision{
                    type
                    period{
                        start
                        end
                    }
                    data{
                        meaning
                        reference{
                            reference
                        }
                    }
                }
              }
            }
          }
        }
      }
    }
    `;

    return query;
  },
  getShareRecordData(entryId: string) {
    const query = /* GraphQL */ `
    query composition {
      Composition(id: "${entryId}") {
        resourceType
        id
        status
        title
        section {
          title
          entry {
            display
            reference
            resource {
              ... on DocumentReference {
                id
                type {
                  coding {
                    display
                    code
                    system
                  }
                }
                extension {
                  url
                  valueString
                }
                description
                status
                docStatus
                subject {
                  reference
                  resource {
                    ... on Patient {
                      id
                      name {
                        family
                        prefix
                        given
                        use
                      }
                    }
                    ... on Practitioner {
                      id
                      name {
                        family
                        prefix
                        given
                        use
                      }
                    }
                  }
                }
                content {
                  attachment {
                    url
                    size
                    title
                    creation
                    contentType
                  }
                }
                context {
                  period {
                    start
                    end
                  }
                  encounter {
                    reference
                    resource {
                      ... on Encounter {
                        id
                        class {
                          code
                          display
                          system
                        }
                      }
                    }
                  }
                  related {
                    reference
                    resource {
                      ... on DiagnosticReport {
                        __typename
                        id
                        status
                        code {
                          coding {
                            code
                            display
                            system
                          }
                        }
                        subject {
                          reference
                        }
                        issued
                        performer {
                          reference
                        }
                        result {
                          reference
                          resource {
                            ... on Observation {
                              id
                              status
                              code {
                                coding {
                                  code
                                  display
                                  system
                                }
                              }
                              subject {
                                reference
                              }
                              component {
                                code {
                                  coding {
                                    code
                                    system
                                    display
                                  }
                                }
                              }
                              valueString
                              valueQuantity {
                                value
                                unit
                                system
                                code
                              }
                              referenceRange {
                                low {
                                  value
                                  unit
                                }
                                high {
                                  value
                                  unit
                                }
                              }
                            }
                          }
                        }
                      }
                      ... on Practitioner {
                        __typename
                        id
                        name {
                          text
                          family
                          given
                          prefix
                        }
                      }
                      ... on PractitionerRole {
                        __typename
                        id
                        specialty {
                          coding {
                            code
                            display
                            system
                          }
                        }
                      }
                      ... on Condition {
                        __typename
                        id
                        conditionCode: code {
                          coding {
                            display
                            code
                            system
                          }
                        }
                      }
                      ... on Organization {
                        __typename
                        id
                        hospitalName: name
                      }
                    }
                  }
                }
                meta {
                  tag {
                    code
                    display
                    system
                  }
                }
              }
              ... on Patient {
                id
                name {
                  family
                  given
                }
                gender
                birthDate
              }
              ... on Consent {
                id
                provision {
                  type
                  period {
                    start
                    end
                  }
                  data {
                    meaning
                    reference {
                      reference
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
    `;

    return query;
  },
};
// Todo download api
// const downloadProfile = {
//   getDownloadProfileData(entryId) {
//     const query = {};
//     return query;
//   },
// };

const familyMemberHistory = {
  getFamilyMemberData(patientId: Patient['id']) {
    const query = `query FamilyMemberHistory {
      FamilyMemberHistoryList(patient: "${patientId}", _sort: "-date"  _filter: "_tag ne ${FHIR_CODE_SYSTEM_URL}/FluentHealthUI|delete") {
        id
        status
        patient {
          reference
        }
        name
        relationship {
          coding {
            code
            display
            system
          }
        }
        deceasedBoolean
        bornDate
        extension: extension {
          url
          valueCode
        }
        condition {
          code { coding { system code display } }
          onsetPeriod { start end },
          note { text }
          extension {
            url valueCodeableConcept { coding { system code display } }
            valueReference { reference ${DOCUMENT_REFERENCE_QUERY_FRAGMENT}}
          }
        }
      }
    }`;
    return query;
  },
};

const auditEventList = {
  getAll(agent: string, type: string) {
    const query = `
    query AuditEvent {
      AuditEventList(agent: "Patient/${agent}", type: "${type}", _count: 1000, _sort: "-date") {
        id
        type {
          display
          system
          code
        }
        subtype {
          display
          system
          code
        }
        recorded
        agent {
          type {
            coding {
              code
              display
              system
            }
          }
          who {
            reference
            resource {
              ... on Patient {
                id
                name {
                  given
                  family
                  prefix
                  suffix
                  use
                }
              }
              ... on Practitioner {
                id
                name {
                  given
                  family
                  prefix
                  suffix
                  use
                }
              }
            }
          }
        }
        entity {
          what {
            reference
            resource {
              ... on Observation {
                id
                code{
                  coding {
                    code
                    display
                    system
                  }
                }
              }
              ... on Patient {
                id
                extension {
                  url
                  valueString
                }
              }
            }
          }
          type {
            code
            display
            system
          }
          role  {
            code
            display
            system
          }
          name
          description
          detail {
            type
            valueString
          }
        }
      }
    }
    `;
    return query;
  },
};
const getPreventativeScreeningList = {
  getPreventativeScreening(patientId: string, identifiers: string[]) {
    if (!patientId || identifiers.length === 0) {
      throw new Error('Invalid input: patientId and identifiers are required.');
    }
    const query = `
      query ProcedureList {
        ${identifiers
          .map(
            (id) => `
            ${id.replace(/[^a-zA-Z0-9_]/g, '')}: ProcedureList(
              subject: "${patientId}",
              identifier: "${id}"
              _filter: "_tag ne ${FHIR_CODE_SYSTEM_URL}/FluentHealthUI|delete"
            ) {
              resourceType
              id
              identifier {
                value
              }
              status
              code {
                coding {
                  system
                  code
                  display
                }
              }
              subject {
                reference
              }
              recorder {
                reference
              }
              note {
                text
              }
              report {
                reference
                  ${DOCUMENT_REFERENCE_QUERY_FRAGMENT}
              }
              performedDateTime
            }`
          )
          .join('\n')}
      }
    `;

    return query;
  },
};
const getProcedureList = {
  getAll(patientId: Patient['id']) {
    const query = /* GraphQL */ `
      query Practitioner {
        ProcedureList(subject: "Patient/${patientId}", _sort: "-date") {
          resourceType
          id
          identifier{
            system
            value
          }
          status
          code {
            coding {
              system
              code
              display
            }
            text
          }
          subject {
            reference
          }
          performedPeriod { start end }
          note{ text }
          report {
            reference
            ${DOCUMENT_REFERENCE_QUERY_FRAGMENT}
          }
        }
      }`;
    return query;
  },
};
const deleteProfile = {
  getTaskCountByIdentifier(patient: string, identifier: string) {
    const query = /* GraphQL */ `
      query {
        TaskList(patient: "${patient}", identifier:"${identifier}") {
          id
          identifier {
          value
          }
        }
      }
    `;
    return query;
  },
};

const getSymptomsObservationList = {
  getSymptomsObservation(patientId: Patient['id'], identifier: string) {
    const query = `query{
      ObservationList(subject: "${patientId}", identifier: "${identifier}", _sort: "-_lastUpdated"  _filter: "_tag ne https://fluentinhealth.com/FHIR/CodeSystem/FluentHealthUI|delete") {
      resourceType
        id
        status
        identifier {
          system
          value
        }
        code {
          coding {
            system
            code
            display
          }
          text
        }
        subject {
          reference
        }
        effectivePeriod {
          start
          end
        }
        note{ text}
        derivedFrom {
          reference
          ${DOCUMENT_REFERENCE_QUERY_FRAGMENT}
        }
        extension {
          url
            valueCodeableConcept {
              coding {
                system
                code
                display
              }
            }
          }
        }
      }`;
    return query;
  },
};

const getAllergiesIntolerancesList = {
  getAllergiesIntolerances(patientId: Patient['id']) {
    const query = `query{
        AllergyIntoleranceList(patient: "${patientId}", _sort: "-date") {
          resourceType
          id
          clinicalStatus {
            coding {
              system
              code
              display
            }
          }
          code {
            coding {
              system
              code
              display
            }
          }
          identifier {
            system
            value
          }
          category
          criticality
          onsetDateTime
          lastOccurrence
          note{text}
          patient {
            reference
            display
          }
          extension{
            valueReference{
              reference
               ${DOCUMENT_REFERENCE_QUERY_FRAGMENT}
            }
          }
        }
      }`;
    return query;
  },
};

export const valueSetsForProfile = {
  getValuesetsForProfile(urls: { url: string; type: string }[]) {
    const query = /* GraphQL */ `
      query ValueSetPatient {
        ${urls
          .map(
            (set) => `
          ${set.type}: ValueSetList(url: "${set.url}") {
            id
            compose {
              include {
                concept {
                  code
                  display
                }
              }
            }
          }`
          )
          .join('\n')}
      }
    `;
    return query;
  },
};
const getImmunizationList = {
  getAll(patientId: Patient['id']) {
    const query = /* GraphQL */ `
      query ImmunizationList {
        ImmunizationList(patient: "Patient/${patientId}", _count: 1000, _filter: "_tag ne DELETE") {
          resourceType
          id
          identifier {
            system
            value
          }
          status
          vaccineCode {
            coding {
              system
              code
              display
            }
            text
          }
          patient {
            reference
          }
          extension{
            valueReference{
              reference
               ${DOCUMENT_REFERENCE_QUERY_FRAGMENT}
            }
          }
          occurrenceDateTime
          encounter {
            reference
          }
          protocolApplied {
            doseNumberPositiveInt
            doseNumberString
          }
          reportOrigin {
            coding {
              system
              code
              display
            }
          }
        }
      }`;

    return query;
  },
};
const getMedicationsList = {
  getAll(patientId: Patient['id']) {
    const query = /* GraphQL */ `
      query medicationStatementList {
        MedicationStatementList(
          subject: "Patient/${patientId}",
          _count: 1000,
          _filter: "_tag ne ${FHIR_CODE_SYSTEM_URL}/FluentHealthUI|delete"
        ) {
          id
          resourceType
          identifier {
            system
            value
          }
          status
          medicationCodeableConcept {
            coding {
              system
              code
              display
            }
          }
          subject {
            reference
          }
          effectivePeriod {
            start
            end
          }
          dosage {
            timing {
              repeat {
                boundsDuration {
                  value
                  unit
                }
                when
              }
            }
            doseAndRate {
              doseQuantity {
                value
              }
            }
            additionalInstruction {
              coding {
                system
                code
                display
              }
            }
            route {
              coding {
                system
                code
                display
              }
            }
          }
          extension {
            url
            valueBoolean
            valueCodeableConcept {
              coding {
                system
                code
                display
              }
            }
          }
          derivedFrom {
            reference
            ${DOCUMENT_REFERENCE_QUERY_FRAGMENT}
          }
        }
      }`;

    return query;
  },
};

export const medplumGraphQlQuery = {
  patientInfo,
  valueSet,
  emergencyContact,
  healthcareProxy,
  healthInsurance,
  questionnaireList,
  questionnaireResponseList,
  questionnaireResponsePSList,
  masterObservationList,
  careTeam,
  doctorList,
  auditEventList,
  shareProfileRecord,
  getProcedureList,
  familyMemberHistory,
  deleteProfile,
  getSymptomsObservationList,
  valueSetsForProfile,
  getAllergiesIntolerancesList,
  getPreventativeScreeningList,
  // downloadProfile,
  // medication,
  // supplement,
  condition,
  // surgery,
  // allergy,
  // screening,
  // immunization,
  // patientSymptoms,
  // patientVital,
  getImmunizationList,
  getMedicationsList,
};
