// Import necessary types and constants
import { AnalyticsEventName, AnalyticsFlow, EventPropsNames } from '@lib/analyticsService';
import { identifyHealthProfileUser } from '@lib/identifyService';

// Interface for VT events
export interface VitalsEventProps {
  EventName:
    | 'VitalInteracted'
    | 'VTBPAddStarted'
    | 'VTBPAddInProgSystolic'
    | 'VTBPAddInProgDiastolic'
    | 'VTBPAddInProgDate'
    | 'VTBPAddInProgTime'
    | 'VTBPAddInProgRecorder'
    | 'VTBPAddInProgPosition'
    | 'VTBPAddCompleted'
    | 'VTBPInteracted'
    | 'VTBPEdited'
    | 'VTBPRemoved'
    | 'VTBTAddStarted'
    | 'VTBTAddInProgTemparature'
    | 'VTBTAddInProgDate'
    | 'VTBTAddInProgTime'
    | 'VTBTAddCompleted'
    | 'VTBTInteracted'
    | 'VTBTEdited'
    | 'VTBTRemoved'
    | 'VTPRAddStarted'
    | 'VTPRAddInProgRate'
    | 'VTPRAddInProgDate'
    | 'VTPRAddInProgTime'
    | 'VTPRAddCompleted'
    | 'VTPRInteracted'
    | 'VTPREdited'
    | 'VTPRRemoved'
    | 'VTOSLAddStarted'
    | 'VTOSLAddInProgLevel'
    | 'VTOSLAddInProgDate'
    | 'VTOSLAddInProgTime'
    | 'VTOSLAddCompleted'
    | 'VTOSLInteracted'
    | 'VTOSLEdited'
    | 'VTOSLRemoved'
    | 'VTRRAddStarted'
    | 'VTRRAddInProgRate'
    | 'VTRRAddInProgDate'
    | 'VTRRAddInProgTime'
    | 'VTRRAddCompleted'
    | 'VTRRInteracted'
    | 'VTRREdited'
    | 'VTRRRemoved';

  // General VT
  vt_type?: string;
  vt_state?: 'empty' | 'filled';

  // Blood Pressure (BP)
  vt_bp_entry_point?: string;
  vt_bp_systolic?: number;
  vt_bp_diastolic?: number;
  vt_bp_date?: string;
  vt_bp_time?: string;
  vt_bp_recorder?: string;
  vt_bp_position?: string;

  // Body Temperature (BT)
  vt_bt_entry_point?: string;
  vt_bt_temparature?: number;
  vt_bt_date?: string;
  vt_bt_time?: string;

  // Pulse Rate (PR)
  vt_pr_entry_point?: string;
  vt_pr_rate?: number;
  vt_pr_date?: string;
  vt_pr_time?: string;

  // Oxygen Saturation Level (OSL)
  vt_osl_entry_point?: string;
  vt_osl_level?: number;
  vt_osl_date?: string;
  vt_osl_time?: string;

  // Respiratory Rate (RR)
  vt_rr_entry_point?: string;
  vt_rr_rate?: number;
  vt_rr_date?: string;
  vt_rr_time?: string;
}

// recordVitalsEvents function for VT events
export const recordVitalsEvents = (
  trackEventInFlow: (flow: AnalyticsFlow, eventName: AnalyticsEventName, data: Record<string, any>) => void,
  props: VitalsEventProps
) => {
  const analyticsData: Record<string, any> = {
    [EventPropsNames.FlowId]: 'VT',

    ...(props.vt_type && { [EventPropsNames.vitalType]: props.vt_type }),
    ...(props.vt_state && { [EventPropsNames.vitalState]: props.vt_state }),

    ...(props.vt_bp_entry_point && { [EventPropsNames.bpEntryPoint]: props.vt_bp_entry_point }),
    ...(props.vt_bp_systolic !== undefined && { [EventPropsNames.bpSystolic]: props.vt_bp_systolic }),
    ...(props.vt_bp_diastolic !== undefined && { [EventPropsNames.bpDiastolic]: props.vt_bp_diastolic }),
    ...(props.vt_bp_date && { [EventPropsNames.bpDate]: props.vt_bp_date }),
    ...(props.vt_bp_time && { [EventPropsNames.bpTime]: props.vt_bp_time }),
    ...(props.vt_bp_recorder && { [EventPropsNames.bpRecorder]: props.vt_bp_recorder }),
    ...(props.vt_bp_position && { [EventPropsNames.bpPosition]: props.vt_bp_position }),

    ...(props.vt_bt_entry_point && { [EventPropsNames.btEntryPoint]: props.vt_bt_entry_point }),
    ...(props.vt_bt_temparature !== undefined && { [EventPropsNames.btTemperature]: props.vt_bt_temparature }),
    ...(props.vt_bt_date && { [EventPropsNames.btDate]: props.vt_bt_date }),
    ...(props.vt_bt_time && { [EventPropsNames.btTime]: props.vt_bt_time }),

    ...(props.vt_pr_entry_point && { [EventPropsNames.prEntryPoint]: props.vt_pr_entry_point }),
    ...(props.vt_pr_rate !== undefined && { [EventPropsNames.prRate]: props.vt_pr_rate }),
    ...(props.vt_pr_date && { [EventPropsNames.prDate]: props.vt_pr_date }),
    ...(props.vt_pr_time && { [EventPropsNames.prTime]: props.vt_pr_time }),

    ...(props.vt_osl_entry_point && { [EventPropsNames.oslEntryPoint]: props.vt_osl_entry_point }),
    ...(props.vt_osl_level !== undefined && { [EventPropsNames.oslLevel]: props.vt_osl_level }),
    ...(props.vt_osl_date && { [EventPropsNames.oslDate]: props.vt_osl_date }),
    ...(props.vt_osl_time && { [EventPropsNames.oslTime]: props.vt_osl_time }),

    ...(props.vt_rr_entry_point && { [EventPropsNames.rrEntryPoint]: props.vt_rr_entry_point }),
    ...(props.vt_rr_rate !== undefined && { [EventPropsNames.rrRate]: props.vt_rr_rate }),
    ...(props.vt_rr_date && { [EventPropsNames.rrDate]: props.vt_rr_date }),
    ...(props.vt_rr_time && { [EventPropsNames.rrTime]: props.vt_rr_time }),
  };
  trackEventInFlow(AnalyticsFlow.VitalFlow, AnalyticsEventName[props.EventName], analyticsData);
};

interface CareTeamProps {
  ct_first_name?: string;
  ct_last_name?: string;
  ct_speciality?: string;
  ct_phone_number?: string;
  ct_alt_phone_number?: string;
  ct_email?: string;
  ct_city?: string;
  primary_doctor?: boolean;
  EventName:
    | 'CareTeamAddStarted'
    | 'CareTeamAddInProgFirstName'
    | 'CareTeamAddInProgLastName'
    | 'CareTeamAddInProgSpeciality'
    | 'CareTeamAddInProgPhoneNumber'
    | 'CareTeamAddInProgAltPhoneNumber'
    | 'CareTeamAddInProgEmail'
    | 'CareTeamAddInProgCity'
    | 'CareTeamAddInProgPrimary'
    | 'CareTeamAddCompleted'
    | 'CareTeamEditStarted'
    | 'CareTeamEditCompleted'
    | 'CareTeamRemoved';
}

// Function to record My Care Team events
export const recordCareTeamEvents = (
  trackEventInFlow: (flow: AnalyticsFlow, eventName: AnalyticsEventName, data: Record<string, any>) => void,
  props: CareTeamProps
) => {
  const analyticsData: Record<string, any> = {
    [EventPropsNames.FlowId]: 'CT',
    ...(props.ct_first_name && { [EventPropsNames.CareTeamFirstName]: props.ct_first_name }),
    ...(props.ct_last_name && { [EventPropsNames.CareTeamLastName]: props.ct_last_name }),
    ...(props.ct_speciality && { [EventPropsNames.CareTeamSpeciality]: props.ct_speciality }),
    ...(props.ct_phone_number && { [EventPropsNames.CareTeamPhoneNumber]: props.ct_phone_number }),
    ...(props.ct_alt_phone_number && { [EventPropsNames.CareTeamAltPhoneNumber]: props.ct_alt_phone_number }),
    ...(props.ct_email && { [EventPropsNames.CareTeamEmail]: props.ct_email }),
    ...(props.ct_city && { [EventPropsNames.CareTeamCity]: props.ct_city }),
    ...(props.primary_doctor !== undefined && { [EventPropsNames.PrimaryDoctor]: props.primary_doctor }),
  };
  trackEventInFlow(AnalyticsFlow.MyCareTeam, AnalyticsEventName[props.EventName], analyticsData);
};

// Interface for Login Page Event Props
interface LoginPageProps {
  otp_correct?: boolean; // Indicates OTP correctness
  login_type?: 'otp' | 'mpin' | '2fa'; // Login method type
  EventName:
    | 'LoginMobileNumberEntered' // Mobile number entered
    | 'LoginMobileNumberError' // Mobile number error
    | 'LoginStarted' // Login process started
    | 'PINError1' // First PIN error (mpin)
    | 'PINError2' // Second PIN error (mpin)
    | 'PINError3' // Third PIN error (mpin)
    | 'LoginAccountLocked' // Account locked event
    | 'LoginMPINEntered' // Login MPIN Entered
    | 'Login2FAOTPError' // Login 2FA OTP Error
    | 'Login2FAOTPExpired' // Login 2FA OTP Expired
    | 'Login2FAOTPResendClicked' // Login 2FA OTP Resend Clicked
    | 'PINResetStarted' // PIN reset started
    | 'OTPEntered' // OTP entered event (otp)
    | 'OTPEnteredError' // OTP entry error (otp)
    | 'OTPExpiredError' // OTP expiration error (otp)
    | 'ResendOTPClicked' // Resend OTP clicked (otp)
    | 'PINResetCompleted' // PIN reset completed (mpin)
    | 'LoginCompleted'; // Successful login event (otp/mpin)
}

// Function to Record Login Events
export const recordLoginEvents = (
  trackEventInFlow: (flow: AnalyticsFlow, eventName: AnalyticsEventName, data: Record<string, any>) => void,
  props: LoginPageProps
) => {
  const analyticsData: Record<string, any> = {
    [EventPropsNames.FlowId]: 'LO', // Login flow identifier
    ...(props.login_type && { [EventPropsNames.LoginType]: props.login_type }), // Optional login type
    ...(props.otp_correct && { [EventPropsNames.OTPCorrect]: props.otp_correct }), // Optional OTP correctness
  };
  trackEventInFlow(AnalyticsFlow.LoginFlow, AnalyticsEventName[props.EventName], analyticsData);
};

// Interface for Condition Page Event Props
export interface ConditionEventProps {
  EventName:
    | 'ConditionAddStarted'
    | 'ConditionAddInProgName'
    | 'ConditionAddInProgChronicity'
    | 'ConditionAddInProgStartDate'
    | 'ConditionAddInProgShared'
    | 'ConditionAddInProgStatus'
    | 'ConditionAddInProgEndDate'
    | 'ConditionAddInProgNotes'
    | 'ConditionAddInProgRecordsAdded'
    | 'ConditionAddCompleted'
    | 'ConditionInteracted'
    | 'ConditionEdited'
    | 'ConditionRemoved';
  co_entry_point?: string; // where the flow was started
  co_name?: string; // condition FACT code / name
  co_chronicity?: string; // e.g., "chronic" or "acute"
  co_start_date?: string; // date condition was detected
  co_shared?: boolean; // whether condition is shared
  co_status?: string; // whether user still has the condition (yes/no)
  co_end_date?: string; // condition end date
  co_notes?: string; // additional details entered by the user
  co_records_added?: boolean; // whether health records were linked
}

// Function to Record Condition Events
export const recordConditionEvents = (
  trackEventInFlow: (flow: AnalyticsFlow, eventName: AnalyticsEventName, data: Record<string, any>) => void,
  props: ConditionEventProps
) => {
  const analyticsData: Record<string, any> = {
    [EventPropsNames.FlowId]: 'CO',
    ...(props.co_entry_point && { [EventPropsNames.co_entry_point]: props.co_entry_point }),
    ...(props.co_name && { [EventPropsNames.co_name]: props.co_name }),
    ...(props.co_chronicity && { [EventPropsNames.co_chronicity]: props.co_chronicity }),
    ...(props.co_start_date && { [EventPropsNames.co_start_date]: props.co_start_date }),
    ...(props.co_shared !== undefined && { [EventPropsNames.co_shared]: props.co_shared }),
    ...(props.co_status && { [EventPropsNames.co_status]: props.co_status }),
    ...(props.co_end_date && { [EventPropsNames.co_end_date]: props.co_end_date }),
    ...(props.co_notes && { [EventPropsNames.co_notes]: props.co_notes }),
    ...(props.co_records_added !== undefined && { [EventPropsNames.co_records_added]: props.co_records_added }),
  };
  trackEventInFlow(AnalyticsFlow.ConditionFlow, AnalyticsEventName[props.EventName], analyticsData);
};

// Interface for Symptom Page Event Props
interface SymptomSectionProps {
  sy_entry_point: 'my_health_profile' | 'dashboard_card' | 'other'; // Entry point for symptom assessment
  sy_name?: string; // FACT code of symptom name
  sy_start_date?: string; // Start date of symptom (yyyy/mm/dd)
  sy_end_date?: string; // End date of symptom (yyyy/mm/dd)
  sy_status?: boolean; // Whether the user still has the symptom (yes/no)
  sy_record_added?: boolean; // Whether linked health records were added
  sy_notes?: string; // Notes added by the user

  EventName:
    | 'SymptomsAddStarted' // Start of symptom addition
    | 'SymptomsAddInProgName' // Symptom name in progress
    | 'SymptomsAddInProgStartDate' // Start date in progress
    | 'SymptomsAddInProgEndDate' // End date in progress
    | 'SymptomsAddInProgRecordsAdded' // Records in progress
    | 'SymptomsAddInProgStatus' // Status toggle interaction
    | 'SymptomsAddInProgNotes' // Notes added
    | 'SymptomsAddCompleted' // Symptom addition completed
    | 'SymptomsInteracted' // Symptom interaction
    | 'SymptomsEdited' // Symptom edited
    | 'SymptomsRemoved'; // Symptom removed
}

// Function to Record Symptoms Events
export const recordSymptomsEvents = (
  trackEventInFlow: (flow: AnalyticsFlow, eventName: AnalyticsEventName, data: Record<string, any>) => void,
  props: SymptomSectionProps
) => {
  const analyticsData: Record<string, any> = {
    [EventPropsNames.FlowId]: 'SY', // Symptoms flow identifier
    [EventPropsNames.SymptomEntryPoint]: props.sy_entry_point, // Entry point for symptoms flow
    ...(props.sy_name && { [EventPropsNames.SymptomName]: props.sy_name }), // FACT code of symptom name
    ...(props.sy_start_date && { [EventPropsNames.SymptomStartDate]: props.sy_start_date }), // Start date
    ...(props.sy_end_date && { [EventPropsNames.SymptomEndDate]: props.sy_end_date }), // End date
    ...(props.sy_status !== undefined && { [EventPropsNames.SymptomStatus]: props.sy_status }), // Symptom status (yes/no)
    ...(props.sy_record_added !== undefined && { [EventPropsNames.SymptomLinkedRecordAdded]: props.sy_record_added }), // Linked records
    ...(props.sy_notes && { [EventPropsNames.SymptomNotes]: props.sy_notes }), // Notes entered by user
  };
  trackEventInFlow(AnalyticsFlow.SymptomsFlow, AnalyticsEventName[props.EventName], analyticsData);
};
// Interface for Lifestyle Nutrition Event Props
export interface LifestyleNutritionEventProps {
  EventName:
    | 'LNExerciseAddStarted'
    | 'LNExerciseAddInProgActivity'
    | 'LNExerciseAddInProgFrequency'
    | 'LNExerciseAddInProgIntenseExercise'
    | 'LNExerciseAddInProgFitnessRoutine'
    | 'LNExerciseAddCompleted'
    | 'LNExerciseInteracted'
    | 'LNExerciseEdited'
    | 'LNExerciseRemoved'
    | 'LNDietAddStarted'
    | 'LNDietAddInProgType'
    | 'LNDietAddInProgPreferences'
    | 'LNDietAddCompleted'
    | 'LNDietInteracted'
    | 'LNDietEdited'
    | 'LNDietRemoved'
    | 'LNATCAddStarted'
    | 'LNATCAddInProgAlcohol'
    | 'LNATCAddInProgTobacco'
    | 'LNATCAddInProgCaffeine'
    | 'LNATCAddCompleted'
    | 'LNATCInteracted'
    | 'LNATCEdited'
    | 'LNATCRemoved'
    | 'LNMHSAddStarted'
    | 'LNMHSAddInProgMentalCondition'
    | 'LNMHSAddInProgFamilyHistory'
    | 'LNMHSAddInProgBurntOut'
    | 'LNMHSAddInProgSleep'
    | 'LNMHSAddInProgMorningFeel'
    | 'LNMHSAddInProgStressLevel'
    | 'LNMHSAddCompleted'
    | 'LNMHSInteracted'
    | 'LNMHSEdited'
    | 'LNMHSRemoved'
    | 'LNOccupationAddStarted'
    | 'LNOccupationAddInProgName'
    | 'LNOccupationAddInProgExtendedHours'
    | 'LNOccupationAddCompleted'
    | 'LNOccupationInteracted'
    | 'LNOccupationEdited'
    | 'LNOccupationRemoved';
  entry_point?: string;
  activity?: string;
  frequency?: string;
  intense_exercise?: boolean;
  fitness_routine?: boolean;
  diet_type?: string;
  diet_preference?: string;
  atc_alcohol?: boolean;
  atc_tobacco?: boolean;
  atc_caffeine?: boolean;
  mental_condition?: string;
  family_history?: boolean;
  burnt_out?: boolean;
  sleep_duration?: string;
  morning_feel?: string;
  stress_level?: string;
  occupation?: string;
  extended_hours?: boolean;
  ln_exercise_entry_point?: string;
}
// Function to Record Lifestyle Nutrition Events
export const recordLifestyleNutritionEvents = (
  trackEventInFlow: (flow: AnalyticsFlow, eventName: AnalyticsEventName, data: Record<string, any>) => void,
  props: LifestyleNutritionEventProps
) => {
  const analyticsData: Record<string, any> = {
    [EventPropsNames.FlowId]: 'LN',
    ...(props.entry_point && { [EventPropsNames.LNExerciseEntryPoint]: props.entry_point }),
    ...(props.activity && { [EventPropsNames.LNExerciseActivity]: props.activity }),
    ...(props.frequency && { [EventPropsNames.LNExerciseFrequency]: props.frequency }),
    ...(props.intense_exercise !== undefined && { [EventPropsNames.LNIntenseExercise]: props.intense_exercise }),
    ...(props.fitness_routine !== undefined && { [EventPropsNames.LNFitnessRoutine]: props.fitness_routine }),
    ...(props.diet_type && { [EventPropsNames.LNDietType]: props.diet_type }),
    ...(props.diet_preference && { [EventPropsNames.LNDietPreference]: props.diet_preference }),
    ...(props.atc_alcohol !== undefined && { [EventPropsNames.LNATCAlcohol]: props.atc_alcohol }),
    ...(props.atc_tobacco !== undefined && { [EventPropsNames.LNATCTobacco]: props.atc_tobacco }),
    ...(props.atc_caffeine !== undefined && { [EventPropsNames.LNATCCaffeine]: props.atc_caffeine }),
    ...(props.mental_condition && { [EventPropsNames.LNMHSMentalCondition]: props.mental_condition }),
    ...(props.family_history !== undefined && { [EventPropsNames.LNMHSFamilyHistory]: props.family_history }),
    ...(props.burnt_out !== undefined && { [EventPropsNames.LNMHSBurntOut]: props.burnt_out }),
    ...(props.sleep_duration && { [EventPropsNames.LNMHSSleepDuration]: props.sleep_duration }),
    ...(props.morning_feel && { [EventPropsNames.LNMHSMorningFeel]: props.morning_feel }),
    ...(props.stress_level && { [EventPropsNames.LNMHSStressLevel]: props.stress_level }),
    ...(props.occupation && { [EventPropsNames.LNOccupation]: props.occupation }),
    ...(props.extended_hours !== undefined && { [EventPropsNames.LNOccupationExtendedHours]: props.extended_hours }),
  };
  trackEventInFlow(AnalyticsFlow.LifestyleNutritionFlow, AnalyticsEventName[props.EventName], analyticsData);
};
// Interface for Upload Record Page Event Props
interface UploadRecordSectionProps {
  ur_user?: string; // User upload context
  ur_file_size?: number; // File size in MB (converted from bytes)
  ur_encryption?: boolean; // Encryption status
  document_id?: string; // Document ID
  ur_type?: string; // Record type
  ur_visit_date?: string; // Visit date
  ur_doctor_name?: string; // Doctor's name
  ur_notes?: boolean; // Notes status
  EventName:
    | 'UploadRecordStarted' //
    | 'UploadRecordInProgUserSelected' //
    | 'UploadRecordInProgSuccessful' //
    | 'UploadRecordInProgTypeSelected' //
    | 'UploadRecordInProgNameAdded' //
    | 'UploadRecordInProgVisitDateAdded'
    | 'UploadRecordInProgDoctorNameAdded' //
    | 'UploadRecordInProgNotesAdded' //
    | 'UploadRecordCompleted'
    | 'UploadRecordExited';
}

// Function to Record Upload Record Events
export const recordUploadRecordEvents = (
  trackEventInFlow: (flow: AnalyticsFlow, eventName: AnalyticsEventName, data: Record<string, any>) => void,
  props: UploadRecordSectionProps
) => {
  const analyticsData: Record<string, any> = {
    [EventPropsNames.FlowId]: 'UR', // Upload record flow identifier
    ...(props.ur_user && { [EventPropsNames.UploadRecordUser]: props.ur_user }),
    ...(props.ur_file_size && { [EventPropsNames.UploadRecordFileSize]: props.ur_file_size }),
    ...(props.ur_encryption && { [EventPropsNames.UploadRecordEncryption]: props.ur_encryption }),
    ...(props.document_id && { [EventPropsNames.UploadRecordDocumentId]: props.document_id }),
    ...(props.ur_type && { [EventPropsNames.UploadRecordType]: props.ur_type }),
    ...(props.ur_visit_date && { [EventPropsNames.UploadRecordVisitDate]: props.ur_visit_date }),
    ...(props.ur_doctor_name && { [EventPropsNames.UploadRecordDoctorName]: props.ur_doctor_name }),
    ...(props.ur_notes !== undefined && { [EventPropsNames.UploadRecordNotes]: props.ur_notes }),
  };
  trackEventInFlow(AnalyticsFlow.UploadRecordFlow, AnalyticsEventName[props.EventName], analyticsData);
};

// Interface for Bookmark Event Props
interface BookmarkEventProps {
  rb_record_type: string; // Type of record being bookmarked or unbookmarked
  EventName: 'RecordBookmarked' | 'RecordUnbookmarked';
}

// Function to Record Bookmark/Unbookmark Events
export const recordBookmarkEvents = (
  trackEventInFlow: (flow: AnalyticsFlow, eventName: AnalyticsEventName, data: Record<string, any>) => void,
  props: BookmarkEventProps
) => {
  const analyticsData: Record<string, any> = {
    [EventPropsNames.FlowId]: 'RB',
    [EventPropsNames.RecordBookmarkType]: props.rb_record_type,
  };
  trackEventInFlow(AnalyticsFlow.BookmarkFlow, AnalyticsEventName[props.EventName], analyticsData);
};

// Interface for Edit MedicalRecord Event Props
interface EditMedicalRecordProps {
  EventName: 'EditRecordStarted' | 'EditRecordCompleted' | 'RecordRemoved';
  re_record_type?: string; // Type of record being edited or removed
  document_id?: string; // Type of record being edited or removed
}

// Function to MedicalRecord Edit Events
export const recordEditMedicalRecordEvents = (
  trackEventInFlow: (flow: AnalyticsFlow, eventName: AnalyticsEventName, data: Record<string, any>) => void,
  props: EditMedicalRecordProps
) => {
  const analyticsData: Record<string, any> = {
    [EventPropsNames.FlowId]: 'RE',
    ...(props.re_record_type && { [EventPropsNames.EditMedicalRecordType]: props.re_record_type }),
    ...(props.document_id && { [EventPropsNames.UploadRecordDocumentId]: props.document_id }),
  };
  trackEventInFlow(AnalyticsFlow.EditMedicalRecordFlow, AnalyticsEventName[props.EventName], analyticsData);
};

// Interface for Health Record Hub Event Props
interface HealthRecordHubProps {
  hrh_tab_name?: string; // Name of the tab interacted with by the user
  hrh_searched_term?: string; // Term typed by the user in the search bar
  ri_record_type?: string; // Type of record interacted with by the user
  document_id?: string;
  reason_of_rejection?: string;
  pri_record_type?: string;
  EventName:
    | 'HealthRecordHubInteracted' // User clicked on a tab in the Health Record Hub
    | 'HealthRecordHubSearched' // User searched in the Health Record Hub
    | 'RecordInteracted' // User clicked on a specific record
    | 'EncryptedRecordInteracted'
    | 'EncryptedRecordUnlocked'
    | 'EncryptedRecordPasswordError'
    | 'RejectedRecordInteracted'
    | 'RejectedRecordReuploadInteracted'
    | 'ApprovedRecordInteracted'
    | 'ProcessingRecordInteracted';
}

// Function to Record Health Record Hub Events
export const recordHealthRecordHubEvents = (
  trackEventInFlow: (flow: AnalyticsFlow, eventName: AnalyticsEventName, data: Record<string, any>) => void,
  props: HealthRecordHubProps
) => {
  const eventData: Record<string, any> = {
    [EventPropsNames.FlowId]: 'HRH', // Health Record Hub flow identifier
    ...(props.hrh_tab_name && { [EventPropsNames.RecordTabName]: props.hrh_tab_name }), // Tab name if provided
    ...(props.document_id && { [EventPropsNames.UploadRecordDocumentId]: props.document_id }), // Search term if provided
    ...(props.pri_record_type && { [EventPropsNames.ProcessingRecordType]: props.pri_record_type }), // Search term if provided
    ...(props.hrh_searched_term && { [EventPropsNames.RecordSearchedTerm]: props.hrh_searched_term }), // Search term if provided
    ...(props.ri_record_type && { [EventPropsNames.HealthRecordType]: props.ri_record_type }), // Record type if provided
    ...(props.reason_of_rejection && { [EventPropsNames.ReasonOfRejection]: props.reason_of_rejection }), // Record type if provided
  };
  trackEventInFlow(AnalyticsFlow.HealthRecordHubFlow, AnalyticsEventName[props.EventName], eventData);
};

// Interface for Record Download Event Props
interface RecordDownloadProps {
  rd_record_type: string; // Type of record downloaded by the user
  document_id: string; // User of records
  EventName: 'RecordDownloaded'; // Event indicating a record has been downloaded
}

// Function to Record Record Download Events
export const recordRecordDownloadEvents = (
  trackEventInFlow: (flow: AnalyticsFlow, eventName: AnalyticsEventName, data: Record<string, any>) => void,
  props: RecordDownloadProps
) => {
  const analyticsData: Record<string, any> = {
    [EventPropsNames.FlowId]: 'RD', // Record Download flow identifier
    [EventPropsNames.RecordMedicalType]: props.rd_record_type, // Type of record downloaded
    [EventPropsNames.UploadRecordDocumentId]: props.document_id, // Owner of the record
  };

  trackEventInFlow(AnalyticsFlow.RecordDownloadFlow, AnalyticsEventName[props.EventName], analyticsData);
};

// Interface for Record Shared Event Props
interface RecordSharedProps {
  rs_record_type: string; // Type of record shared by the user
  rs_record_owner: string; // User of records
  rs_platform: string; // Platform where the record was shared (e.g., gmail, WhatsApp)
  document_id: string; // Document ID

  EventName: 'RecordShared'; // Event indicating a record has been shared
}
// Function to Record Record Shared Events
export const recordSharedMedicalRecordEvents = (
  trackEventInFlow: (flow: AnalyticsFlow, eventName: AnalyticsEventName, data: Record<string, any>) => void,
  props: RecordSharedProps
) => {
  const analyticsData: Record<string, any> = {
    [EventPropsNames.FlowId]: 'RS', // Record Shared flow identifier
    [EventPropsNames.SharedMedicalRecordType]: props.rs_record_type, // Type of record shared
    [EventPropsNames.SharedMedicalRecordOwner]: props.rs_record_owner, // Owner of the record
    [EventPropsNames.UploadRecordDocumentId]: props.document_id, // Owner of the record
    [EventPropsNames.SharedMedicalRecordPlatform]: props.rs_platform, // Platform where record was shared
  };

  trackEventInFlow(AnalyticsFlow.SharedMedicalRecordFlow, AnalyticsEventName[props.EventName], analyticsData);
};

// Interface for Record Sorted and Filtered Event Props
interface RecordSortedFilteredProps {
  filter_type?: 'Sort' | 'Record Type' | 'Tags' | 'Date Range' | 'Bookmark'; // Type of filter applied
  filter_value?: string; // Value of the filter selected

  EventName:
    | 'RecordsFilteredApplied' // When a filter is applied
    | 'RecordsFilterRemoved'; // When a filter is removed
}

// Function to Record Sorted and Filtered Events
export const recordMedicalRecordFilterEvents = (
  trackEventInFlow: (flow: AnalyticsFlow, eventName: AnalyticsEventName, data: Record<string, any>) => void,
  props: RecordSortedFilteredProps
) => {
  const analyticsData: Record<string, any> = {
    [EventPropsNames.FlowId]: 'RF', // Record Sorted and Filtered flow identifier
    ...(props.filter_type && { [EventPropsNames.MedicalRecordFilterType]: props.filter_type }), // Optional filter type
    ...(props.filter_value && { [EventPropsNames.MedicalRecordFilterValue]: props.filter_value }), // Optional filter value
  };
  trackEventInFlow(AnalyticsFlow.MedicalRecordFilterFlow, AnalyticsEventName[props.EventName], analyticsData);
};

// Interface for Medications Event Props
export interface MedicationsEventProps {
  me_entry_point?: string;
  me_name?: string | string[];
  me_status?: boolean;
  me_start_date?: string;
  me_end_date?: string;
  me_dosage_morning?: number;
  me_dosage_afternoon?: number;
  me_dosage_evening?: number;
  me_dosage_night?: number;
  me_dosage_duration?: number;
  me_food?: string;
  me_administered?: string;
  me_notes?: string;
  me_record_added?: boolean;
  EventName:
    | 'MedicationsAddStarted'
    | 'MedicationsAddInProgType'
    | 'MedicationsAddInProgName'
    | 'MedicationsAddInProgStatus'
    | 'MedicationsAddInProgStartDate'
    | 'MedicationsAddInProgEndDate'
    | 'MedicationsAddInProgScheduleMorning'
    | 'MedicationsAddInProgScheduleAfternoon'
    | 'MedicationsAddInProgScheduleEvening'
    | 'MedicationsAddInProgScheduleNight'
    | 'MedicationsAddInProgScheduleDuration'
    | 'MedicationsAddInProgScheduleFood'
    | 'MedicationsAddInProgFrequency'
    | 'MedicationsAddInProgAdministered'
    | 'MedicationsAddInProgNotes'
    | 'MedicationsAddInProgRecordsAdded'
    | 'MedicationsAddCompleted'
    | 'MedicationsInteracted'
    | 'MedicationsEdited'
    | 'MedicationsRemoved';
}

// Function to Record Medications Events
export const recordMedicationsEvents = (
  trackEventInFlow: (flow: AnalyticsFlow, eventName: AnalyticsEventName, data: Record<string, any>) => void,
  props: MedicationsEventProps
) => {
  const analyticsData: Record<string, any> = {
    [EventPropsNames.FlowId]: 'ME',
    ...(props.me_entry_point && { [EventPropsNames.MedicationEntryPoint]: props.me_entry_point }),
    ...(props.me_name && { [EventPropsNames.MedicationName]: props.me_name }),
    ...(props.me_status !== undefined && { [EventPropsNames.MedicationStatus]: props.me_status }),
    ...(props.me_start_date && { [EventPropsNames.MedicationStartDate]: props.me_start_date }),
    ...(props.me_end_date && { [EventPropsNames.MedicationEndDate]: props.me_end_date }),
    ...(props.me_dosage_morning !== undefined && {
      [EventPropsNames.MedicationDosageMorning]: props.me_dosage_morning,
    }),
    ...(props.me_dosage_afternoon !== undefined && {
      [EventPropsNames.MedicationDosageAfternoon]: props.me_dosage_afternoon,
    }),
    ...(props.me_dosage_evening !== undefined && {
      [EventPropsNames.MedicationDosageEvening]: props.me_dosage_evening,
    }),
    ...(props.me_dosage_night !== undefined && { [EventPropsNames.MedicationDosageNight]: props.me_dosage_night }),
    ...(props.me_dosage_duration !== undefined && {
      [EventPropsNames.MedicationDosageDuration]: props.me_dosage_duration,
    }),
    ...(props.me_food && { [EventPropsNames.MedicationFood]: props.me_food }),
    ...(props.me_administered && { [EventPropsNames.MedicationAdministered]: props.me_administered }),
    ...(props.me_notes && { [EventPropsNames.MedicationNotes]: props.me_notes }),
    ...(props.me_record_added !== undefined && { [EventPropsNames.MedicationRecordsAdded]: props.me_record_added }),
  };
  trackEventInFlow(AnalyticsFlow.MedicationsFlow, AnalyticsEventName[props.EventName], analyticsData);
};

export interface SupplementsEventProps {
  su_entry_point?: string;
  su_name?: string | string[];
  su_start_date?: string;
  su_status?: boolean;
  su_end_date?: string;
  su_dosage_morning?: number;
  su_dosage_afternoon?: number;
  su_dosage_evening?: number;
  su_dosage_night?: number;
  su_dosage_duration?: number;
  su_food?: string;
  su_dosage?: string;
  su_frequency?: string;
  su_administered?: string;
  su_prescribed?: boolean;
  su_record_added?: boolean;
  su_notes?: string;
  EventName:
    | 'SupplementsAddStarted'
    | 'SupplementsAddInProgName'
    | 'SupplementsAddInProgStartDate'
    | 'SupplementsAddInProgStatus'
    | 'SupplementsAddInProgEndDate'
    | 'SupplementsAddInProgScheduleMorning'
    | 'SupplementsAddInProgScheduleAfternoon'
    | 'SupplementsAddInProgScheduleEvening'
    | 'SupplementsAddInProgScheduleNight'
    | 'SupplementsAddInProgScheduleDuration'
    | 'SupplementsAddInProgScheduleFood'
    | 'SupplementsAddInProgDosage'
    | 'SupplementsAddInProgFrequency'
    | 'SupplementsAddInProgAdministered'
    | 'SupplementsAddInProgPrescribed'
    | 'SupplementsAddInProgRecordsAdded'
    | 'SupplementsAddInProgNotes'
    | 'SupplementsAddCompleted'
    | 'SupplementsInteracted'
    | 'SupplementsEdited'
    | 'SupplementsRemoved';
}

// Function to Record Supplements Events
export const recordSupplementsEvents = (
  trackEventInFlow: (flow: AnalyticsFlow, eventName: AnalyticsEventName, data: Record<string, any>) => void,
  props: SupplementsEventProps
) => {
  const analyticsData: Record<string, any> = {
    [EventPropsNames.FlowId]: 'SU',
    ...(props.su_entry_point && { [EventPropsNames.SupplementEntryPoint]: props.su_entry_point }),
    ...(props.su_name && { [EventPropsNames.SupplementName]: props.su_name }),
    ...(props.su_start_date && { [EventPropsNames.SupplementStartDate]: props.su_start_date }),
    ...(props.su_status !== undefined && { [EventPropsNames.SupplementStatus]: props.su_status }),
    ...(props.su_end_date && { [EventPropsNames.SupplementEndDate]: props.su_end_date }),
    ...(props.su_dosage_morning !== undefined && {
      [EventPropsNames.SupplementDosageMorning]: props.su_dosage_morning,
    }),
    ...(props.su_dosage_afternoon !== undefined && {
      [EventPropsNames.SupplementDosageAfternoon]: props.su_dosage_afternoon,
    }),
    ...(props.su_dosage_evening !== undefined && {
      [EventPropsNames.SupplementDosageEvening]: props.su_dosage_evening,
    }),
    ...(props.su_dosage_night !== undefined && { [EventPropsNames.SupplementDosageNight]: props.su_dosage_night }),
    ...(props.su_dosage_duration !== undefined && {
      [EventPropsNames.SupplementDosageDuration]: props.su_dosage_duration,
    }),
    ...(props.su_food && { [EventPropsNames.SupplementFood]: props.su_food }),
    ...(props.su_dosage && { [EventPropsNames.SupplementDosage]: props.su_dosage }),
    ...(props.su_frequency && { [EventPropsNames.SupplementFrequency]: props.su_frequency }),
    ...(props.su_administered && { [EventPropsNames.SupplementAdministered]: props.su_administered }),
    ...(props.su_prescribed !== undefined && { [EventPropsNames.SupplementPrescribed]: props.su_prescribed }),
    ...(props.su_record_added !== undefined && { [EventPropsNames.SupplementRecordsAdded]: props.su_record_added }),
    ...(props.su_notes && { [EventPropsNames.SupplementNotes]: props.su_notes }),
  };
  trackEventInFlow(AnalyticsFlow.SupplementsFlow, AnalyticsEventName[props.EventName], analyticsData);
};
// Interface for Allergies Event Props
interface AllergiesEventProps {
  al_entry_point?: string;
  al_name?: string;
  al_type?: string;
  al_criticality?: string;
  al_date?: string;
  al_status?: boolean;
  al_end_date?: string;
  al_notes?: string;
  al_records_added?: boolean;

  EventName:
    | 'AllergiesAddStarted'
    | 'AllergiesAddInProgName'
    | 'AllergiesAddInProgType'
    | 'AllergiesAddInProgCriticality'
    | 'AllergiesAddInProgStartDate'
    | 'AllergiesAddInProgStatus'
    | 'AllergiesAddInProgEndDate'
    | 'AllergiesAddInProgNotes'
    | 'AllergiesAddInProgRecordsAdded'
    | 'AllergiesAddCompleted'
    | 'AllergiesInteracted'
    | 'AllergiesEdited'
    | 'AllergiesRemoved';
}

// Function to Record Allergies Events
export const recordAllergiesEvents = (
  trackEventInFlow: (flow: AnalyticsFlow, eventName: AnalyticsEventName, data: Record<string, any>) => void,
  props: AllergiesEventProps
) => {
  const analyticsData: Record<string, any> = {
    [EventPropsNames.FlowId]: 'AL', // Allergies flow identifier
    ...(props.al_entry_point && { [EventPropsNames.AllergyEntryPoint]: props.al_entry_point }),
    ...(props.al_name && { [EventPropsNames.AllergyName]: props.al_name }),
    ...(props.al_type && { [EventPropsNames.AllergyType]: props.al_type }),
    ...(props.al_criticality && { [EventPropsNames.AllergyCriticality]: props.al_criticality }),
    ...(props.al_date && { [EventPropsNames.AllergyDiagnosedDate]: props.al_date }),
    ...(props.al_status !== undefined && { [EventPropsNames.AllergyStatus]: props.al_status }),
    ...(props.al_end_date && { [EventPropsNames.AllergyEndDate]: props.al_end_date }),
    ...(props.al_notes && { [EventPropsNames.AllergyNotes]: props.al_notes }),
    ...(props.al_records_added !== undefined && { [EventPropsNames.AllergyRecordsAdded]: props.al_records_added }),
  };
  trackEventInFlow(AnalyticsFlow.AllergiesFlow, AnalyticsEventName[props.EventName], analyticsData);
};
// Interface for Surgery Event Props
interface SurgerySectionProps {
  sur_entry_point: 'my_health_profile' | 'dashboard_card' | 'other'; // Entry point for surgery flow
  sur_type?: string; // FACT code of surgery type
  sur_status?: string; // Status of the surgery
  sur_date?: string; // Date of surgery (yyyy/mm/dd)
  sur_notes?: string; // Notes added by the user
  sur_records_added?: boolean; // Whether linked records were added

  EventName:
    | 'SurgeriesAddStarted' // Start of surgery addition
    | 'SurgeriesAddInProgType' // Surgery type in progress
    | 'SurgeriesAddInProgStatus' // Surgery status in progress
    | 'SurgeriesAddInProgDate' // Surgery date in progress
    | 'SurgeriesAddInProgNotes' // Notes added
    | 'SurgeriesAddInProgRecordsAdded' // Records added
    | 'SurgeriesAddCompleted' // Surgery addition completed
    | 'SurgeriesInteracted' // Surgery interaction
    | 'SurgeriesEdited' // Surgery edited
    | 'SurgeriesRemoved'; // Surgery removed
}

// Function to Record Surgery Events
export const recordSurgeryEvents = (
  trackEventInFlow: (flow: AnalyticsFlow, eventName: AnalyticsEventName, data: Record<string, any>) => void,
  props: SurgerySectionProps
) => {
  const analyticsData: Record<string, any> = {
    [EventPropsNames.FlowId]: 'SUR', // Surgeries flow identifier
    [EventPropsNames.SurgeryEntryPoint]: props.sur_entry_point, // Entry point for surgeries flow
    ...(props.sur_type && { [EventPropsNames.SurgeryType]: props.sur_type }), // FACT code of surgery type
    ...(props.sur_status && { [EventPropsNames.SurgeryStatus]: props.sur_status }), // Surgery status
    ...(props.sur_date && { [EventPropsNames.SurgeryDate]: props.sur_date }), // Surgery date
    ...(props.sur_notes && { [EventPropsNames.SurgeryNotes]: props.sur_notes }), // Notes added by the user
    ...(props.sur_records_added !== undefined && { [EventPropsNames.SurgeryRecordsAdded]: props.sur_records_added }), // Linked records
  };
  trackEventInFlow(AnalyticsFlow.SurgeriesFlow, AnalyticsEventName[props.EventName], analyticsData);
};

// Preventative Screening and Vaccine Events Tracking
interface PreventativeScreeningSectionProps {
  ps_entry_points: 'my_health_profile' | 'dashboard_card' | 'other'; // Entry point for preventative screening
  ps_type?: string; // Type of preventative screening
  ps_family_history?: boolean; // Family history status
  ps_hpv?: boolean; // HPV diagnosed status
  ps_sexually_active?: string; // Sexually active status
  ps_date?: string; // Date of screening
  ps_notes?: string; // Notes added by user
  ps_records_added?: boolean; // Linked records status

  EventName:
    | 'PreventativeScreeningAddStarted' // Start of screening addition
    | 'PreventativeScreeningAddInProgType' // Screening type selection
    | 'PreventativeScreeningAddInProgFamilyHistory' // Family history selection
    | 'PreventativeScreeningAddInProgHPVDiagnosed' // HPV diagnosed selection
    | 'PreventativeScreeningAddInProgSexuallyActive' // Sexually active selection
    | 'PreventativeScreeningAddInProgDate' // Date selection
    | 'PreventativeScreeningAddInProgNotes' // Notes addition
    | 'PreventativeScreeningAddInProgRecordsAdded' // Records addition
    | 'PreventativeScreeningAddCompleted' // Completion of screening addition
    | 'PreventativeScreeningInteracted' // Screening interaction
    | 'PreventativeScreeningEdited' // Screening edited
    | 'PreventativeScreeningRemoved'; // Screening removed
}

// Function to Record Preventative Screening Events
export const recordPreventativeScreeningEvents = (
  trackEventInFlow: (flow: AnalyticsFlow, eventName: AnalyticsEventName, data: Record<string, any>) => void,
  props: PreventativeScreeningSectionProps
) => {
  const analyticsData: Record<string, any> = {
    [EventPropsNames.FlowId]: 'PS', // Preventative Screening flow identifier
    [EventPropsNames.PreventativeScreeningEntryPoint]: props.ps_entry_points, // Entry point
    ...(props.ps_type && { [EventPropsNames.PreventativeScreeningType]: props.ps_type }),
    ...(props.ps_family_history !== undefined && {
      [EventPropsNames.PreventativeScreeningFamilyHistory]: props.ps_family_history,
    }),
    ...(props.ps_hpv !== undefined && { [EventPropsNames.PreventativeScreeningHPV]: props.ps_hpv }),
    ...(props.ps_sexually_active && {
      [EventPropsNames.PreventativeScreeningSexuallyActive]: props.ps_sexually_active,
    }),
    ...(props.ps_date && { [EventPropsNames.PreventativeScreeningDate]: props.ps_date }),
    ...(props.ps_notes && { [EventPropsNames.PreventativeScreeningNotes]: props.ps_notes }),
    ...(props.ps_records_added !== undefined && {
      [EventPropsNames.PreventativeScreeningRecordsAdded]: props.ps_records_added,
    }),
  };
  trackEventInFlow(AnalyticsFlow.PreventativeScreeningFlow, AnalyticsEventName[props.EventName], analyticsData);
};

// Vaccine Events Tracking
interface VaccineSectionProps {
  vc_entry_point: 'my_health_profile' | 'dashboard_card' | 'other'; // Entry point for vaccine
  vc_type?: string; // Type of vaccine selected
  vc_date?: string; // Date of vaccination
  vc_fully_vaccinated?: boolean; // Fully vaccinated status
  vc_dose_count?: number; // Number of doses
  vc_records_added?: boolean; // Linked records status

  EventName:
    | 'VaccineAddStarted' // Start of vaccine addition
    | 'VaccineAddInProgDate' // Vaccine date selection
    | 'VaccineAddInProgFullyVaccinated' // Fully vaccinated selection
    | 'VaccineAddInProgDoseCount' // Dose count selection
    | 'VaccineAddInProgRecordsAdded' // Records addition
    | 'VaccineAddCompleted' // Completion of vaccine addition
    | 'VaccineInteracted' // Vaccine interaction
    | 'VaccineEdited' // Vaccine edited
    | 'VaccineRemoved'; // Vaccine removed
}

// Function to Record Vaccine Events
export const recordVaccineEvents = (
  trackEventInFlow: (flow: AnalyticsFlow, eventName: AnalyticsEventName, data: Record<string, any>) => void,
  props: VaccineSectionProps
) => {
  const analyticsData: Record<string, any> = {
    [EventPropsNames.FlowId]: 'VC', // Vaccine flow identifier
    [EventPropsNames.VaccineEntryPoint]: props.vc_entry_point, // Entry point
    ...(props.vc_type && { [EventPropsNames.VaccineType]: props.vc_type }),
    ...(props.vc_date && { [EventPropsNames.VaccineDate]: props.vc_date }),
    ...(props.vc_fully_vaccinated !== undefined && {
      [EventPropsNames.VaccineFullyVaccinated]: props.vc_fully_vaccinated,
    }),
    ...(props.vc_dose_count !== undefined && { [EventPropsNames.VaccineDoseCount]: props.vc_dose_count }),
    ...(props.vc_records_added !== undefined && { [EventPropsNames.VaccineRecordsAdded]: props.vc_records_added }),
  };
  trackEventInFlow(AnalyticsFlow.VaccineFlow, AnalyticsEventName[props.EventName], analyticsData);
};

// Interface for Settings Event Props
export interface SettingsProps {
  st_settings_section?: string; // Name of settings section where interaction occurred
  st_settings_objective?: string; // Specific settings objective the user clicked
  st_face_id?: boolean; // Face ID toggle state, on or off
  st_weight_measurement_unit?: string; // Selected weight measurement unit (e.g., kg)
  st_height_measurement_unit?: string; // Selected height measurement unit (e.g., cm)
  st_push_notifications_preference?: boolean; // Preference state for push notifications, on or off
  st_email_preference?: boolean; // Email preference toggle state, on or off
  st_whatsapp_preference?: boolean; // WhatsApp preference toggle state, on or off
  st_policy_name?: string; // Legal policy name the user interacted with
  st_faq?: string; // FAQ item the user viewed or interacted with
  st_support_type?: string; // Type of support option selected by the user
  st_follow_us_platform?: string; // Which Social Media selected by the user
  EventName:
    | 'SettingsClicked' // User clicked the settings icon
    | 'SettingsInteracted' // Interaction with settings section/objective
    | 'MeasurementPreferencesHeightInteracted' // Height unit preference change
    | 'MeasurementPreferencesWeightInteracted' // Weight unit preference change
    | 'CommunicationPreferencesRemindersInteracted' // Interaction with reminder notifications toggle
    | 'CommunicationPreferencesMarkComInteracted' // Interaction with general communication settings
    | 'PersonalDataDeleted' // Request for personal data deletion
    | 'PersonalDataDownloaded' // Download of personal data
    | 'ChatButtonClicked' // Chat support button interaction
    | 'LegalPolicyInteracted' // Interaction with a legal policy
    | 'FAQInteracted' // Interaction with an FAQ
    | 'FluentSupportInteracted' // Access of a specific support option
    | 'AccountLoggedOut' // User logout
    | 'FollowUsOnPlatformInteracted'
    | 'AccountDeleteInteracted'
    | 'AccountDeleted'; // Social Media Events
}

// Function to Record Settings Events
export const recordSettingsEvents = (
  trackEventInFlow: (flow: AnalyticsFlow, eventName: AnalyticsEventName, data: Record<string, any>) => void,
  props: SettingsProps
) => {
  const analyticsData: Record<string, any> = {
    [EventPropsNames.FlowId]: 'ST', // Settings flow identifier
    ...(props.st_settings_section && { [EventPropsNames.SettingsSection]: props.st_settings_section }),
    ...(props.st_settings_objective && { [EventPropsNames.SettingsObjective]: props.st_settings_objective }),
    ...(props.st_weight_measurement_unit && {
      [EventPropsNames.SettingsWeightMeasurementUnit]: props.st_weight_measurement_unit,
    }),
    ...(props.st_height_measurement_unit && {
      [EventPropsNames.SettingsHeightMeasurementUnit]: props.st_height_measurement_unit,
    }),
    ...(props.st_push_notifications_preference !== undefined && {
      [EventPropsNames.SettingsPushNotificationsPreference]: props.st_push_notifications_preference,
    }),
    ...(props.st_email_preference !== undefined && {
      [EventPropsNames.SettingsEmailPreference]: props.st_email_preference,
    }),
    ...(props.st_whatsapp_preference !== undefined && {
      [EventPropsNames.SettingsWhatsAppPreference]: props.st_whatsapp_preference,
    }),
    ...(props.st_policy_name && { [EventPropsNames.SettingsPolicyName]: props.st_policy_name }),
    ...(props.st_faq && { [EventPropsNames.SettingsFAQ]: props.st_faq }),
    ...(props.st_support_type && { [EventPropsNames.SettingsSupportType]: props.st_support_type }),
    ...(props.st_follow_us_platform && { [EventPropsNames.SettingsSocialMediaPlatform]: props.st_follow_us_platform }),
  };
  trackEventInFlow(AnalyticsFlow.SettingsFlow, AnalyticsEventName[props.EventName], analyticsData);
};

// Profile Event Props Interface
interface ProfileEventProps {
  pi_tab_name?: string; // Profile tab name interacted
  pi_edited_fields?: string; // Edited fields in General Info
  pi_my_health_profile?: string; // Health Profile element name interacted

  EventName:
    | 'MyProfileInteracted' // Profile icon clicked
    | 'MyProfileTabInteracted' // Tab in Profile clicked
    | 'GeneralInfoEdited' // General Info edited
    | 'MyHealthProfileInteracted'; // Health Profile element clicked
}

// Record Profile Events
export const recordProfileEvents = (
  trackEventInFlow: (flow: AnalyticsFlow, eventName: AnalyticsEventName, data: Record<string, any>) => void,
  props: ProfileEventProps
) => {
  const analyticsData = {
    [EventPropsNames.FlowId]: 'PI',
    ...(props.pi_tab_name && { [EventPropsNames.ProfileTabName]: props.pi_tab_name }),
    ...(props.pi_edited_fields && { [EventPropsNames.ProfileEditedFields]: props.pi_edited_fields }),
    ...(props.pi_my_health_profile && { [EventPropsNames.ProfileMyHealthProfile]: props.pi_my_health_profile }),
  };

  trackEventInFlow(AnalyticsFlow.ProfileFlow, AnalyticsEventName[props.EventName], analyticsData);
};

// Health Insurance Event Properties Interface
interface HealthInsuranceEventProps {
  hi_policy_provider?: string; // Selected policy provider name
  hi_insurance_number?: boolean; // insurance number added status
  hi_edited_fields?: string[]; // List of fields edited in Health Insurance
  hi_mobile_number?: boolean; // Mobile Number Added
  hi_record_added?: boolean; // Is document Uploaded

  EventName:
    | 'HealthInsuranceAddStarted' // Add button for Health Insurance clicked
    | 'HealthInsuranceInProgPolicyName' // Policy provider name selected
    | 'HealthInsuranceInProgMobileNumber' // Mobile number added
    | 'HealthInsuranceInProgPolicyNumber' // Policy Number added
    | 'HealthInsuranceAddCompleted' // Save button clicked after adding Health Insurance
    | 'HealthInsuranceEditStarted' // Edit button for Health Insurance clicked
    | 'HealthInsuranceEditCompleted' // Save button clicked after editing Health Insurance
    | 'HealthInsuranceRemoved' // Health Insurance removed
    | 'HealthInsuranceRecordAdded'; // Health Insurance Record Uploaded
}

// Record Health Insurance Events
export const recordHealthInsuranceEvents = (
  trackEventInFlow: (flow: AnalyticsFlow, eventName: AnalyticsEventName, data: Record<string, any>) => void,
  props: HealthInsuranceEventProps
) => {
  const analyticsData = {
    [EventPropsNames.FlowId]: 'HI', // Health Insurance Flow ID
    ...(props.hi_policy_provider && { [EventPropsNames.HealthInsurancePolicyProvider]: props.hi_policy_provider }),
    ...(props.hi_insurance_number !== undefined && {
      [EventPropsNames.HealthInsuranceNumber]: props.hi_insurance_number,
    }),
    ...(props.hi_edited_fields && { [EventPropsNames.HealthInsuranceEditedFields]: props.hi_edited_fields }),
    ...(props.hi_mobile_number !== undefined && { [EventPropsNames.HealthInsuranceMobile]: props.hi_mobile_number }),
    ...(props.hi_record_added !== undefined && {
      [EventPropsNames.HealthInsuranceRecordAdded]: props.hi_record_added,
    }),
  };

  trackEventInFlow(AnalyticsFlow.HealthInsuranceFlow, AnalyticsEventName[props.EventName], analyticsData);
};

// Emergency Contact Event Properties Interface
interface EmergencyContactEventProps {
  ec_first_name?: string; // Contact's first name entered by user
  ec_last_name?: string; // Contact's last name entered by user
  ec_relationship?: string; // Relationship of contact selected
  ec_mobile_number?: string; // Contact's mobile number entered
  ec_edited_fields?: string[]; // List of edited fields for contact

  EventName:
    | 'EmergencyContactAddStarted' // Add button for emergency contact clicked
    | 'EmergencyContactInProgFirstName' // Contact's first name added
    | 'EmergencyContactInProgLastName' // Contact's last name added
    | 'EmergencyContactInProgRelationship' // Relationship for contact selected
    | 'EmergencyContactInProgMobileNumber' // Contact's mobile number entered
    | 'EmergencyContactCompleted' // Save button clicked after adding contact
    | 'EmergencyContactEditStarted' // Edit button for contact clicked
    | 'EmergencyContactEditCompleted' // Save button clicked after editing contact
    | 'EmergencyContactRemoved'; // Contact removed
}
export const recordEmergencyContactEvents = (
  trackEventInFlow: (flow: AnalyticsFlow, eventName: AnalyticsEventName, data: Record<string, any>) => void,
  props: EmergencyContactEventProps
) => {
  const analyticsData = {
    [EventPropsNames.FlowId]: 'EC', // Emergency Contact Flow ID
    ...(props.ec_first_name && { [EventPropsNames.EmergencyContactFirstName]: props.ec_first_name }),
    ...(props.ec_last_name && { [EventPropsNames.EmergencyContactLastName]: props.ec_last_name }),
    ...(props.ec_relationship && { [EventPropsNames.EmergencyContactRelationship]: props.ec_relationship }),
    ...(props.ec_mobile_number && { [EventPropsNames.EmergencyContactMobileNumber]: props.ec_mobile_number }),
    ...(props.ec_edited_fields && { [EventPropsNames.EmergencyContactEditedFields]: props.ec_edited_fields }),
  };

  trackEventInFlow(AnalyticsFlow.EmergencyContactFlow, AnalyticsEventName[props.EventName], analyticsData);
};

// Additional Medical Decision Maker Event Properties Interface
interface AdditionalMedicalDecisionMakerEventProps {
  amdm_first_name?: string; // Proxy's first name entered by user
  amdm_last_name?: string; // Proxy's last name entered by user
  amdm_relationship?: string; // Relationship of proxy selected
  amdm_mobile_number?: string; // Whether the user added mobile number
  amdm_record_added?: boolean; // Count of files uploaded by user

  EventName:
    | 'AMDMAddStarted' // Add button for additional medical decision maker clicked
    | 'AMDMInProgFirstName' // Proxy's first name added
    | 'AMDMInProgLastName' // Proxy's last name added
    | 'AMDMInProgRelationship' // Relationship for proxy selected
    | 'AMDMInProgMobileNumber' // Proxy's mobile number entered
    | 'AMDMInProgRecordAdded' // File uploaded for proxy
    | 'AMDMAddCompleted' // Save button clicked after adding proxy
    | 'AMDMEditStarted' // Edit button for proxy clicked
    | 'AMDMEditCompleted' // Save button clicked after editing proxy
    | 'AMDMRemoved'; // Removed Additional Medical Decision Maker
}

// Record Additional Medical Decision Maker Events
export const recordAdditionalMedicalDecisionMakerEvents = (
  trackEventInFlow: (flow: AnalyticsFlow, eventName: AnalyticsEventName, data: Record<string, any>) => void,
  props: AdditionalMedicalDecisionMakerEventProps
) => {
  const analyticsData = {
    [EventPropsNames.FlowId]: 'AMDM', // Additional Medical Decision Maker Flow ID
    ...(props.amdm_first_name && { [EventPropsNames.AMDMFirstName]: props.amdm_first_name }),
    ...(props.amdm_last_name && { [EventPropsNames.AMDMLastName]: props.amdm_last_name }),
    ...(props.amdm_relationship && { [EventPropsNames.AMDMRelationship]: props.amdm_relationship }),
    ...(props.amdm_mobile_number !== undefined && { [EventPropsNames.AMDMMobileNumber]: props.amdm_mobile_number }),
    ...(props.amdm_record_added !== undefined && { [EventPropsNames.AMDMRecordAdded]: props.amdm_record_added }),
  };

  trackEventInFlow(
    AnalyticsFlow.AdditionalMedicalDecisionMakerFlow,
    AnalyticsEventName[props.EventName],
    analyticsData
  );
};

// Profile Picture Event Properties Interface
interface ProfilePictureEventProps {
  pp_action?: 'add' | 'edit'; // Type of action by user: add or edit
  pp_upload_error?: boolean; // Whether the user encountered an error (true/false)

  EventName:
    | 'ProfilePictureUploadStarted' // User clicks on upload/change picture
    | 'ProfilePictureUploadError' // User encounters picture size error
    | 'ProfilePictureUploadCompleted' // User clicks the save button
    | 'ProfilePictureRemoved'; // User deletes the profile picture
}

export const recordProfilePictureEvents = (
  trackEventInFlow: (flow: AnalyticsFlow, eventName: AnalyticsEventName, data: Record<string, any>) => void,
  props: ProfilePictureEventProps
) => {
  const analyticsData = {
    [EventPropsNames.FlowId]: 'PP', // Profile Picture Flow ID
    ...(props.pp_action && { [EventPropsNames.ProfilePictureAction]: props.pp_action }),
    ...(props.pp_upload_error !== undefined && { [EventPropsNames.ProfilePictureUploadError]: props.pp_upload_error }),
  };

  trackEventInFlow(AnalyticsFlow.ProfilePictureFlow, AnalyticsEventName[props.EventName], analyticsData);
};

interface FamilyMemberHistoryEventProps {
  fm_id?: string; // User ID of Relative added by user
  fm_relationship?: any; // Family Member's relationship entered by user
  fm_birthdate?: string; // Family Member's date of birth entered by user
  fm_status?: boolean | string; // Family Member's status selected
  fm_blood_group?: any; // Family Member's blood group entered
  fm_ethnicity?: any; // Family Member's ethnicity by user
  fm_condition?: any; // Family Member's ethnicity by user
  fm_store_health_records?: boolean; // Family Member's ethnicity by user
  fm_book_online_consults?: boolean; // Family Member's ethnicity by user
  fm_share_health_information?: boolean; // Family Member's ethnicity by user
  fm_keep_track_of_health_info?: boolean; // Family Member's ethnicity by user
  fm_other?: string; // Family Member's ethnicity by user
  fm_entry_point?: string; // Family Member's entry point

  // Family Member Condition properties
  fm_co_name?: string; // Condition FACT Code
  fm_co_chronicity?: string; // Selection of acute or chronic
  fm_co_date?: string; // Date condition was detected
  fm_co_shared?: boolean; // User share conditions
  fm_co_status?: boolean | string; // Whether or not user still has a condition
  fm_co_end_date?: string; // End date of condition
  fm_co_notes?: string; // Whether or not user added notes
  fm_co_records_added?: boolean; // Whether or not records added

  EventName:
    | 'FamilyMemberHistoryAddStarted' // Add button for family member clicked
    | 'FamilyMemberHistoryInProgRelationship' // Relationship for family member selected
    | 'FamilyMemberHistoryInProgBirthDate' // Birthdate for family member selected
    | 'FamilyMemberHistoryInProgStatus' // Family member's status added
    | 'FamilyMemberHistoryInProgBloodGroup' // Family member's blood group added
    | 'FamilyMemberHistoryInProgEthnicity' // Ethnicity for family member selected
    | 'FamilyMemberHistoryIntentSelected' // Intent Selected for family member selected
    | 'FamilyMemberHistoryStoreHealthRecords' // Store Health Records for family member selected
    | 'FamilyMemberHistoryBookOnlineConsults' // Book Online Consults for family member selected
    | 'FamilyMemberHistoryShareHealthInformation' // Share Health Information for family member selected
    | 'FamilyMemberHistoryKeepTrackOfHealthInfo' // Keep Track of Health Info for family member selected
    | 'FamilyMemberHistoryOther' // Other for family member entered
    | 'FamilyMemberAddCompleted' // Save button clicked after adding Family member
    | 'FamilyMemberEditStarted' // Edit button for Family member clicked
    | 'FamilyMemberEditCompleted' // Save button clicked after editing Family member
    | 'FamilyMemberHistoryRemoved' // Family member removed
    | 'FamilyMemberHistoryConditionInteracted' // Family member Condition interacted

    // Family Member Condition events
    | 'FMConditionAddStarted' // When a user clicks on the add button to add condition for family member
    | 'FMConditionAddInProgName' // User enters a condition
    | 'FMConditionAddInProgChronicity' // User selects between chronic and acute
    | 'FMConditionAddInProgDate' // User enters date the condition was detected
    | 'FMConditionAddInProgShared' // User selects share condition
    | 'FMConditionAddInProgStatus' // When user interacts with the toggle
    | 'FMConditionAddInProgEndDate' // When user enters the end date of condition
    | 'FMConditionAddInProgNotes' // User adds details of condition entered
    | 'FMConditionAddInProgRecordsAdded' // User links health records
    | 'FMConditionAddCompleted' // When user clicks on save
    | 'FMConditionInteracted' // When user clicks on added condition
    | 'FMConditionEditStarted' // When user clicks to edit condition added for family member
    | 'FMConditionEditCompleted' // When user edits any of the field and clicks on save
    | 'FMConditionRemoved'; // When user removes condition
}

export const recordFamilyMemberHistoryEvents = (
  trackEventInFlow: (flow: AnalyticsFlow, eventName: AnalyticsEventName, data: Record<string, any>) => void,
  props: FamilyMemberHistoryEventProps
) => {
  const analyticsData = {
    [EventPropsNames.FlowId]: 'FM', // Family Member Flow ID
    ...(props.fm_id && { [EventPropsNames.FamilyMemberID]: props.fm_id }),
    ...(props.fm_entry_point && { [EventPropsNames.FamilyMemberHistoryEntryPoint]: props.fm_entry_point }),
    ...(props.fm_relationship && { [EventPropsNames.FamilyMemberHistoryRelationship]: props.fm_relationship }),
    ...(props.fm_birthdate && { [EventPropsNames.FamilyMemberHistoryBirthDate]: props.fm_birthdate }),
    ...(props.fm_status && { [EventPropsNames.FamilyMemberHistoryStatus]: props.fm_status }),
    ...(props.fm_blood_group && { [EventPropsNames.FamilyMemberHistoryBloodGroup]: props.fm_blood_group }),
    ...(props.fm_ethnicity && { [EventPropsNames.FamilyMemberHistoryEthnicity]: props.fm_ethnicity }),
    ...(props.fm_condition && { [EventPropsNames.FamilyMemberHistoryCondition]: props.fm_condition }),
    ...(props.fm_store_health_records !== undefined && {
      [EventPropsNames.FamilyMemberHistoryStoreHealthRecords]: props.fm_store_health_records,
    }),
    ...(props.fm_book_online_consults !== undefined && {
      [EventPropsNames.FamilyMemberHistoryBookOnlineConsults]: props.fm_book_online_consults,
    }),
    ...(props.fm_share_health_information !== undefined && {
      [EventPropsNames.FamilyMemberHistoryShareHealthInformation]: props.fm_share_health_information,
    }),
    ...(props.fm_keep_track_of_health_info !== undefined && {
      [EventPropsNames.FamilyMemberHistoryKeepTrackOfHealthInfo]: props.fm_keep_track_of_health_info,
    }),
    ...(props.fm_other && { [EventPropsNames.FamilyMemberHistoryOther]: props.fm_other }),

    // Family Member Condition properties
    ...(props.fm_co_name && { [EventPropsNames.FamilyMemberConditionName]: props.fm_co_name }),
    ...(props.fm_co_chronicity && { [EventPropsNames.FamilyMemberConditionChronicity]: props.fm_co_chronicity }),
    ...(props.fm_co_date && { [EventPropsNames.FamilyMemberConditionDate]: props.fm_co_date }),
    ...(props.fm_co_shared !== undefined && { [EventPropsNames.FamilyMemberConditionShared]: props.fm_co_shared }),
    ...(props.fm_co_status !== undefined && { [EventPropsNames.FamilyMemberConditionStatus]: props.fm_co_status }),
    ...(props.fm_co_end_date && { [EventPropsNames.FamilyMemberConditionEndDate]: props.fm_co_end_date }),
    ...(props.fm_co_notes !== undefined && { [EventPropsNames.FamilyMemberConditionNotes]: props.fm_co_notes }),
    ...(props.fm_co_records_added !== undefined && {
      [EventPropsNames.FamilyMemberConditionRecordsAdded]: props.fm_co_records_added,
    }),
  };

  trackEventInFlow(AnalyticsFlow.FamilyMemberHistoryFlow, AnalyticsEventName[props.EventName], analyticsData);
};

interface AddNewRecordProps {
  ar_entry_point: string;
  EventName: 'AddNewRecordInteracted';
}

export const recordAddnewRecordsEvents = (
  trackEventInFlow: (flow: AnalyticsFlow, eventName: AnalyticsEventName, data: Record<string, any>) => void,
  props: AddNewRecordProps
) => {
  const analyticsData = {
    [EventPropsNames.FlowId]: 'RI', // Profile Picture Flow ID
    [EventPropsNames.AddNewRecordEntryPoint]: props.ar_entry_point, // Profile Picture Flow ID
  };

  trackEventInFlow(AnalyticsFlow.RecordFlow, AnalyticsEventName[props.EventName], analyticsData);
};

// Reproductive Health Event Properties Interface
interface ReproductiveHealthEventProps {
  rh_entry_point?: string;
  rh_sexually_active?: string; // Whether or not the user is sexually active (yes, no)
  rh_last_menstrual_date?: string; // Date entered by user (yyyy/mm/dd)
  rh_pregnancy_history?: string; // Option selected by user
  rh_pregnancy_terminated?: string; // Option selected by user
  rh_children?: boolean; // Whether or not one has children (yes, no)
  rh_miscarriage?: string; // Option selected by user
  rh_fertility_treatments?: boolean; // Option selected by user (yes, no)
  rh_pregnancy_status?: string; // Option selected by user

  EventName:
    | 'ReproductiveHealthAddStarted' // When user clicks on Reproductive health for first time when its in empty state
    | 'ReproductiveHealthAddInProgSexuallyActive' // When the user selects whether, or not one is sexually active
    | 'ReproductiveHealthAddInProgMenstrualDate' // When user enters last menstrual cycle date
    | 'ReproductiveHealthAddInProgPregnancyHistory' // When the user selects the option for pregnancy status
    | 'ReproductiveHealthAddInProgPregnancyTerminate' // When user selects option for pregnancy termination
    | 'ReproductiveHealthAddInProgChildren' // When the user selects whether, or not one has children
    | 'ReproductiveHealthAddInProgMiscarriage' // When user selects option for miscarriage
    | 'ReproductiveHealthAddInProgFertilityTreatments' // When user selects option for fertility treatments
    | 'ReproductiveHealthAddInProgPregnancyStatus' // When user selects option for current pregnancy status
    | 'ReproductiveHealthAddCompleted' // When user clicks on save after adding fields
    | 'ReproductiveHealthInteracted' // When user clicks on filled reproductive health tab
    | 'ReproductiveHealthEdited' // When user makes changes to any of the fields and clicks on save
    | 'ReproductiveHealthRemoved'; // When user clicks on clear all
}

export const recordReproductiveHealthEvents = (
  trackEventInFlow: (flow: AnalyticsFlow, eventName: AnalyticsEventName, data: Record<string, any>) => void,
  props: ReproductiveHealthEventProps
) => {
  const analyticsData = {
    [EventPropsNames.FlowId]: 'RH', // Reproductive Health Flow ID
    ...(props.rh_entry_point && { [EventPropsNames.ReproductiveHealthEntryPoint]: props.rh_entry_point }),
    ...(props.rh_sexually_active && { [EventPropsNames.ReproductiveHealthSexuallyActive]: props.rh_sexually_active }),
    ...(props.rh_last_menstrual_date && {
      [EventPropsNames.ReproductiveHealthLastMenstrualDate]: props.rh_last_menstrual_date,
    }),
    ...(props.rh_pregnancy_history && {
      [EventPropsNames.ReproductiveHealthPregnancyHistory]: props.rh_pregnancy_history,
    }),
    ...(props.rh_pregnancy_terminated && {
      [EventPropsNames.ReproductiveHealthPregnancyTerminated]: props.rh_pregnancy_terminated,
    }),
    ...(props.rh_children !== undefined && { [EventPropsNames.ReproductiveHealthChildren]: props.rh_children }),
    ...(props.rh_miscarriage && { [EventPropsNames.ReproductiveHealthMiscarriage]: props.rh_miscarriage }),
    ...(props.rh_fertility_treatments !== undefined && {
      [EventPropsNames.ReproductiveHealthFertilityTreatments]: props.rh_fertility_treatments,
    }),
    ...(props.rh_pregnancy_status && {
      [EventPropsNames.ReproductiveHealthPregnancyStatus]: props.rh_pregnancy_status,
    }),
  };

  trackEventInFlow(AnalyticsFlow.ReproductiveHealthFlow, AnalyticsEventName[props.EventName], analyticsData);
};

// Interface for  Health Profile Basic Info Event Props
interface HealthProfileBasicInfoProps {
  height?: string; // height
  patientId: string; // patientId
  weight?: string; // weight
  bloodgroup?: string; // bloodgroup
  sab?: string; // sab
  ethnicity?: string; // ethnicity
  gender?: string; // gender
  language?: string; // language
  EventName: 'HealthProfileBasicInfoAdded' | 'HealthProfileBasicInfoEdited';
}
// Function to Health Profile Basic Info Events
export const healthProfileBasicInfoEvents = (
  trackEventInFlow: (flow: AnalyticsFlow, eventName: AnalyticsEventName, data: Record<string, any>) => void,
  props: HealthProfileBasicInfoProps
) => {
  const analyticsData: Record<string, any> = {
    [EventPropsNames.FlowId]: 'HPBI', // Health Profile Basic Info flow identifier
    ...(props.height && { [EventPropsNames.height]: props.height }),
    ...(props.weight && { [EventPropsNames.weight]: props.weight }),
    ...(props.bloodgroup && { [EventPropsNames.bloodgroup]: props.bloodgroup }),
    ...(props.sab && { [EventPropsNames.sab]: props.sab }),
    ...(props.ethnicity && { [EventPropsNames.ethnicity]: props.ethnicity }),
    ...(props.gender && { [EventPropsNames.gender]: props.gender }),
    ...(props.language && { [EventPropsNames.language]: props.language }),
  };
  trackEventInFlow(AnalyticsFlow.HealthProfileBasicInfo, AnalyticsEventName[props.EventName], analyticsData);
  identifyHealthProfileUser(props.EventName, {
    ...(props.height && { [EventPropsNames.height]: props.height }),
    ...(props.weight && { [EventPropsNames.weight]: props.weight }),
    ...(props.bloodgroup && { [EventPropsNames.bloodgroup]: props.bloodgroup }),
    ...(props.sab && { [EventPropsNames.sab]: props.sab }),
    ...(props.ethnicity && { [EventPropsNames.ethnicity]: props.ethnicity }),
    ...(props.gender && { [EventPropsNames.gender]: props.gender }),
    ...(props.language && { [EventPropsNames.language]: props.language }),
  });
};
interface ReminderSectionProps {
  rm_entry_point?: string | null;
  rm_name?: string | null;
  rm_date?: string | null;
  rm_all_day?: string | null;
  rm_time?: string | null;
  rm_frequency?: string | null;
  rm_notes?: string | null;

  EventName:
    | 'ReminderAddStarted'
    | 'ReminderAddInProgName'
    | 'ReminderAddInProgDate'
    | 'ReminderAddInProgAllDay'
    | 'ReminderAddInProgTime'
    | 'ReminderAddInProgFrequency'
    | 'ReminderAddInProgNotes'
    | 'ReminderAddCompleted'
    | 'ReminderMarkedCompleted'
    | 'ReminderEditStarted'
    | 'ReminderEditCompleted'
    | 'ReminderRemoved';
}

export const recordReminderEvents = (
  trackEventInFlow: (flow: AnalyticsFlow, eventName: AnalyticsEventName, data: any) => void,
  props: ReminderSectionProps
) => {
  const analyticsData: any = {
    [EventPropsNames.FlowId]: 'RM',
    ...(props.rm_entry_point && { [EventPropsNames.ReminderEntryPoint]: props.rm_entry_point }),
    ...(props.rm_name && { [EventPropsNames.ReminderName]: props.rm_name }),
    ...(props.rm_date && { [EventPropsNames.ReminderDate]: props.rm_date }),
    ...(props.rm_all_day && { [EventPropsNames.ReminderAllDay]: props.rm_all_day }),
    ...(props.rm_time && { [EventPropsNames.ReminderTime]: props.rm_time }),
    ...(props.rm_frequency && { [EventPropsNames.ReminderFrequency]: props.rm_frequency }),
    ...(props.rm_notes && { [EventPropsNames.ReminderNotes]: props.rm_notes }),
  };
  trackEventInFlow(AnalyticsFlow.ReminderFlow, AnalyticsEventName[props.EventName], analyticsData);
};
