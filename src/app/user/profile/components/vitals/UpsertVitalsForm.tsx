import {
  Box,
  Flex,
  FormControl,
  FormErrorMessage,
  FormLabel,
  Input,
  InputGroup,
  InputRightElement,
  useDisclosure,
  useTheme,
} from '@chakra-ui/react';
import dayjs from 'dayjs';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import { Clock as ClockIcon } from 'react-feather';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { VITALS } from '@user/lib/constants';
import { VitalsEventProps, recordVitalsEvents } from '@user/lib/events-analytics-manager';

import { IModal } from 'src/components/Modal';
import { DatePickerField, SuggestionDropdown } from 'src/components/ui/Form';
import { TimeOfRecordingOption } from '@lib/models/misc';
import { HOURLY_BASE_OPTIONS } from '@lib/constants';
import { useAnalyticsService } from '@lib/state';

const VALIDATION = {
  required: true,
  minLength: 1,
  maxLength: 40,
  validate: (value: string) => !!value.trim(),
};
const EventField = (mainFieldName: string) => {
  switch (mainFieldName) {
    case VITALS.BodyTemperature.key:
      return {
        PrimaryEvent: 'VTBTAddInProgTemparature',
        DateEvent: 'VTBTAddInProgDate',
        TimeEvent: 'VTBTAddInProgTime',
        PrimaryKey: 'vt_bt_temparature',
        DateKey: 'vt_bt_date',
        TimeKey: 'vt_bt_time',
      };
    case VITALS.PulseRate.key:
      return {
        PrimaryKey: 'vt_pr_rate',
        DateEvent: 'VTPRAddInProgDate',
        TimeEvent: 'VTPRAddInProgTime',
        PrimaryEvent: 'VTPRAddInProgRate',
        DateKey: 'vt_pr_date',
        TimeKey: 'vt_pr_time',
      };
    case VITALS.OxygenSaturationLevel.key:
      return {
        PrimaryEvent: 'VTOSLAddInProgLevel',
        DateEvent: 'VTOSLAddInProgDate',
        TimeEvent: 'VTOSLAddInProgTime',
        PrimaryKey: 'vt_osl_level',
        DateKey: 'vt_osl_date',
        TimeKey: 'vt_osl_time',
      };
    case VITALS.RespiratoryRate.key:
      return {
        PrimaryEvent: 'VTRRAddInProgRate',
        DateEvent: 'VTRRAddInProgDate',
        TimeEvent: 'VTRRAddInProgTime',
        PrimaryKey: 'vt_rr_rate',
        DateKey: 'vt_rr_date',
        TimeKey: 'vt_rr_time',
      };
    default:
      return {};
  }
};
export const TIME_OF_RECORDING_BASE_OPTIONS = HOURLY_BASE_OPTIONS.map((el) => ({
  ...el,
  hideIcon: true,
})) as TimeOfRecordingOption[];

interface IUpsertVitalsModal {
  mainFieldName: string;
  mainFieldLabel: string;
  selectedVital: any;
  myFormState: 'Add' | 'Edit';
  onClose?: () => void;
  onSubmit?: (formValues: any) => void;
  setModalState: (modalState: IModal) => void;
  minReqValue?: number;
  maxReqValue?: number;
}

const getDefaultValues = (mainFieldName: string, selectedVital: any) => {
  const vitalCode = Object.values(VITALS).find((vital) => vital.key === mainFieldName)?.code;
  const isCurrentVital = selectedVital?.code?.coding?.some((coding: { code?: string }) => coding?.code === vitalCode);
  const value = isCurrentVital ? selectedVital?.valueQuantity?.value?.toString() : undefined;
  const effectiveDateTime = isCurrentVital ? selectedVital?.effectiveDateTime : null;
  const dateOfRecording = effectiveDateTime ? dayjs(effectiveDateTime).format('YYYY-MM-DD') : null;
  const timeOfRecording = effectiveDateTime ? dayjs(effectiveDateTime).format('HH:mm:ss') : null;

  return {
    [mainFieldName]: value,
    dateOfRecording,
    timeOfRecording: timeOfRecording ? dayjs(timeOfRecording, 'HH:mm:ss').format('HH:mm') : '',
  };
};

export function UpsertVitalsForm({
  selectedVital,
  mainFieldName,
  mainFieldLabel,
  onSubmit,
  setModalState,
  minReqValue,
  maxReqValue,
}: IUpsertVitalsModal) {
  const datePickerPopover = useDisclosure();
  const theme = useTheme();
  const { trackEventInFlow } = useAnalyticsService();
  const [formChanged, setFormChanged] = useState(false);
  const [originalValues, setOriginalValues] = useState<any>(null);

  const [timeOfRecordingOptions, setTimeOfRecordingOptions] = useState(TIME_OF_RECORDING_BASE_OPTIONS);
  const defaultValue = useMemo(() => getDefaultValues(mainFieldName, selectedVital), [mainFieldName, selectedVital]);

  useEffect(() => {
    if (selectedVital) {
      setOriginalValues(defaultValue);
    }
  }, [selectedVital]);

  const form = useForm({
    mode: 'onChange',
    defaultValues: defaultValue,
    resolver: zodResolver(
      z.object({
        [mainFieldName]: z.preprocess(
          (val) => {
            if (val === undefined || val === null || val === '') return NaN;
            return Number(val);
          },
          z.number().refine(
            (num) => {
              if (Number.isNaN(num)) return false;
              if (mainFieldName === 'bodyTemperature' && num >= 30 && num <= 112) {
                return !(num >= 46 && num <= 85);
              }
              return (
                (minReqValue === undefined || num >= minReqValue) && (maxReqValue === undefined || num <= maxReqValue)
              );
            },
            {
              message:
                minReqValue !== undefined && maxReqValue !== undefined
                  ? `Please input valid data`
                  : `${mainFieldLabel} is required.`,
            }
          )
        ),
        dateOfRecording: z.string().min(1, 'Date is required'),
        timeOfRecording: z.string().regex(/^(?:2[0-3]|[01][0-9]):[0-5][0-9]$/, 'Time should be in HH:mm format'),
      })
    ),
  });

  const {
    handleSubmit,
    register,
    formState: { isSubmitting, isValid, isDirty, errors },
    watch,
  } = form;

  const dateField = watch('dateOfRecording');
  const timeField = watch('timeOfRecording');
  const mainField = watch(mainFieldName);

  const saveButtonEnabled = useMemo((): boolean => {
    if (!isValid) return false;

    const fieldsFilled =
      mainField !== undefined && mainField !== null && !Number.isNaN(Number(mainField)) && !!dateField && !!timeField;

    const shouldEnable = selectedVital ? formChanged : isDirty;

    return Boolean(fieldsFilled && shouldEnable);
  }, [isValid, isDirty, formChanged, selectedVital, mainField, dateField, timeField]);

  useEffect(() => {
    if (!selectedVital || !originalValues) return;

    const currentValues = {
      [mainFieldName]: mainField,
      dateOfRecording: dateField,
      timeOfRecording: timeField,
    };

    const hasChanged =
      originalValues[mainFieldName] !== currentValues[mainFieldName] ||
      originalValues.dateOfRecording !== currentValues.dateOfRecording ||
      originalValues.timeOfRecording !== currentValues.timeOfRecording;

    setFormChanged(hasChanged);
  }, [mainField, dateField, timeField, selectedVital, originalValues]);

  const formSubmitHandler = useCallback(
    async (values: any) => {
      const value = selectedVital ? { ...selectedVital, ...values } : values;

      await onSubmit?.(value);
      form.reset();
    },
    [selectedVital, onSubmit]
  );

  useEffect(() => {
    setModalState({
      onPrimaryButtonClick: handleSubmit(formSubmitHandler),
      primaryButtonEnabled: saveButtonEnabled,
      isPrimaryButtonLoading: isSubmitting,
    });
  }, [saveButtonEnabled, isSubmitting]);

  const datePickerChangeHandler = useCallback(
    (date: Date | null) => {
      const value = dayjs(date).isValid() ? dayjs(date).format('YYYY-MM-DD') : '';

      form.setValue('dateOfRecording', value, {
        shouldDirty: true,
        shouldTouch: true,
        shouldValidate: true,
      });

      if (!selectedVital && value) {
        recordVitalsEvents(trackEventInFlow, {
          EventName: EventField(mainFieldName).DateEvent as VitalsEventProps['EventName'],
          [EventField(mainFieldName).DateKey as string]: dayjs(date).format('YYYY/MM/DD'),
        });
      }

      datePickerPopover.onClose();
    },
    [selectedVital]
  );

  const datePickerClearHandler = useCallback(() => {
    form.setValue('dateOfRecording', '', {
      shouldDirty: true,
      shouldTouch: true,
      shouldValidate: true,
    });
    datePickerPopover.onClose();
  }, []);

  const onTimeOfRecordingSelect = useCallback(
    (value: TimeOfRecordingOption) => {
      form.setValue('timeOfRecording', value.value, {
        shouldDirty: true,
        shouldTouch: true,
        shouldValidate: true,
      });

      if (!selectedVital) {
        recordVitalsEvents(trackEventInFlow, {
          EventName: EventField(mainFieldName).TimeEvent as VitalsEventProps['EventName'],
          [EventField(mainFieldName).TimeKey as string]: value?.value,
        });
      }
    },
    [selectedVital]
  );

  const onTimeOfRecordingSearchChange = useCallback((searchText: string) => {
    const newList = TIME_OF_RECORDING_BASE_OPTIONS.filter((option) => option.value.includes(searchText));
    setTimeOfRecordingOptions(newList);
  }, []);

  return (
    <FormProvider {...form}>
      <Box
        color="black"
        display="flex"
        flexDirection="column"
        mt="24px"
        gap="14"
        mx="2"
      >
        <FormControl
          variant="floating"
          isInvalid={!!errors[mainFieldName]}
        >
          <Input
            placeholder=" "
            type="number"
            {...register(mainFieldName, VALIDATION)}
            onBlur={(e) => {
              if (!errors[mainFieldName] && !selectedVital) {
                recordVitalsEvents(trackEventInFlow, {
                  EventName: EventField(mainFieldName).PrimaryEvent as VitalsEventProps['EventName'],
                  [EventField(mainFieldName).PrimaryKey as string]: e.target.value,
                });
              }
            }}
          />
          <FormLabel>{mainFieldLabel}</FormLabel>
          <FormErrorMessage>
            {typeof errors[mainFieldName]?.message === 'string'
              ? 'Please enter a valid input'
              : 'This field is required'}
          </FormErrorMessage>
        </FormControl>

        <Flex
          justifyContent="space-between"
          gap="6"
          mb="10"
        >
          <DatePickerField
            name="dateOfRecording"
            labelText="Date*"
            errorText={errors.dateOfRecording?.message || 'This field is required'}
            datePickerChangeHandler={datePickerChangeHandler}
            datePickerClearHandler={datePickerClearHandler}
            datePickerPopover={datePickerPopover}
            isClearDateButtonDisabled={!dateField}
            selected={dayjs(dateField).isValid() ? dayjs(dateField).toDate() : null}
            maxDate={new Date()}
          />

          <Box
            width="100%"
            position="relative"
          >
            <FormControl
              variant="floating"
              isInvalid={!!errors.timeOfRecording}
            >
              <SuggestionDropdown
                options={timeOfRecordingOptions}
                textValue={timeField}
                onSelect={onTimeOfRecordingSelect}
                onChange={onTimeOfRecordingSearchChange}
                onClear={() => {}}
                keepOpenAfterBlur={false}
                resetTextValueAfterBlur={false}
                debounceDelay={100}
                isFreeInput
              >
                {({ searchInputChangeHandler, searchInputBlueHandler, suggestionDropdownPopover }) => (
                  <InputGroup display="block">
                    <Input
                      placeholder=" "
                      {...register('timeOfRecording', {
                        onChange: searchInputChangeHandler,
                        onBlur: searchInputBlueHandler,
                      })}
                    />
                    <FormLabel fontWeight="normal">Time*</FormLabel>
                    <FormErrorMessage>{errors.timeOfRecording?.message as React.ReactNode}</FormErrorMessage>
                    <InputRightElement
                      w="36px"
                      h="36px"
                      cursor="pointer"
                      _hover={{ '& ~ input': { borderColor: 'fluentHealth.500' } }}
                      onClick={() => {
                        setTimeOfRecordingOptions(TIME_OF_RECORDING_BASE_OPTIONS);
                        suggestionDropdownPopover.onOpen();
                      }}
                    >
                      <ClockIcon
                        size={18}
                        color={theme.colors.papaya[600]}
                      />
                    </InputRightElement>
                  </InputGroup>
                )}
              </SuggestionDropdown>
            </FormControl>
          </Box>
        </Flex>
      </Box>
    </FormProvider>
  );
}
