import React, { useCallback, useEffect, useRef, useState } from 'react';
import { But<PERSON>, Divider, Flex, Text, VStack, useToast } from '@chakra-ui/react';
import { SelectInstance } from 'react-select/dist/declarations/src';
import { QuestionnaireResponsePayload } from '@user/lib/models/questionnaire-response';
import { QuestionnaireResponse } from '@gql/graphql';
import { LifestyleNutritionEventProps, recordLifestyleNutritionEvents } from '@user/lib/events-analytics-manager';

import { getValueSetByMasterList } from '@lib/utils/utils';
import { useAnalyticsService, useAuthService, usePublicSettings } from '@lib/state';
import { MasterLifestyleQuestion } from '@lib/models/lifestyle-and-nutrition';
import { Select, SelectOptionProps } from 'src/components/ui/Select';
import { Card, QuestionCTA } from '../SidebarComponents';
import { LIFESTYLE_QUESTION_FIELD_TYPES, LIFESTYLE_QUESTION_FIELD_TYPE_MAP } from '../../../lib/state';
import { useMasterQuestionnaireList, useMasterQuestionnaireResponseList } from 'src/app/user/lib/medplum-state';
import { SidebarEmptyState } from '../SidebarEmptyState';
import { MEDPLUM_QUESTIONNAIRE } from '@lib/constants';

const eventMapping: Record<string, { EventName: any; key: string }> = {
  'health-sleep-personal-history-mental-health-conditions': {
    EventName: 'LNMHSAddInProgMentalCondition',
    key: 'mental_condition',
  },
  'health-sleep-family-history-mental-health-conditions': {
    EventName: 'LNMHSAddInProgFamilyHistory',
    key: 'family_history',
  },
  'lifestyle-nutrition-alcohol-tobacco-caffeine-alcoholic-drinks-per-week': {
    EventName: 'LNATCAddInProgAlcohol',
    key: 'atc_alcohol',
  },
  'feel-burned-out': { EventName: 'LNMHSAddInProgBurntOut', key: 'burnt_out' },
  'hours-do-you-sleep': { EventName: 'LNMHSAddInProgSleep', key: 'sleep_duration' },
  'generally-feel-you-wake-up': { EventName: 'LNMHSAddInProgMorningFeel', key: 'morning_feel' },
  'average-level-stress': { EventName: 'LNMHSAddInProgStressLevel', key: 'stress_level' },
  'your-occupation': { EventName: 'LNOccupationAddInProgName', key: 'occupation' },
  'required-extended-periods': { EventName: 'LNOccupationAddInProgExtendedHours', key: 'extended_hours' },
  'smoke-tobacco': { EventName: 'LNATCAddInProgTobacco', key: 'atc_tobacco' },
  beverages: { EventName: 'LNATCAddInProgCaffeine', key: 'atc_caffeine' },
  routine: { EventName: 'LNExerciseAddInProgFitnessRoutine', key: 'activity' },
  get: { EventName: 'LNExerciseAddInProgFrequency', key: 'frequency' },
  week: { EventName: 'LNExerciseAddInProgIntenseExercise', key: 'intense_exercise' },
  activities: { EventName: 'LNExerciseAddInProgActivity', key: 'activity' },
  describe: { EventName: 'LNDietAddInProgType', key: 'diet_type' },
  preferences: { EventName: 'LNDietAddInProgPreferences', key: 'diet_preference' },
};
const masterScreenAddStarted: Record<string, LifestyleNutritionEventProps['EventName']> = {
  LifestyleNutritionExercise: 'LNExerciseAddStarted',
  LifestyleNutritionDiet: 'LNDietAddStarted',
  LifestyleNutritionOccupation: 'LNOccupationAddStarted',
  LifestyleNutritionMentalHealthSleep: 'LNMHSAddStarted',
  LifestyleNutritionAlcoholTobaccoCaffeine: 'LNATCAddStarted',
};
const masterScreenInteracted: Record<string, LifestyleNutritionEventProps['EventName']> = {
  LifestyleNutritionExercise: 'LNExerciseInteracted',
  LifestyleNutritionDiet: 'LNDietInteracted',
  LifestyleNutritionOccupation: 'LNOccupationInteracted',
  LifestyleNutritionMentalHealthSleep: 'LNMHSInteracted',
  LifestyleNutritionAlcoholTobaccoCaffeine: 'LNATCInteracted',
};
const masterScreen: Record<string, LifestyleNutritionEventProps['EventName']> = {
  LifestyleNutritionExercise: 'LNExerciseAddCompleted',
  LifestyleNutritionDiet: 'LNDietAddCompleted',
  LifestyleNutritionOccupation: 'LNOccupationAddCompleted',
  LifestyleNutritionMentalHealthSleep: 'LNMHSAddCompleted',
  LifestyleNutritionAlcoholTobaccoCaffeine: 'LNATCAddCompleted',
};
const masterScreenEdited: Record<string, LifestyleNutritionEventProps['EventName']> = {
  LifestyleNutritionExercise: 'LNExerciseEdited',
  LifestyleNutritionDiet: 'LNDietEdited',
  LifestyleNutritionOccupation: 'LNOccupationEdited',
  LifestyleNutritionMentalHealthSleep: 'LNMHSEdited',
  LifestyleNutritionAlcoholTobaccoCaffeine: 'LNATCEdited',
};
const masterScreenRemoved: Record<string, LifestyleNutritionEventProps['EventName']> = {
  LifestyleNutritionExercise: 'LNExerciseRemoved',
  LifestyleNutritionDiet: 'LNDietRemoved',
  LifestyleNutritionOccupation: 'LNOccupationRemoved',
  LifestyleNutritionMentalHealthSleep: 'LNMHSRemoved',
  LifestyleNutritionAlcoholTobaccoCaffeine: 'LNATCRemoved',
};
const entryPointProps: Record<string, string> = {
  LifestyleNutritionExercise: 'ln_exercise_entry_point',
  LifestyleNutritionDiet: 'ln_diet_entry_point',
  LifestyleNutritionOccupation: 'ln_occupation_entry_point',
  LifestyleNutritionMentalHealthSleep: 'ln_mhs_entry_point',
  LifestyleNutritionAlcoholTobaccoCaffeine: 'ln_atc_entry_point',
};
const parseSelectValueFromString = (value: any, isMulti: boolean, selectOptions: any[]) => {
  // console.log('Parse', value, isMulti);
  if (value && value.length > 0) {
    return isMulti
      ? value.map((valX: any) => {
          return {
            label:
              valX?.valueCoding?.display ||
              valX?.label ||
              selectOptions.filter((x) => x.value === valX?.value)[0]?.label ||
              (valX?.valueCoding?.code && selectOptions.filter((x) => x.value === valX?.valueCoding?.code)[0]?.label) ||
              (valX?.label && selectOptions.filter((x) => x.value === valX?.label)[0]?.label) ||
              (valX?.valueCoding?.display &&
                selectOptions.filter((x) => x.value === valX?.valueCoding?.display)[0]?.label),
            value:
              valX?.valueCoding?.code ||
              valX?.value ||
              (valX?.label && selectOptions.filter((x) => x.value === valX?.label)[0]?.value) ||
              (valX?.valueCoding?.display &&
                selectOptions.filter((x) => x.value === valX?.valueCoding?.display)[0]?.value),
          };
        })
      : {
          value:
            value[0]?.valueCoding?.code ||
            value?.value ||
            selectOptions.filter((x) => x.label === value[0]?.valueCoding?.display)[0]?.value ||
            selectOptions.filter((x) => x.label === value?.display)[0]?.value,
          label:
            value[0]?.valueCoding?.display ||
            value?.label ||
            selectOptions.filter((x) => x.value === value[0]?.valueCoding?.code)[0]?.label ||
            selectOptions.filter((x) => x.value === value?.value)[0]?.label,
        };
  }
  return '';
};

function QuestionItem({
  question,
  answer,
  masterList,
  formatPayloadArr,
  isPublicMode = false,
}: {
  question: MasterLifestyleQuestion;
  answer?: any;
  masterList: any;
  formatPayloadArr: (questData: any, data: any, isMulti: boolean) => void;
  isPublicMode: boolean;
}) {
  const [isCreatingAnswerState, setIsCreatingAnswerState] = useState(false);
  const [error, setError] = useState('');
  const selectRef = useRef<SelectInstance | null>(null);

  // Get current answer value
  const currentAnswerValue = answer?.answer || answer || [];

  // Get the field type. Currently only single/multiple selection is supported.
  const fieldType = LIFESTYLE_QUESTION_FIELD_TYPE_MAP[question?.linkId] ?? LIFESTYLE_QUESTION_FIELD_TYPES.select;
  // Check if it should be multiple select or single select
  const isMulti = fieldType === LIFESTYLE_QUESTION_FIELD_TYPES.multiSelect;

  // Get the default value. If it's a multiple select, create a list of selected options,
  // otherwise create one selected option
  const [fieldValue, setFieldValue] = useState<SelectOptionProps | SelectOptionProps[] | undefined>();

  const memoValueSet = useCallback(
    (mastList: any, urlVal: string) => getValueSetByMasterList(mastList, urlVal),
    [question.answerValueSet]
  );

  // Format answers into a list of options
  const selectOptions =
    question?.answerOption?.map((item: any) => ({
      value: item.id,
      label: item.valueString,
    })) || memoValueSet(masterList, question.answerValueSet);

  useEffect(() => {
    if (currentAnswerValue) {
      // console.log('currentAnswerValue', currentAnswerValue);
      setFieldValue(parseSelectValueFromString(currentAnswerValue, isMulti, selectOptions));
    }
  }, [currentAnswerValue]);

  const handleAnswerChange = (newValue: SelectOptionProps | SelectOptionProps[] | any) => {
    setFieldValue(newValue);
    formatPayloadArr(question, newValue, isMulti);
  };

  // Reset the select value only if the current value is an empty array
  const resetValueOnBlur = () => {
    if (Array.isArray(fieldValue) && fieldValue.length === 0) {
      setFieldValue(parseSelectValueFromString(answer!.answer, isMulti, selectOptions));
      setError('');
    }

    if (isCreatingAnswerState) {
      setIsCreatingAnswerState(false);
    }
  };

  const handleStartCreatingAnswer = async () => {
    setIsCreatingAnswerState(true);
  };

  return (
    <Flex
      direction="column"
      width="full"
    >
      {(currentAnswerValue && currentAnswerValue.length > 0) || isCreatingAnswerState ? (
        <>
          <Text color="gray.400">{question.text}</Text>
          <Select
            ref={selectRef}
            labelText="Select"
            value={fieldValue}
            hideLabelWhenSelectedValue
            closeMenuOnSelect={!isMulti}
            isMulti={isMulti}
            isClearable={false}
            isSearchable={isMulti}
            isLoading={false}
            openMenuOnFocus
            {...(isPublicMode
              ? { isDisabled: true }
              : {
                  options: selectOptions,
                  onChange: handleAnswerChange,
                  onClickOutside: resetValueOnBlur,
                  onBlur: resetValueOnBlur,
                })}
          />
          {error.length > 0 && (
            <Text
              mt="4px"
              fontSize="sm"
              color="fluentHealthComplementary.Red"
            >
              {error}
            </Text>
          )}
        </>
      ) : (
        <>
          <QuestionCTA
            text={question.text}
            pr="10px"
            hideIcon={isPublicMode}
            iconColor="#FF6333"
            {...(isPublicMode ? {} : { onClick: handleStartCreatingAnswer })}
          />
          <Divider mt="12px" />
        </>
      )}
    </Flex>
  );
}

export function QuestionList({ selectedLifestyleCategory }: any) {
  const toast = useToast();
  const { authenticatedUser } = useAuthService();
  const { masterList }: any = useMasterQuestionnaireList(`${MEDPLUM_QUESTIONNAIRE}/${selectedLifestyleCategory.name}`);
  const {
    answerList,
    addQuestionnaireResponse,
    updateQuestionnaireResponse,
    deleteQuestionnaireResponseTask,
    isLoading,
  }: {
    answerList: QuestionnaireResponse[];
    addQuestionnaireResponse: any;
    updateQuestionnaireResponse: any;
    deleteQuestionnaireResponseTask: any;
    isLoading: boolean;
  } = useMasterQuestionnaireResponseList(
    `${MEDPLUM_QUESTIONNAIRE}/${selectedLifestyleCategory.name}`,
    authenticatedUser?.id,
    `my${selectedLifestyleCategory.name}`
  );

  const { trackEventInFlow } = useAnalyticsService();
  const [payloadArr, setPayloadArr] = useState<any[]>([]);
  const [payloadFlag, setPayloadFlag] = useState<boolean>(false);
  const [submitPayload, setSubmitPayload] = useState<any>([]);
  const [saveEnable, setSaveEnable] = useState<boolean>(false);

  const { isPublicMode } = usePublicSettings();

  const formatPayloadArr = (
    questData: { linkId: string; answerOption: any[]; type: string; text: string },
    val: any,
    isMulti: boolean
  ) => {
    if (!saveEnable) {
      setSaveEnable(true);
      setSubmitPayload([]);
    }
    setPayloadArr((prev) => [
      ...prev.filter((x) => x.linkId !== questData?.linkId),
      {
        ...prev.filter((x) => x.linkId === questData?.linkId)[0],
        linkId: questData?.linkId,
        answer: isMulti
          ? val.map((y: any) => {
              return {
                valueCoding: {
                  code: y?.valueCoding?.code || y?.value,
                  display: y?.valueCoding?.display || y?.label,
                  system: '',
                },
              };
            })
          : [
              {
                valueCoding: {
                  code:
                    questData?.answerOption?.find((x: any) => x?.id === val.value).id ||
                    val?.value ||
                    (prev &&
                      prev?.length > 0 &&
                      prev?.find((x) => {
                        return x.linkId === questData?.linkId;
                      })?.answer[0].valueCoding.code),
                  display:
                    questData?.answerOption?.find((x: any) => x?.id === val.value).valueString ||
                    val?.label ||
                    (prev &&
                      prev?.length > 0 &&
                      prev?.find((x) => {
                        return x.linkId === questData?.linkId;
                      })?.answer[0].valueCoding.display),
                  system: '',
                },
              },
            ],
      },
    ]);
    setPayloadFlag(true);
    let result;
    if (isMulti) {
      result = val.map((x: any) => x?.value || x.valueCoding?.code);
    } else {
      const matchingOption = questData?.answerOption?.find((x: any) => x?.id === val.value);
      result = matchingOption?.id || val;
      console.log('result', result);
    }

    const matchedEvent = Object.entries(eventMapping).find(([key]) => questData.linkId.includes(key));
    const hasMatchingAnswer = (answerLists: any[], questDatas: { linkId: string }): boolean => {
      return answerLists.some((entry) =>
        entry.item?.some((item: any) => item.linkId === questDatas.linkId && item.answer?.length > 0)
      );
    };
    if (matchedEvent && !hasMatchingAnswer(answerList, questData)) {
      const [, { EventName, key }] = matchedEvent;
      recordLifestyleNutritionEvents(trackEventInFlow, {
        EventName,
        [key]: Array.isArray(result) ? result.join(',') : result?.label ?? result?.value ?? result,
      });
    }
  };

  const getInitialData = () => {
    if (answerList && answerList.length > 0 && answerList[0] && answerList[0]?.item) {
      answerList[0]?.item.forEach((element: any, index: number) => {
        if (answerList[0].item && index === answerList[0].item.length - 1) {
          setPayloadFlag(true);
        }
        setPayloadArr((prev) => [
          ...prev.filter((x) => x.linkId !== element?.linkId),
          {
            ...prev.filter((x) => x.linkId === element?.linkId)[0],
            linkId: element?.linkId,
            answer: element?.answer?.map((x: any) => {
              return x?.valueCoding?.code
                ? {
                    valueCoding: {
                      code: x?.valueCoding?.code,
                      display: x?.valueCoding?.display,
                      system: '',
                    },
                  }
                : {
                    valueCoding: {
                      display:
                        masterList[0].item
                          ?.filter((y: any) => y?.linkId === element?.linkId)[0]
                          ?.answerOption?.filter((z: any) => z?.id === x?.valueString)[0]?.valueString ||
                        x?.valueString,
                      system: '',
                    },
                  };
            }),
          },
        ]);
      });
    }
  };

  useEffect(() => {
    getInitialData();
  }, []);
  useEffect(() => {
    if (answerList && answerList.length > 0 && answerList[0] && answerList[0]?.item) {
      const eventName = masterScreenInteracted[selectedLifestyleCategory.name];
      if (eventName) {
        recordLifestyleNutritionEvents(trackEventInFlow, {
          EventName: eventName,
        });
      }
    } else {
      const eventName = masterScreenAddStarted[selectedLifestyleCategory.name];
      const eventProps = entryPointProps[selectedLifestyleCategory.name];
      if (eventName) {
        recordLifestyleNutritionEvents(trackEventInFlow, {
          EventName: eventName,
          [eventProps]: 'my_health_profile',
        });
      }
    }
  }, [selectedLifestyleCategory.name]);

  useEffect(() => {
    if (payloadFlag === true) {
      setSubmitPayload(payloadArr);
      setPayloadFlag(false);
    }
  }, [payloadFlag]);

  const onSubmit = async (item: any[], isClear?: boolean) => {
    try {
      const apiPayload: QuestionnaireResponsePayload = {
        questionnaire: `${MEDPLUM_QUESTIONNAIRE}/${selectedLifestyleCategory.name}`,
        item,
      };

      if (!isClear) {
        if (!saveEnable || payloadArr.length !== item.length) {
          // console.log('NOT EQUAL');
          return;
        }
      }
      const deletePayload: any = {
        deleteTask: 'Delete Lifestyle',
        identifier: 'urn:fh-workflow:task:delete:lifestyle-nutrition',
        questionnaireId: answerList[0]?.id,
      };
      // console.log(answerList);
      if (isClear) {
        await deleteQuestionnaireResponseTask(deletePayload);
      } else if (answerList && answerList?.length > 0) {
        apiPayload.id = answerList[0]?.id;
        await updateQuestionnaireResponse(apiPayload);
      } else {
        await addQuestionnaireResponse(apiPayload);
      }

      if (item.length > 0) {
        const matchedProps = item
          ?.map(({ linkId, answer }) => {
            const mappingKey = Object.keys(eventMapping).find((key) => linkId.includes(key));
            if (!mappingKey) return null;
            return {
              [eventMapping[mappingKey].key]: answer[0]?.valueCoding?.display || null,
            };
          })
          .filter(Boolean);
        const mergedProps = Object.assign({}, ...matchedProps);
        const eventProps = entryPointProps[selectedLifestyleCategory.name];
        recordLifestyleNutritionEvents(trackEventInFlow, {
          EventName:
            answerList?.length > 0 && answerList[0] && answerList[0]?.item && answerList[0]?.item?.length > 0
              ? masterScreenEdited[selectedLifestyleCategory.name]
              : masterScreen[selectedLifestyleCategory.name],
          ...mergedProps,
          [eventProps]: 'my_health_profile',
        });

        toast({
          title: `Successfully added the Lifestyle Nutrition`,
          status: 'success',
          duration: 3000,
          isClosable: true,
        });
      }
      setSubmitPayload([]);
      setSaveEnable(false);
    } catch (err) {
      toast({
        title: 'Something went wrong. Please try again.',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
    }
  };

  return isPublicMode && payloadArr.length <= 0 ? (
    <SidebarEmptyState
      title=""
      actionButtonText=""
      imageSrc=""
      isPublicMode={isPublicMode}
    />
  ) : (
    <Flex
      paddingBottom="32px"
      direction="column"
    >
      <Card
        p="20px"
        borderRadius="10px"
        boxShadow="0px 1px 4px rgba(73, 90, 228, 0.12)"
      >
        <VStack spacing="8">
          {masterList?.length &&
            masterList[0]?.item.map((question: any) => (
              <QuestionItem
                key={question.linkId}
                question={question}
                answer={payloadArr.find((x) => x.linkId === question.linkId)}
                masterList={masterList}
                formatPayloadArr={formatPayloadArr}
                isPublicMode={isPublicMode}
              />
            ))}
        </VStack>
      </Card>
      {!isPublicMode && payloadArr.length > 0 && (
        <Flex
          justifyContent="flex-end"
          mt="20px"
        >
          <Button
            variant="link"
            onClick={() => {
              // console.log(payloadArr);
              setPayloadArr([]);
              setSubmitPayload([]);
              onSubmit([], true);
              recordLifestyleNutritionEvents(trackEventInFlow, {
                EventName: masterScreenRemoved[selectedLifestyleCategory.name],
              });
            }}
            mr="32px"
            color="red.100"
          >
            Clear All
          </Button>
          <Button
            isLoading={isLoading}
            disabled={submitPayload.length === 0}
            onClick={() => onSubmit(submitPayload)}
          >
            Save
          </Button>
        </Flex>
      )}
    </Flex>
  );
}
