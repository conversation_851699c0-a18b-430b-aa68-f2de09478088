import {
  Box,
  Button,
  Flex,
  FormControl,
  FormErrorMessage,
  FormLabel,
  HStack,
  IconButton,
  Input,
  Radio,
  RadioGroup,
  Stack,
  Switch,
  Text,
  useDisclosure,
  useToast,
} from '@chakra-ui/react';
import { zodResolver } from '@hookform/resolvers/zod/dist/zod';
import { useCallback, useEffect, useState } from 'react';
import { FormProvider, useForm, useFormContext } from 'react-hook-form';
import { z } from 'zod';
import { DOCUMENT_REF, NavigationHelper, PATIENT_DEFINED } from '@user/lib/constants';
import { useMedication } from '@user/lib/medplum-state';
import { useNavigate } from 'react-router-dom';
import dayjs from 'dayjs';
import { medplumApi } from '@user/lib/medplum-api';
import { SupplementsEventProps, recordSupplementsEvents } from '@user/lib/events-analytics-manager';

import { useAnalyticsService, useAuthService } from '@lib/state';
import { DatePickerField, SuggestionOptionProps } from '../../../../../components/ui/Form';
import { LinkedDocumentsCard } from '../LinkedDocumentsCard';
import { MedicalRecordSelect } from '../MedicalRecordSelect';
import { SearchableSelect, Select, SelectOptionProps } from '../../../../../components/ui/Select';
import {
  FH_CODE_SYSTEM_FACT,
  FH_CODE_SYSTEM_FLUENT_HEALTH_UI,
  FH_STRUCTURE_DEFINITION_CLINICALSTATUS,
  SNOMED_URL,
} from 'src/constants/medplumConstants';
import { useExtractDocumentResource } from 'src/app/medical-records/lib/state';
import { isDuplicatePresentMedication } from '@lib/utils/utils';

interface TimeDoseInputProps {
  label: string;
  value: number;
  onIncrement: () => void;
  onDecrement: () => void;
  onChange: (value: string) => void;
}
const timingMapping: Record<string, string> = {
  morning: 'MORN',
  afternoon: 'AFT',
  evening: 'EVE',
  night: 'NIGHT',
};

const formControlStyles = {
  border: '1px solid',
  borderRadius: '12px',
  px: 4,
  py: 2,
  borderColor: 'iris.500',
  mb: 2,
};
const inputStyles = {
  w: '50px',
  h: '25px',
  min: 0,
  max: 999,
  borderColor: 'iris.500',
  variant: 'flushed',
};
const VALUE_SET_DOSAGE_ADDITIONAL_INSTRUCTION = 'http://hl7.org/fhir/r4/valueset-additional-instruction-codes';
const BEFORE_FOOD_CODE = '311501008';
const BEFORE_FOOD_DISPLAY = 'Before Food';
const AFTER_FOOD_CODE = '311504000';
const AFTER_FOOD_DISPLAY = 'After Food';
const MEDICATION_STATEMENT_PRESCRIBED_EXTENSION_URL = 'https://fluentinhealth.com/fhir/StructureDefinition/Prescribed';

const customOption = { label: 'My supplement is not listed', value: 'MY_supplement_NOT_LISTED' };
export function NoOptionsMessageComponent({ onClick }: { onClick: () => void }) {
  return (
    <Text
      sx={{
        width: '100%',
        textAlign: 'left',
        color: 'fluentHealthText.100',
        cursor: 'pointer',
      }}
      onClick={onClick}
    >
      My supplement is not listed
    </Text>
  );
}

function MinusIcon({ disabled }: { disabled: boolean }) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
    >
      <path
        d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z"
        stroke={disabled ? '#A9AEAF' : '#14181A'}
        fill={disabled ? 'none' : '#fff'}
        strokeLinecap="round"
      />
      <path
        d="M16.2426 12.0005H7.75736"
        stroke={disabled ? '#A9AEAF' : '#14181A'}
        strokeLinecap="round"
      />
    </svg>
  );
}

function PlusIcon() {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
    >
      <path
        d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z"
        fill="#fff"
        stroke="#14181A"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M12 8V16"
        stroke="#14181A"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M8 12H16"
        stroke="#14181A"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
}

function TimeDoseInput({ label, value, onIncrement, onDecrement, onChange }: TimeDoseInputProps): JSX.Element {
  return (
    <FormControl {...formControlStyles}>
      <HStack justifyContent="space-between">
        <FormLabel
          mb="0"
          color="fluentHealthText.200"
          fontWeight="400"
          textTransform="capitalize"
        >
          {label}
        </FormLabel>
        <HStack>
          <IconButton
            aria-label="Decrease"
            icon={<MinusIcon disabled={value === 0} />}
            size="xs"
            variant="ghost"
            isDisabled={value === 0}
            onClick={onDecrement}
          />
          <Input
            type="number"
            value={value}
            onChange={(e) => onChange(e.target.value)}
            {...inputStyles}
            textAlign="center"
          />
          <IconButton
            aria-label="Increase"
            icon={<PlusIcon />}
            size="xs"
            variant="ghost"
            isDisabled={value === 999}
            onClick={onIncrement}
          />
        </HStack>
      </HStack>
    </FormControl>
  );
}

function getInitialFormData(supplement: any) {
  const { item: items } = supplement || {};
  const primary = supplement?.medicationCodeableConcept?.coding?.[0] ?? { code: '', display: '' };
  const isCustom = primary?.code === `su:${PATIENT_DEFINED}`;
  return {
    id: supplement?.id || '',
    drug_id: isCustom ? customOption.value : supplement?.medicationCodeableConcept?.coding[0].code,
    drug_name: isCustom ? customOption.label : supplement?.medicationCodeableConcept?.coding[0].display,
    dosageInstruction: items?.find((i: any) => i.linkId === 'supplement-dosage-amount')?.answer?.[0]?.valueString,
    external_reports: supplement?.derivedFrom ? useExtractDocumentResource(supplement?.derivedFrom) : [],
    supplement_administered: {
      label: supplement?.dosage?.[0]?.route?.coding?.[0]?.display,
      value: supplement?.dosage?.[0]?.route?.coding?.[0]?.code,
    },
    supplement_date: supplement?.effectivePeriod?.start,
    supplement_end: supplement?.effectivePeriod?.end,
    supplement_status: supplement?.extension?.[1]?.valueCodeableConcept?.coding?.[0]?.code === 'active',
    prescribed: supplement?.extension?.[0]?.valueBoolean,
    custom_supplement: isCustom ? primary.display : '',
  };
}

function SupplementSelect({
  supplementOptions,
  trackEventInFlow,
  onSearch,
  supplement,
  setShowCustomInput,
  setHasChanges,
}: {
  supplementOptions: any;
  trackEventInFlow: any;
  onSearch?: (searchVal: string) => void;
  supplement: any;
  setShowCustomInput: (flag: boolean) => void;
  setHasChanges: (flag: boolean) => void;
}) {
  const form: any = useFormContext();
  const [conditionValue, setConditionValue] = useState<SuggestionOptionProps | null>({
    label: form.watch('drug_name'),
    value: form.watch('drug_id'),
  });
  const [menuIsOpen, setMenuIsOpen] = useState(false);
  const [inputValue, setInputValue] = useState('');
  const handleNoOptionsClick = useCallback(() => {
    setConditionValue(customOption);
    setShowCustomInput(true);
    setInputValue('');
    setMenuIsOpen(false);
    form.setValue('drug_name', customOption.label);
    form.setValue('drug_id', customOption.value);
    form.trigger(['drug_name', 'drug_id']);
  }, [setShowCustomInput]);

  const onChange = useCallback(
    (option: SelectOptionProps | any) => {
      if (option) {
        form.setValue('drug_id', option.value);
        form.setValue('drug_name', option.label);
        form.trigger(['drug_id', 'drug_name']);
        setConditionValue(option);
        setHasChanges(true);
        if (!supplement) {
          recordSupplementsEvents(trackEventInFlow, {
            EventName: 'SupplementsAddInProgName',
            su_name: option.value,
          });
        }
      } else {
        form.setValue('drug_id', '');
        form.setValue('drug_name', '');
        form.trigger(['drug_id', 'drug_name']);
        setConditionValue(null);
        setHasChanges(true);
      }
      setShowCustomInput(false);
    },
    [supplementOptions]
  );

  const handleFocus = useCallback(() => {
    if (conditionValue?.value === customOption.value) {
      setConditionValue(null);
      form.setValue('supplement', '');
      setShowCustomInput(false);
    }
  }, [conditionValue, form, setShowCustomInput]);

  const noOptionsMessageFn = useCallback(
    () => <NoOptionsMessageComponent onClick={handleNoOptionsClick} />,
    [handleNoOptionsClick]
  );

  return (
    <FormControl
      variant="floating"
      isInvalid={!!form.formState.errors?.drug_id}
    >
      <SearchableSelect
        labelText="Name of supplement*"
        value={conditionValue}
        options={supplementOptions}
        onChange={onChange}
        onSearch={onSearch}
        isSupplementSelect
        isClearable={false}
        onFocus={handleFocus}
        menuIsOpen={menuIsOpen}
        onMenuOpen={() => setMenuIsOpen(true)}
        onMenuClose={() => setMenuIsOpen(false)}
        inputValue={inputValue}
        onInputChange={setInputValue}
        noOptionsMessage={noOptionsMessageFn}
      />
      <FormErrorMessage> {form?.formState?.errors?.drug_id?.message} </FormErrorMessage>
    </FormControl>
  );
}

export default function SupplementForm({
  supplement,
  closeDialog,
  supplementOptions,
  supplementAdministeredOptions,
  formSubmiting,
}: {
  supplement: any;
  closeDialog: () => void;
  supplementOptions: any;
  supplementAdministeredOptions: any;
  formSubmiting: (incoming: boolean) => void;
}) {
  const toast = useToast();
  const navigate = useNavigate();
  const startDatePickerPopover = useDisclosure();
  const endDatePickerPopover = useDisclosure();

  const [isLoading] = useState<boolean>(false);
  const { authenticatedUser } = useAuthService();
  const { medicationList, updateMedication, addMedication } = useMedication(authenticatedUser?.id);
  const { trackEventInFlow } = useAnalyticsService();
  const [hasChanges, setHasChanges] = useState(false);
  const [showCustomInput, setShowCustomInput] = useState(
    supplement?.medicationCodeableConcept?.coding[0].code === `su:${PATIENT_DEFINED}` || false
  );
  const form = useForm({
    mode: 'onChange',
    defaultValues: getInitialFormData(supplement),
    resolver: zodResolver(
      z.object({
        drug_id: z
          .string()
          .min(1, 'Supplement type is required')
          .refine((value) => {
            if (value === customOption.value) {
              form.clearErrors('drug_id.value');
              return true;
            }
            const duplicateId = isDuplicatePresentMedication(value, medicationList, supplement);
            return !duplicateId;
          }, 'This Supplement already exists'),
        drug_name: z.string().trim().min(1),
        supplement_administered: z
          .object({ value: z.string().trim().nullable().optional(), label: z.string().trim().nullable().optional() })
          .optional(),
        supplement_administered_label: z.string().trim().optional(),
        supplement_date: z.string().trim().nullable().optional(),
        supplement_end: z.string().trim().nullable().optional(),
        supplement_status: z.boolean().optional(),
        prescribed: z.boolean().optional(),
        custom_supplement: showCustomInput ? z.string().min(1, 'This field is required') : z.string().optional(),
      })
    ),
  });
  const defaultSchedule = (supplement?.dosage || []).reduce(
    (acc: any, dose: any) => {
      const whenList = dose?.timing?.repeat?.when || []; // Ensure it's an array
      const doseValue = dose?.doseAndRate?.[0]?.doseQuantity?.value || 0;
      const duration = dose?.timing?.repeat?.boundsDuration?.value ?? acc.duration;
      const foodDisplay = dose?.additionalInstruction?.[0]?.coding?.[0]?.display;
      const foodTiming =
        acc.foodTiming ||
        (foodDisplay === AFTER_FOOD_DISPLAY ? 'after' : foodDisplay === BEFORE_FOOD_DISPLAY ? 'before' : '');
      whenList.forEach((when: string) => {
        switch (when) {
          case timingMapping.morning:
            acc.morning += doseValue;
            break;
          case timingMapping.afternoon:
            acc.afternoon += doseValue;
            break;
          case timingMapping.evening:
            acc.evening += doseValue;
            break;
          case timingMapping.night:
            acc.night += doseValue;
            break;
          default:
            break;
        }
      });
      acc.duration = duration;
      acc.foodTiming = foodTiming;
      return acc;
    },
    { morning: 0, afternoon: 0, evening: 0, night: 0, duration: 0, foodTiming: '' }
  );
  const [schedule, setSchedule] = useState(defaultSchedule);
  const scheduleMapping: Record<
    keyof typeof schedule,
    { eventName: SupplementsEventProps['EventName']; propName: keyof SupplementsEventProps }
  > = {
    morning: { eventName: 'SupplementsAddInProgScheduleMorning', propName: 'su_dosage_morning' },
    afternoon: { eventName: 'SupplementsAddInProgScheduleAfternoon', propName: 'su_dosage_afternoon' },
    evening: { eventName: 'SupplementsAddInProgScheduleEvening', propName: 'su_dosage_evening' },
    night: { eventName: 'SupplementsAddInProgScheduleNight', propName: 'su_dosage_night' },
    duration: { eventName: 'SupplementsAddInProgScheduleDuration', propName: 'su_dosage_duration' },
  };
  const updateSupplementEventForKey = (key: keyof typeof schedule, newValue: number) => {
    const mappingItem = scheduleMapping[key];
    if (mappingItem && !supplement) {
      recordSupplementsEvents(trackEventInFlow, {
        EventName: mappingItem.eventName,
        [mappingItem.propName]: newValue,
      });
    }
  };
  const handleIncrement = (key: keyof typeof schedule) => {
    setSchedule((prev: any) => {
      const current = Number(prev[key]);
      const newVal = Math.min(999, current + 1);
      updateSupplementEventForKey(key, newVal);
      return { ...prev, [key]: newVal };
    });
    setHasChanges(true);
  };
  const handleDecrement = (key: keyof typeof schedule) => {
    setSchedule((prev: any) => {
      const current = Number(prev[key]);
      const newVal = Math.max(0, current - 1);
      updateSupplementEventForKey(key, newVal);
      return { ...prev, [key]: newVal };
    });
    setHasChanges(true);
  };

  const handleDoseChange = (key: keyof typeof schedule, value: string) => {
    const numericValue = Number(value);
    if (!Number.isNaN(numericValue) && numericValue >= 0 && numericValue <= 999) {
      setSchedule((prev: any) => {
        updateSupplementEventForKey(key, numericValue);
        return { ...prev, [key]: numericValue };
      });
    }
    setHasChanges(true);
  };
  const startDateField = form.watch('supplement_date');
  const endDateField = form.watch('supplement_end');
  const datePickerChangeHandler = (date: Date | null, type: string) => {
    if (type === 'start') {
      if (dayjs(date).isValid()) {
        form.setValue('supplement_date', dayjs(date).format('YYYY-MM-DD'));
        setHasChanges(true);
        if (!supplement) {
          recordSupplementsEvents(trackEventInFlow, {
            EventName: 'SupplementsAddInProgStartDate',
            su_start_date: dayjs(date).format('YYYY/MM/DD'),
          });
        }
      } else {
        form.setValue('supplement_date', '');
        setHasChanges(true);
      }
      form.trigger('supplement_date');
      startDatePickerPopover.onClose();
    } else {
      if (dayjs(date).isValid()) {
        form.setValue('supplement_end', dayjs(date).format('YYYY-MM-DD'));
        setHasChanges(true);
        if (!supplement) {
          recordSupplementsEvents(trackEventInFlow, {
            EventName: 'SupplementsAddInProgEndDate',
            su_end_date: dayjs(date).format('YYYY/MM/DD'),
          });
        }
      } else {
        form.setValue('supplement_end', '');
        setHasChanges(true);
      }
      endDatePickerPopover.onClose();
    }
  };
  const datePickerClearHandler = (type: string) => {
    if (type === 'start') {
      form.setValue('supplement_date', '');
      startDatePickerPopover.onClose();
    } else {
      form.setValue('supplement_end', '');
      endDatePickerPopover.onClose();
    }
  };
  const {
    handleSubmit,
    register,
    formState: { isSubmitting, isValid },
    watch,
  }: any = form;
  const takingAdministered = watch('supplement_administered');
  const externalReportsField = watch('external_reports');
  const onSupplementAdministeredSelect = useCallback((value: SelectOptionProps | any) => {
    form.setValue('supplement_administered', value);
    form.trigger('supplement_administered');
    setHasChanges(true);
    if (!supplement) {
      recordSupplementsEvents(trackEventInFlow, {
        EventName: 'SupplementsAddInProgAdministered',
        su_administered: value?.label,
      });
    }
  }, []);
  async function onSubmit(addedSupplement: any) {
    try {
      formSubmiting(true);
      const newCoding = await medplumApi.valueSetList.getFHIRCodingFromCMS(addedSupplement?.drug_id);
      const groupedDosage: Record<number, string[]> = {};
      Object.entries(schedule).forEach(([key, value]) => {
        if (!['morning', 'afternoon', 'evening', 'night'].includes(key) || Number(value) <= 0) return;
        if (!groupedDosage[Number(value)]) {
          groupedDosage[Number(value)] = [];
        }
        groupedDosage[Number(value)].push(key);
      });

      const dosageInstructions = Object.entries(groupedDosage).map(([doseValue, times]) => {
        const whenArray = times.map((key) => timingMapping[key]);

        const dosage: any = {
          timing: {
            repeat: {
              boundsDuration: {
                value: schedule.duration,
                unit: 'd',
              },
              when: whenArray,
            },
          },
          doseAndRate: [
            {
              doseQuantity: { value: Number(doseValue) },
            },
          ],
          additionalInstruction: [],
        };
        if (addedSupplement?.supplement_administered?.value) {
          dosage.route = {
            coding: [
              {
                system: SNOMED_URL,
                code: addedSupplement?.supplement_administered.value,
                display: addedSupplement?.supplement_administered.label,
              },
            ],
          };
        }

        return dosage;
      });
      if (schedule.foodTiming) {
        const additionalCoding = {
          system: VALUE_SET_DOSAGE_ADDITIONAL_INSTRUCTION,
          code: schedule.foodTiming === 'before' ? BEFORE_FOOD_CODE : AFTER_FOOD_CODE,
          display: schedule.foodTiming === 'before' ? BEFORE_FOOD_DISPLAY : AFTER_FOOD_DISPLAY,
        };

        dosageInstructions.forEach((dosage: any) => {
          dosage.additionalInstruction.push({
            coding: [additionalCoding],
          });
        });
      }
      let coding: any = [];
      if (addedSupplement?.drug_id === customOption.value) {
        coding = [
          {
            system: SNOMED_URL,
            code: `su:${PATIENT_DEFINED}`,
            display: addedSupplement.custom_supplement,
          },
        ];
      } else {
        coding = newCoding;
      }
      const prescribedExtension = {
        url: MEDICATION_STATEMENT_PRESCRIBED_EXTENSION_URL,
        valueBoolean: addedSupplement?.prescribed || false,
      };

      const clinicalStatusExtension = {
        url: FH_STRUCTURE_DEFINITION_CLINICALSTATUS,
        valueCodeableConcept: {
          coding: [
            {
              system: FH_CODE_SYSTEM_FLUENT_HEALTH_UI,
              code: addedSupplement?.supplement_status ? 'active' : 'inactive',
              display: addedSupplement?.supplement_status ? 'Active' : 'Inactive',
            },
          ],
        },
      };
      const payload: {
        resourceType: string;
        id?: string;
        identifier: { system: string; value: string }[];
        status: string;
        medicationCodeableConcept: { coding: any };
        subject: { reference: string };
        effectivePeriod: { start: any; end?: any };
        dosage: any[];
        derivedFrom?: { reference: string }[];
        extension: Array<typeof prescribedExtension | typeof clinicalStatusExtension>;
      } = {
        resourceType: 'MedicationStatement',
        ...(supplement?.id ? { id: supplement.id } : {}),
        identifier: [
          {
            system: FH_CODE_SYSTEM_FACT,
            value: 'supplements',
          },
        ],
        status: addedSupplement?.supplement_status ? 'active' : addedSupplement?.supplement_end ? 'stopped' : 'active',
        medicationCodeableConcept: {
          coding,
        },
        subject: {
          reference: `Patient/${authenticatedUser?.id}`,
        },
        effectivePeriod: {
          start: addedSupplement.supplement_date,
          ...(!addedSupplement?.supplement_status && addedSupplement?.supplement_end
            ? { end: addedSupplement.supplement_end }
            : {}),
        },
        dosage: dosageInstructions,
        extension: [prescribedExtension, clinicalStatusExtension],
      };
      if (externalReportsField?.length) {
        payload.derivedFrom = externalReportsField.map((item: any) => ({
          reference: `${DOCUMENT_REF}/${item.id}`,
        }));
      }
      if (supplement) {
        await updateMedication({
          payloadMedication: payload,
        });
      } else {
        await addMedication({ payload });
      }
      toast({
        title: `${supplement ? 'Successfully updated' : 'Successfully added'} supplement`,
        status: 'success',
        duration: 4000,
        isClosable: true,
      });
      recordSupplementsEvents(trackEventInFlow, {
        EventName: supplement ? 'SupplementsEdited' : 'SupplementsAddCompleted',
        su_entry_point: 'my_health_profile',
        su_name: addedSupplement?.drug_name,
        su_status: addedSupplement?.supplement_status,
        su_start_date: dayjs(addedSupplement?.supplement_date).format('YYYY/MM/DD'),
        su_end_date: addedSupplement?.supplement_end && dayjs(addedSupplement?.supplement_end).format('YYYY/MM/DD'),
        su_dosage_morning: Number(schedule.morning),
        su_dosage_afternoon: Number(schedule.afternoon),
        su_dosage_evening: Number(schedule.evening),
        su_dosage_night: Number(schedule.night),
        su_dosage_duration: Number(schedule.duration),
        su_food: schedule.foodTiming,
        su_administered: addedSupplement?.supplement_administered?.label,
        su_record_added: addedSupplement?.external_reports?.length,
      });

      closeDialog?.();
    } catch (_) {
      toast({
        title: 'Something went wrong. Please try again.',
        status: 'error',
        duration: 4000,
        isClosable: true,
      });
    } finally {
      formSubmiting(false);
      navigate(NavigationHelper.getEhrView(false, 'medications-supplements', 'supplement'));
    }
  }

  const removeAttachedMedicalRecordHandler = (record: any) => {
    form.setValue(
      'external_reports',
      externalReportsField.filter((item: any) => item.id !== record.id)
    );
  };
  useEffect(() => {
    const subscription = form.watch(() => {
      setHasChanges(true);
    });

    return () => subscription.unsubscribe();
  }, [form]);

  return (
    <FormProvider {...form}>
      <Box
        color="black"
        display="flex"
        flexDirection="column"
      />
      <Flex
        direction="column"
        gap="4"
        mt="4"
      >
        <Box mt="3">
          <Flex
            direction="column"
            gap="8"
          >
            <SupplementSelect
              supplementOptions={supplementOptions}
              trackEventInFlow={trackEventInFlow}
              supplement={supplement}
              setShowCustomInput={setShowCustomInput}
              setHasChanges={setHasChanges}
            />
            {showCustomInput && (
              <FormControl
                isInvalid={!!form.formState.errors.custom_supplement}
                variant="floating"
              >
                <Input
                  defaultValue={form.watch('custom_supplement') || ''}
                  placeholder=" "
                  {...form.register('custom_supplement')}
                  onChange={(e) => {
                    form.setValue('custom_supplement', e?.target?.value);
                    form.trigger('custom_supplement');
                  }}
                  onBlurCapture={(e) => {
                    if (e.target.value && !supplement) {
                      recordSupplementsEvents(trackEventInFlow, {
                        EventName: 'SupplementsAddInProgName',
                        su_name: e.target.value.toString(),
                      });
                    }
                  }}
                />
                <FormLabel>Enter the supplement*</FormLabel>
              </FormControl>
            )}
          </Flex>
        </Box>
        <Box mt="5">
          <DatePickerField
            name="start_date"
            labelText="Start Date for this supplement"
            errorText="This field is required"
            rules={{ required: true }}
            datePickerChangeHandler={(date) => datePickerChangeHandler(date, 'start')}
            datePickerClearHandler={() => datePickerClearHandler('start')}
            datePickerPopover={startDatePickerPopover}
            isClearDateButtonDisabled={startDateField?.length === 0}
            selected={
              dayjs(form.watch('supplement_date')).isValid() ? dayjs(form.watch('supplement_date')).toDate() : null
            }
            popoverProps={{ placement: 'bottom-start' }}
            maxDate={endDateField && dayjs(endDateField).isValid() ? dayjs(endDateField).toDate() : new Date()}
            {...register('supplement_date')}
          />
          <Flex
            alignItems="center"
            justifyContent="space-between"
            borderBottom="1px solid var(--chakra-colors-iris-500)"
            color="var(--chakra-colors-iris-500)"
            mt={8}
          >
            <FormLabel
              fontSize="18px"
              fontWeight={400}
            >
              Are you still taking this supplement?
            </FormLabel>
            <Switch
              size="md"
              mb={2}
              isChecked={form.watch('supplement_status')}
              onChange={(e) => {
                form.setValue('supplement_status', e.target.checked);
                if (!supplement) {
                  recordSupplementsEvents(trackEventInFlow, {
                    EventName: 'SupplementsAddInProgStatus',
                    su_status: e.target.checked,
                  });
                }
              }}
            />
          </Flex>
          {!form.watch('supplement_status') && (
            <Flex mt={10}>
              <DatePickerField
                name="end_date"
                labelText="End date for this supplement"
                datePickerChangeHandler={(date) => datePickerChangeHandler(date, 'end')}
                datePickerClearHandler={() => datePickerClearHandler('end')}
                datePickerPopover={endDatePickerPopover}
                isClearDateButtonDisabled={endDateField?.length === 0}
                selected={
                  dayjs(form.watch('supplement_end')).isValid() ? dayjs(form.watch('supplement_end')).toDate() : null
                }
                popoverProps={{ placement: 'bottom-start' }}
                minDate={
                  startDateField && dayjs(startDateField).isValid() ? dayjs(startDateField).toDate() : new Date()
                }
                maxDate={new Date()}
                {...register('supplement_end')}
              />
            </Flex>
          )}
        </Box>
        <Box>
          <Text
            color="iris.500"
            mt={3}
            mb={1}
            fontSize="18px"
          >
            Schedule of supplement
          </Text>
          {(['morning', 'afternoon', 'evening', 'night'] as const)?.map((time) => (
            <TimeDoseInput
              key={time}
              label={time}
              value={schedule[time]}
              onIncrement={() => handleIncrement(time)}
              onDecrement={() => handleDecrement(time)}
              onChange={(value) => handleDoseChange(time, value)}
            />
          ))}
          <FormControl {...formControlStyles}>
            <HStack
              justifyContent="space-between"
              pr="32px"
            >
              <FormLabel
                mb="0"
                color="fluentHealthText.200"
                fontWeight="400"
              >
                Duration (in days)
              </FormLabel>
              <Input
                type="number"
                value={String(schedule.duration)}
                onChange={(e) => handleDoseChange('duration', e.target.value)}
                {...inputStyles}
                textAlign="center"
              />
            </HStack>
          </FormControl>
          <FormControl {...formControlStyles}>
            <HStack justifyContent="space-between">
              <RadioGroup
                onChange={(value) => {
                  setSchedule((prev: any) => ({ ...prev, foodTiming: value }));
                  if (!supplement) {
                    recordSupplementsEvents(trackEventInFlow, {
                      EventName: 'SupplementsAddInProgScheduleFood',
                      su_food: value,
                    });
                  }
                  setHasChanges(true);
                }}
                value={schedule.foodTiming}
              >
                <Stack
                  direction="row"
                  spacing={6}
                >
                  <Radio
                    color="fluentHealthText.200"
                    value="before"
                  >
                    Before food
                  </Radio>
                  <Radio
                    color="fluentHealthText.200"
                    value="after"
                  >
                    After food
                  </Radio>
                </Stack>
              </RadioGroup>
            </HStack>
          </FormControl>
        </Box>
        <Box mt="3">
          <Select
            labelText="How is this supplement administered?"
            value={supplementAdministeredOptions?.find((item: any) => item.value === takingAdministered?.value)}
            onChange={onSupplementAdministeredSelect}
            options={supplementAdministeredOptions}
            menuPosition="fixed"
            isSearchable={false}
          />
        </Box>
        <Flex
          alignItems="center"
          justifyContent="space-between"
          borderBottom="1px solid var(--chakra-colors-iris-500)"
          color="var(--chakra-colors-iris-500)"
          mt={8}
        >
          <FormLabel
            fontSize="18px"
            fontWeight={400}
          >
            Did a doctor prescribe this?
          </FormLabel>
          <Switch
            size="md"
            mb={2}
            isChecked={form.watch('prescribed')}
            onChange={(e) => {
              form.setValue('prescribed', e.target.checked);
              setHasChanges(true);
              if (!supplement) {
                recordSupplementsEvents(trackEventInFlow, {
                  EventName: 'SupplementsAddInProgPrescribed',
                  su_prescribed: e.target.checked,
                });
              }
            }}
          />
        </Flex>
        <Box mt="3">
          <MedicalRecordSelect
          // onSelectExtra={(e) =>
          //   // recordSupplementsEvents(trackEventInFlow, {
          //   //   EventName: 'SupplementsAddInProgRecordsAdded',
          //   //   me_record_added: !!e?.length,
          //   // })
          // }
          />
          {externalReportsField.length > 0 && (
            <Box mt="3">
              <LinkedDocumentsCard
                records={externalReportsField}
                onRemove={removeAttachedMedicalRecordHandler}
                showRemoveButton
              />
            </Box>
          )}
        </Box>
        <HStack justifyContent="flex-end">
          <Button
            isDisabled={!isValid || !hasChanges}
            isLoading={isSubmitting || isLoading}
            onClick={handleSubmit(onSubmit)}
          >
            {supplement ? 'Save' : 'Add'}
          </Button>
        </HStack>
      </Flex>
    </FormProvider>
  );
}
