import dayjs from 'dayjs';
import React, { Ref, forwardRef, useCallback, useEffect, useRef, useState } from 'react';
import { But<PERSON>, Divider, Flex, Text, VStack, useDisclosure, useToast } from '@chakra-ui/react';
import { FormProvider, useForm } from 'react-hook-form';
import { SelectInstance } from 'react-select/dist/declarations/src';
import { QuestionnaireResponsePayload } from '@user/lib/models/questionnaire-response';
import { recordReproductiveHealthEvents } from '@user/lib/events-analytics-manager';

import { useAnalyticsService, useAuthService, usePublicSettings } from '@lib/state';
import { Select, SelectOptionProps } from 'src/components/ui/Select';
import { DatePickerField as BaseDatePickerField } from '../../../../../components/ui/Form';
import { Card, QuestionCTA } from '../SidebarComponents';
import {
  MasterReproductiveHealthQuestion,
  PatientReproductiveHealthSelectAnswer,
} from '@lib/models/reproductive-health';
import {
  REPRODUCTIVE_HEALTH_QUESTION_FIELD_TYPES,
  useMasterQuestionnaireList,
  useMasterQuestionnaireResponseList,
} from '../../../lib/medplum-state';
import { SidebarEmptyState } from '../SidebarEmptyState';
import { getValueSetByMasterList } from '@lib/utils/utils';
import { MEDPLUM_QUESTIONNAIRE } from '@lib/constants';

// const ANSWER_UPDATE_DELAY = 500;

function DatePickerField({
  answer,
  onChange,
  onBlur,
  isLoading,
  isPublicMode = false,
}: {
  answer?: string;
  onChange: (value: string) => void;
  onBlur?: () => void;
  isLoading: boolean;
  isPublicMode?: boolean;
}) {
  const datePickerPopover = useDisclosure();

  const form = useForm({
    mode: 'onChange',
    defaultValues: {
      date: answer || '',
    },
  });
  const dateField = form.watch('date');

  const datePickerChangeHandler = (date: Date | null) => {
    form.setValue('date', dayjs(date).format('YYYY-MM-DD'));
    datePickerPopover.onClose();
    onChange(form.getValues('date'));
  };

  return (
    <FormProvider {...form}>
      <BaseDatePickerField
        // Field props
        name="date"
        errorText="This field cannot be empty"
        rules={{ required: true, validate: (value) => dayjs(value).isValid() }}
        isInvalid={form.formState.touchedFields.date && form.control._formValues.date.length === 0}
        isDisabled={isLoading || isPublicMode}
        // Datepicker props
        datePickerChangeHandler={isPublicMode ? () => {} : datePickerChangeHandler}
        datePickerPopover={datePickerPopover}
        showClearDateButton={false}
        selected={dayjs(dateField).isValid() ? dayjs(dateField).toDate() : null}
        popoverProps={{ placement: 'bottom-start' }}
        maxDate={new Date()}
        onClickOutside={isPublicMode ? () => {} : onBlur}
      />
    </FormProvider>
  );
}

// eslint-disable-next-line @typescript-eslint/naming-convention
const SelectField = forwardRef(
  (
    {
      answer,
      onChange,
      onBlur,
      isLoading,
      isPublicMode,
      selectOptions,
    }: {
      question: MasterReproductiveHealthQuestion;
      answer?: PatientReproductiveHealthSelectAnswer;
      onChange: (value: string) => void;
      onBlur?: () => void;
      isLoading: boolean;
      isPublicMode?: boolean;
      selectOptions: SelectOptionProps | any;
    },
    ref: Ref<SelectInstance>
  ) => {
    // Get current answer value
    const currentAnswerValue = answer || { code: '', display: '', system: '' };

    // Get the default value of the select.
    const [fieldValue, setFieldValue] = useState<SelectOptionProps>({
      value: currentAnswerValue.code,
      label: currentAnswerValue.display,
    });

    const resetValueOnBlur = () => {
      onBlur?.();
    };

    const handleSelectChange = async (newValue: SelectOptionProps | any) => {
      setFieldValue(newValue);
      await onChange(newValue);
    };

    return (
      <Select
        ref={ref}
        value={fieldValue}
        isLoading={isLoading}
        isSearchable={false}
        hideLabelWhenSelectedValue
        autoFocus={false}
        {...(isPublicMode
          ? { isDisabled: true }
          : {
              options: selectOptions,
              onChange: handleSelectChange,
              onBlur: resetValueOnBlur,
              onClickOutside: resetValueOnBlur,
            })}
      />
    );
  }
);

function QuestionItem({
  question,
  answer,
  formatPayloadArr,
  isPublicMode,
  masterList,
}: {
  question: MasterReproductiveHealthQuestion;
  answer?: any;
  formatPayloadArr: (QuestData: any, data: any) => void;
  isPublicMode: boolean;
  masterList: any;
}) {
  const [isCreatingAnswerState, setIsCreatingAnswerState] = useState(false);
  const { trackEventInFlow } = useAnalyticsService();

  const selectRef = useRef<SelectInstance | null>(null);

  const handleAnswerChange = (newValue: any) => {
    formatPayloadArr(question, newValue);

    const eventMapping: Record<string, { eventName: string; propName: string }> = {
      'reproductive-health-sexually-active': {
        eventName: 'ReproductiveHealthAddInProgSexuallyActive',
        propName: 'rh_sexually_active',
      },
      'reproductive-health-last-menstrual-period-date': {
        eventName: 'ReproductiveHealthAddInProgMenstrualDate',
        propName: 'rh_last_menstrual_date',
      },
      'reproductive-health-ever-been-pregnant': {
        eventName: 'ReproductiveHealthAddInProgPregnancyHistory',
        propName: 'rh_pregnancy_history',
      },
      'reproductive-health-terminated-pregnancy': {
        eventName: 'ReproductiveHealthAddInProgPregnancyTerminate',
        propName: 'rh_pregnancy_terminated',
      },
      'reproductive-health-do-you-have-children': {
        eventName: 'ReproductiveHealthAddInProgChildren',
        propName: 'rh_children',
      },
      'reproductive-health-you-ever-miscarried': {
        eventName: 'ReproductiveHealthAddInProgMiscarriage',
        propName: 'rh_miscarriage',
      },
      'reproductive-health-you-undergone-fertility-treatments': {
        eventName: 'ReproductiveHealthAddInProgFertilityTreatments',
        propName: 'rh_fertility_treatments',
      },
      'reproductive-health-current-pregnancy-status': {
        eventName: 'ReproductiveHealthAddInProgPregnancyStatus',
        propName: 'rh_pregnancy_status',
      },
    };

    if (eventMapping[question.linkId]) {
      let value;

      if (question.type === REPRODUCTIVE_HEALTH_QUESTION_FIELD_TYPES.choice) {
        value = newValue?.label || newValue?.value;
      } else if (question.type === REPRODUCTIVE_HEALTH_QUESTION_FIELD_TYPES.date) {
        value = newValue;
      }

      if (value) {
        recordReproductiveHealthEvents(trackEventInFlow, {
          EventName: eventMapping[question.linkId].eventName as any,
          [eventMapping[question.linkId].propName]: value,
        });
      }
    }
  };

  const memoValueSet = useCallback(
    (mastList: any, urlVal: string) => getValueSetByMasterList(mastList, urlVal),
    [question.answerValueSet]
  );

  // Format answers into a list of options
  const selectOptions =
    question?.answerOption?.map((item: any) => ({
      value: item.id,
      label: item.valueString,
    })) || memoValueSet(masterList, question.answerValueSet);

  const resetFieldOnBlur = () => {
    if (isCreatingAnswerState) {
      setIsCreatingAnswerState(false);
    }
  };

  const handleStartCreatingAnswer = async () => {
    await setIsCreatingAnswerState(true);

    if (!answer?.answer && question.linkId === 'reproductive-health-sexually-active') {
      recordReproductiveHealthEvents(trackEventInFlow, {
        EventName: 'ReproductiveHealthInteracted',
      });
      recordReproductiveHealthEvents(trackEventInFlow, {
        EventName: 'ReproductiveHealthAddStarted',
        rh_entry_point: 'my_health_profile',
      });
    }

    if (question.type === REPRODUCTIVE_HEALTH_QUESTION_FIELD_TYPES.choice) {
      selectRef?.current?.openMenu('first');
    }
  };

  return (
    <Flex
      direction="column"
      width="full"
    >
      {(answer?.answer && answer?.answer?.length > 0) || isCreatingAnswerState ? (
        <>
          <Text color="gray.400">{question.text}</Text>
          {question.type === REPRODUCTIVE_HEALTH_QUESTION_FIELD_TYPES.choice && (
            <SelectField
              ref={selectRef}
              question={question}
              answer={answer?.answer[0]?.valueCoding || answer}
              onChange={handleAnswerChange}
              onBlur={resetFieldOnBlur}
              isLoading={false}
              isPublicMode={isPublicMode}
              selectOptions={selectOptions}
            />
          )}
          {question.type === REPRODUCTIVE_HEALTH_QUESTION_FIELD_TYPES.date && (
            <DatePickerField
              answer={answer?.answer[0]?.valueDate || answer}
              onChange={handleAnswerChange}
              onBlur={resetFieldOnBlur}
              isLoading={false}
              isPublicMode={isPublicMode}
            />
          )}
        </>
      ) : (
        <>
          <QuestionCTA
            text={question.text}
            pr="10px"
            hideIcon={isPublicMode}
            iconColor="#FF6333"
            {...(isPublicMode ? {} : { onClick: handleStartCreatingAnswer })}
          />
          <Divider mt="12px" />
        </>
      )}
    </Flex>
  );
}

export function QuestionList() {
  const toast = useToast();
  const { authenticatedUser } = useAuthService();
  const { masterList }: any = useMasterQuestionnaireList(`${MEDPLUM_QUESTIONNAIRE}/ReproductiveHealth`);
  const { trackEventInFlow } = useAnalyticsService();
  const {
    answerList,
    addQuestionnaireResponse,
    updateQuestionnaireResponse,
    deleteQuestionnaireResponseTask,
    isLoading,
  }: any = useMasterQuestionnaireResponseList(
    `${MEDPLUM_QUESTIONNAIRE}/ReproductiveHealth`,
    authenticatedUser?.id,
    'myPatientReproductiveHealthAnswers'
  );

  const [payloadArr, setPayloadArr] = useState<any[]>([]);
  const [submitPayload, setSubmitPayload] = useState<any>([]);
  const [saveEnable, setSaveEnable] = useState<boolean>(false);

  const { isPublicMode } = usePublicSettings();
  const formatPayloadArr = (questData: { linkId: string; answerOption: any[]; type: string }, val: any) => {
    if (!saveEnable) {
      setSaveEnable(true);
      setSubmitPayload([]);
    }
    // console.log('QuestData', questData, val);
    setPayloadArr((prev) => [
      ...prev.filter((x) => x.linkId !== questData?.linkId),
      {
        ...prev.filter((x) => x.linkId === questData?.linkId)[0],
        linkId: questData?.linkId,
        answer: [
          questData.type === 'choice'
            ? {
                valueCoding: {
                  code:
                    questData?.answerOption?.find((x: any) => x?.id === val).id ||
                    val?.value ||
                    prev.find((x) => x.linkId === questData?.linkId).answer[0].valueCoding.code,
                  display:
                    questData?.answerOption?.find((x: any) => x?.id === val).valueString ||
                    val?.label ||
                    prev.find((x) => x.linkId === questData?.linkId).answer[0].valueCoding.display,
                  system: '',
                },
              }
            : { valueDate: val },
        ],
      },
    ]);
  };

  useEffect(() => {
    if (answerList && answerList.length > 0 && answerList[0] && answerList[0]?.item) {
      answerList[0]?.item.forEach((element: any) => {
        setPayloadArr((prev) => [
          ...prev.filter((x) => x.linkId !== element?.linkId),
          {
            ...prev.filter((x) => x.linkId === element?.linkId)[0],
            linkId: element?.linkId,
            answer: [
              element?.answer?.[0]?.valueCoding
                ? {
                    valueCoding: {
                      code: element?.answer?.[0]?.valueCoding?.code,
                      display: element?.answer?.[0]?.valueCoding?.display,
                      system: '',
                    },
                  }
                : { valueDate: element?.answer?.[0]?.valueDate },
            ],
          },
        ]);
      });
    }
  }, []);

  const onSubmit = async (item: any[], isClear?: boolean) => {
    try {
      const apiPayload: QuestionnaireResponsePayload = {
        questionnaire: `${MEDPLUM_QUESTIONNAIRE}/ReproductiveHealth`,
        item,
      };
      const deletePayload: any = {
        deleteTask: 'Delete Reproductive Health',
        identifier: 'urn:fh-workflow:task:delete:reproductive-health',
        questionnaireId: answerList[0]?.id,
      };
      if (!isClear) {
        if (!saveEnable || payloadArr.length !== item.length) {
          return;
        }
      }

      // Prepare all properties for tracking
      const allProperties: Record<string, any> = {};
      if (!isClear) {
        payloadArr.forEach((items) => {
          const linkId = items.linkId.replace(/-/g, '_');
          let value;

          if (items.answer[0].valueCoding) {
            value = items.answer[0].valueCoding.display;
          } else if (items.answer[0].valueDate) {
            value = items.answer[0].valueDate;
          }

          // Map linkId to the correct property name
          const propMapping: Record<string, string> = {
            reproductive_health_sexually_active: 'rh_sexually_active',
            reproductive_health_last_menstrual_period_date: 'rh_last_menstrual_date',
            reproductive_health_ever_been_pregnant: 'rh_pregnancy_history',
            reproductive_health_terminated_pregnancy: 'rh_pregnancy_terminated',
            reproductive_health_do_you_have_children: 'rh_children',
            reproductive_health_you_ever_miscarried: 'rh_miscarriage',
            reproductive_health_you_undergone_fertility_treatments: 'rh_fertility_treatments',
            reproductive_health_current_pregnancy_status: 'rh_pregnancy_status',
          };

          if (propMapping[linkId] && value) {
            allProperties[propMapping[linkId]] = value;
          }
        });
      }

      if (isClear) {
        await deleteQuestionnaireResponseTask(deletePayload);

        recordReproductiveHealthEvents(trackEventInFlow, {
          EventName: 'ReproductiveHealthRemoved',
        });
      } else if (!isClear && answerList && answerList?.length > 0) {
        apiPayload.id = answerList[0]?.id;
        await updateQuestionnaireResponse(apiPayload);

        recordReproductiveHealthEvents(trackEventInFlow, {
          EventName: 'ReproductiveHealthEdited',
          ...allProperties,
        });
      } else {
        await addQuestionnaireResponse(apiPayload);

        recordReproductiveHealthEvents(trackEventInFlow, {
          EventName: 'ReproductiveHealthAddCompleted',
          ...allProperties,
        });
      }

      toast({
        title: `Successfully added the reproductive health`,
        status: 'success',
        duration: 4000,
        isClosable: true,
      });

      setSubmitPayload([]);
      setSaveEnable(false);
    } catch (err) {
      toast({
        title: 'Something went wrong. Please try again.',
        status: 'error',
        duration: 4000,
        isClosable: true,
      });
    }
  };

  useEffect(() => {
    setSubmitPayload(payloadArr);
  }, [payloadArr]);

  return isPublicMode && payloadArr.length <= 0 ? (
    <SidebarEmptyState
      title=""
      actionButtonText=""
      imageSrc=""
      isPublicMode={isPublicMode}
    />
  ) : (
    <Flex
      paddingBottom="32px"
      direction="column"
    >
      <Card
        p="20px"
        borderRadius="10px"
        boxShadow="0px 1px 4px rgba(73, 90, 228, 0.12)"
      >
        <VStack spacing="8">
          {masterList?.length &&
            masterList?.[0]?.item.map((question: any) => (
              <QuestionItem
                key={question.linkId}
                question={question}
                answer={payloadArr.find((x) => x.linkId === question.linkId)}
                formatPayloadArr={formatPayloadArr}
                isPublicMode={isPublicMode}
                masterList={masterList}
              />
            ))}
        </VStack>
      </Card>
      {!isPublicMode && payloadArr.length > 0 && (
        <Flex
          justifyContent="flex-end"
          mt="20px"
        >
          <Button
            variant="link"
            onClick={() => {
              setPayloadArr([]);
              setSubmitPayload([]);
              onSubmit([], true);
              // trackEventInFlow(AnalyticsFlow.ReproductiveHealthManaged, AnalyticsEventName.ReproductiveHealthManaged, {
              //   [EventPropsNames.FlowName]: 'Profile',
              //   [EventPropsNames.Action]: 'Clear all',
              //   [EventPropsNames.ScreenName]: 'Reproductive health screen',
              //   [EventPropsNames.StepCompleted]: 'reproductive health cleared',
              //   [EventPropsNames.CompletedSuccess]: false,
              // });
            }}
            mr="32px"
            color="red.100"
          >
            Clear All
          </Button>
          <Button
            isLoading={isLoading}
            disabled={submitPayload.length === 0}
            onClick={() => onSubmit(submitPayload)}
          >
            Save
          </Button>
        </Flex>
      )}
    </Flex>
  );
}
