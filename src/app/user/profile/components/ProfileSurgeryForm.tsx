import {
  Box,
  Button,
  Flex,
  FormControl,
  FormErrorMessage,
  FormLabel,
  HStack,
  Input,
  Stack,
  Text,
  useDisclosure,
  useToast,
} from '@chakra-ui/react';
import { useCallback, useEffect, useState } from 'react';
import { FormProvider, useForm, useFormContext } from 'react-hook-form';
import dayjs from 'dayjs';
import { useProcedureList } from '@user/lib/medplum-state';
import { QuestionnaireResponse } from '@gql/graphql';
import { useNavigate } from 'react-router-dom';
import { DOCUMENT_REF, NavigationHelper, PATIENT_DEFINED } from '@user/lib/constants';
import { recordSurgeryEvents } from '@user/lib/events-analytics-manager';
import { medplumApi } from '@user/lib/medplum-api';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';

import { useAnalyticsService, useAuthService } from '@lib/state';
import { SearchableSelect, SelectOptionProps } from '../../../../components/ui/Select';
import { ProcedureType } from '@lib/models/misc';
import { DatePickerField, SuggestionOptionProps } from 'src/components/ui/Form';
import { Surgery } from '@lib/models/surgery-proceudure';
import { LinkedDocumentsCard } from './LinkedDocumentsCard';
import { AttachedMedicalRecord } from '@lib/models/medical-record';
import { MedicalRecordSelect } from './MedicalRecordSelect';
import { FHIR_CODE_SYSTEM_URL } from '@lib/constants';
import { useExtractDocumentResource } from 'src/app/medical-records/lib/state';
import { formatDateForSave, isDuplicatePresentProcedures } from '@lib/utils/utils';

type SurgeryFormValues = Omit<Surgery, 'surgery' | 'id'> & {
  surgery: SuggestionOptionProps | null;
  notes: string;
  procedure_status: string;
  custom_surgery?: string;
};
const customOption = { label: 'My surgery and/or procedure is not listed', value: 'MY_PROCEDURE_NOT_LISTED' };
export function NoOptionsMessageComponent({ onClick }: { onClick: () => void }) {
  return (
    <Text
      sx={{
        width: '100%',
        textAlign: 'left',
        color: 'fluentHealthText.100',
        cursor: 'pointer',
      }}
      onClick={onClick}
    >
      My surgery and/or procedure is not listed
    </Text>
  );
}
const getInitialFormData = (surgery: any | null): SurgeryFormValues => {
  const date = surgery?.performedPeriod?.start;
  const primary = surgery?.code?.coding?.[0] ?? { code: '', display: '' };
  const isCustom = primary.code === `pro:${PATIENT_DEFINED}`;
  return {
    surgery_date: date?.length > 0 && dayjs(date).isValid() ? date : null,
    procedure_type: surgery?.length > 0 ? surgery?.code?.coding[0]?.display : ProcedureType.NonSurgical,
    surgery: {
      value: isCustom ? customOption.value : surgery?.code?.coding[0]?.code || '',
      label: isCustom ? customOption.label : surgery?.code?.coding[0]?.display || '',
    },
    external_reports: surgery?.report ? useExtractDocumentResource(surgery.report) : [],
    notes: surgery?.note?.[0]?.text || '',
    procedure_status: surgery?.status || '',
    custom_surgery: isCustom ? primary.display : '',
  };
};

const getSurgeryOption = (surgery: any | null): SuggestionOptionProps | null => {
  if (!surgery) {
    return null;
  }
  const primary = surgery?.code?.coding?.[0] ?? { code: '', display: '' };
  const isCustom = primary.code === `pro:${PATIENT_DEFINED}`;
  return {
    value: isCustom ? customOption.value : surgery?.code?.coding[0]?.code || '',
    label: isCustom ? customOption.label : surgery?.code?.coding[0]?.display || '',
  };
};

function SurgerySelect({
  surgery,
  onAfterSelect,
  procedureOptions,
  setShowCustomInput,
}: {
  surgery: QuestionnaireResponse | null;
  onAfterSelect?: (value: SuggestionOptionProps) => void;
  procedureOptions: SelectOptionProps[];
  setShowCustomInput: (flag: boolean) => void;
}) {
  const form: any = useFormContext();
  const [surgeryValue, setSurgeryValue] = useState<SuggestionOptionProps | null>(getSurgeryOption(surgery));
  const [menuIsOpen, setMenuIsOpen] = useState(false);
  const [inputValue, setInputValue] = useState('');
  const handleNoOptionsClick = useCallback(() => {
    setSurgeryValue(customOption);
    setShowCustomInput(true);
    setInputValue('');
    setMenuIsOpen(false);
    form.setValue('surgery', customOption);
  }, [setShowCustomInput]);

  const { trackEventInFlow } = useAnalyticsService();
  const options = procedureOptions; // formatSurgeryListToSuggestionList(suggestionList);
  const handleFocus = useCallback(() => {
    if (surgeryValue?.value === customOption.value) {
      setSurgeryValue(null);
      form.setValue('surgery', '');
      setShowCustomInput(false);
    }
  }, [surgeryValue, form, setShowCustomInput]);

  const onChange = useCallback((option: SelectOptionProps | any) => {
    if (option) {
      form.setValue('surgery.label', option.label);
      form.setValue('surgery.value', option.value);
      form.trigger('surgery');
      setSurgeryValue(option);
      if (!surgery) {
        recordSurgeryEvents(trackEventInFlow, {
          EventName: 'SurgeriesAddInProgType',
          sur_entry_point: 'my_health_profile',
          sur_type: option?.value,
        });
      }
    } else {
      form.setValue('surgery.label', '');
      form.setValue('surgery.value', '');
      form.trigger('surgery');
      setSurgeryValue(null);
    }
    onAfterSelect?.(option);
  }, []);
  const noOptionsMessageFn = useCallback(
    () => <NoOptionsMessageComponent onClick={handleNoOptionsClick} />,
    [handleNoOptionsClick]
  );
  return (
    <FormControl
      variant="floating"
      isInvalid={!!form.formState.errors?.surgery}
    >
      <SearchableSelect
        labelText="Type of surgery and/or procedure*"
        value={surgeryValue}
        options={options}
        onChange={onChange}
        onFocus={handleFocus}
        menuIsOpen={menuIsOpen}
        onMenuOpen={() => setMenuIsOpen(true)}
        onMenuClose={() => setMenuIsOpen(false)}
        inputValue={inputValue}
        onInputChange={setInputValue}
        noOptionsMessage={noOptionsMessageFn}
      />
      <FormErrorMessage> {form?.formState?.errors?.surgery?.value?.message} </FormErrorMessage>
    </FormControl>
  );
}

export default function ProfileSurgeryForm({
  surgery,
  procedureOptions,
  procedureStatus,
  closeDialog,
  loader,
}: {
  surgery: any | null;
  procedureOptions: SelectOptionProps[];
  procedureStatus: SelectOptionProps[];
  closeDialog: () => void;
  loader: (loading: boolean) => void;
}) {
  const toast = useToast();
  const navigate = useNavigate();
  const isEditing = !!surgery;

  const [isLoading] = useState<boolean>(false);
  const [isFormValid, setIsFormValid] = useState(false);
  const [showCustomInput, setShowCustomInput] = useState(
    surgery?.code.coding[0].code === `pro:${PATIENT_DEFINED}` || false
  );

  const datePickerPopover = useDisclosure();

  const { authenticatedUser } = useAuthService();

  const { procedureList, addSurgeryProcedure, updateSurgeryProcedure } = useProcedureList(authenticatedUser?.id);

  const { trackEventInFlow } = useAnalyticsService();
  const surgeryFormSchema = z.object({
    surgery: z.object({
      value: z
        .string()
        .min(1, 'Surgery type is required')
        .refine((value) => {
          if (value === customOption.value) {
            // eslint-disable-next-line @typescript-eslint/no-use-before-define
            form.clearErrors('surgery.value');
            return true;
          }
          const duplicateId = isDuplicatePresentProcedures(value, procedureList.ProcedureList, surgery);
          return !duplicateId;
        }, 'This procedure already exists'),
      label: z.string(),
    }),
    custom_surgery: showCustomInput ? z.string().min(1, 'This field is required') : z.string().optional(),
    procedure_status: z.string().min(1, 'Procedure status is required'),
    surgery_date: z.string().optional().nullable(),
    notes: z.string().max(500, 'Notes cannot exceed 500 characters').optional().nullable(),
    external_reports: z.array(z.any()).optional(),
  });

  const form = useForm<SurgeryFormValues>({
    resolver: zodResolver(surgeryFormSchema),
    mode: 'onChange',
    defaultValues: getInitialFormData(surgery),
  });
  const {
    handleSubmit,
    register,
    formState: { isSubmitting, isValid },
  } = form;
  const dateField = form.watch('surgery_date');
  const externalReportsField = form.watch('external_reports');
  const [surgeryStatus, setSurgeryStatus] = useState<any>(form.watch('procedure_status'));

  async function onSubmit(values: any) {
    try {
      loader(true);
      const { procedure_status, notes, surgery_date, external_reports, custom_surgery } = values;
      let coding: any = [];
      if (values.surgery.value !== customOption.value) {
        const codeValue = values.surgery.value;
        coding = await medplumApi.valueSetList.getFHIRCodingFromCMS(codeValue);
      } else {
        coding = [
          {
            system: 'http://fluentinhealth/fact',
            code: `pro:${PATIENT_DEFINED}`,
            display: custom_surgery,
          },
        ];
      }
      const newPayload = {
        surgeryProcedure: { coding },
        status: procedure_status,
        ...(notes && { note: [{ text: notes }] }),
      };

      const alreadyAddedLinkages = surgery?.report;
      const linkageToList =
        external_reports
          ?.filter((er: any) => !alreadyAddedLinkages?.includes(er.docRefId))
          .map((item: any) => ({ reference: `${DOCUMENT_REF}/${item.id}` })) || [];

      const payloadSurgeryProcedure = {
        resourceType: 'Procedure',
        status: newPayload.status,
        id: surgery?.id,
        identifier: [{ system: `${FHIR_CODE_SYSTEM_URL}/FACT`, value: 'FACT-pro' }],
        code: newPayload.surgeryProcedure,
        note: newPayload.note,
        subject: { reference: `Patient/${authenticatedUser?.id}` },
        ...(surgery_date && { performedPeriod: { start: formatDateForSave(surgery_date) } }),
        report: linkageToList,
      };

      if (surgery?.id) {
        await updateSurgeryProcedure({ procedureId: surgery.id, procedurePayload: payloadSurgeryProcedure });
      } else {
        await addSurgeryProcedure(payloadSurgeryProcedure);
      }
      recordSurgeryEvents(trackEventInFlow, {
        EventName: surgery?.id ? 'SurgeriesEdited' : 'SurgeriesAddCompleted',
        sur_entry_point: 'my_health_profile',
        sur_type: values.surgery.value,
        sur_notes: String(notes),
        sur_date: dayjs(surgery_date).format('YYYY/MM/DD'),
        sur_status: String(procedure_status),
        sur_records_added: !!external_reports?.length,
      });
      toast({
        title: `Successfully ${isEditing ? 'edit' : 'add'}ed the procedure`,
        status: 'success',
        duration: 4000,
        isClosable: true,
      });
      closeDialog?.();
      if (!isEditing) {
        navigate(NavigationHelper.getEhrView(false, 'procedures'));
      }
    } catch (_err) {
      toast({
        title: 'Something went wrong. Please try again.',
        status: 'error',
        duration: 4000,
        isClosable: true,
      });
    } finally {
      loader(false);
    }
  }

  const removeAttachedMedicalRecordHandler = (record: AttachedMedicalRecord) => {
    form.setValue(
      'external_reports',
      externalReportsField.filter((item) => item.id !== record.id)
    );
  };

  const datePickerChangeHandler = (date: Date | null) => {
    if (dayjs(date).isValid()) {
      form.setValue('surgery_date', dayjs(date).format('YYYY-MM-DD'));
    } else {
      form.setValue('surgery_date', '');
    }
    datePickerPopover.onClose();
    if (!surgery) {
      recordSurgeryEvents(trackEventInFlow, {
        EventName: 'SurgeriesAddInProgDate',
        sur_entry_point: 'my_health_profile',
        sur_date: dayjs(date).format('YYYY/MM/DD'),
      });
    }
  };

  const datePickerClearHandler = () => {
    form.setValue('surgery_date', '');
    datePickerPopover.onClose();
  };

  const formatStatus = (status: string): string =>
    status
      ?.split('-')
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join('-');

  useEffect(() => {
    const subscription = form.watch(() => {
      setIsFormValid(true);
    });

    return () => subscription.unsubscribe();
  }, [form]);
  return (
    <FormProvider {...form}>
      <Flex
        direction="column"
        paddingTop="20px"
      >
        <Box
          color="black"
          display="flex"
          flexDirection="column"
          gap="7"
        >
          <SurgerySelect
            surgery={surgery}
            procedureOptions={procedureOptions}
            setShowCustomInput={setShowCustomInput}
          />
          {showCustomInput && (
            <FormControl
              isInvalid={!!form.formState.errors.custom_surgery}
              variant="floating"
              isRequired
              mt={2}
            >
              <Input
                defaultValue={form.watch('custom_surgery') || ''}
                placeholder=" "
                {...form.register('custom_surgery')}
                onChange={(e) => {
                  form.setValue('custom_surgery', e?.target?.value);
                  form.trigger('custom_surgery');
                }}
                onBlurCapture={(e) => {
                  if (e.target.value && !surgery) {
                    recordSurgeryEvents(trackEventInFlow, {
                      EventName: 'SurgeriesAddInProgType',
                      sur_entry_point: 'my_health_profile',
                      sur_type: e.target.value.toString(),
                    });
                  }
                }}
              />
              <FormLabel>Enter the surgery and/or procedure*</FormLabel>
            </FormControl>
          )}

          <Stack
            spacing={2}
            mt={4}
          >
            <SearchableSelect
              name="procedure_status"
              labelText="Status of surgery and/or procedure*"
              options={procedureStatus.map((item: any) => ({
                value: item.code,
                label: item.display,
              }))}
              value={{ value: surgeryStatus, label: formatStatus(surgeryStatus) }}
              onChange={(option: SelectOptionProps) => {
                form.setValue('procedure_status', String(option?.value));
                form.trigger('procedure_status');
                setSurgeryStatus(option?.value);
                if (!surgery) {
                  recordSurgeryEvents(trackEventInFlow, {
                    EventName: 'SurgeriesAddInProgStatus',
                    sur_entry_point: 'my_health_profile',
                    sur_status: String(option?.label),
                  });
                }
              }}
              isClearable={false}
              style={{ textTransform: 'capitalize' }}
            />
          </Stack>
          <Flex
            flex={1}
            mt={6}
          >
            <DatePickerField
              // Field props
              name="surgery_date"
              labelText="Date of surgery and/or procedure"
              errorText="Invalid Date format"
              rules={{ required: false }}
              datePickerChangeHandler={datePickerChangeHandler}
              datePickerClearHandler={datePickerClearHandler}
              datePickerPopover={datePickerPopover}
              isClearDateButtonDisabled={dateField?.length === 0}
              selected={dayjs(dateField).isValid() ? dayjs(dateField).toDate() : null}
              maxDate={new Date()}
            />
          </Flex>
          <FormControl
            variant="floating"
            mt={3}
            isInvalid={!!form.formState.errors.notes}
          >
            <Input
              placeholder=" "
              {...register('notes')}
              onBlurCapture={(e) => {
                if (!surgery) {
                  recordSurgeryEvents(trackEventInFlow, {
                    EventName: 'SurgeriesAddInProgNotes',
                    sur_entry_point: 'my_health_profile',
                    sur_notes: String(e?.target?.value),
                  });
                }
              }}
            />
            <FormLabel fontWeight="normal">Add your notes here</FormLabel>
          </FormControl>
          <Stack
            spacing={2}
            mt={4}
          >
            <MedicalRecordSelect
              labelText="Link health records"
              onSelectExtra={() => {
                if (!surgery) {
                  recordSurgeryEvents(trackEventInFlow, {
                    EventName: 'SurgeriesAddInProgRecordsAdded',
                    sur_entry_point: 'my_health_profile',
                    sur_records_added: !!externalReportsField?.length,
                  });
                }
              }}
            />
            {externalReportsField.length > 0 && (
              <LinkedDocumentsCard
                records={externalReportsField}
                onRemove={removeAttachedMedicalRecordHandler}
                showRemoveButton
              />
            )}
          </Stack>
        </Box>
        <HStack
          justifyContent="flex-end"
          mt="12"
        >
          <Button
            isDisabled={!isValid || isSubmitting || isLoading || !isFormValid}
            isLoading={isSubmitting || isLoading}
            onClick={handleSubmit(onSubmit)}
          >
            {isEditing ? 'Save' : 'Add'}
          </Button>
        </HStack>
      </Flex>
    </FormProvider>
  );
}
