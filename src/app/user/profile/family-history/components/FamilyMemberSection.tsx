/* eslint-disable import/order */
import React, { Suspense, useEffect, useState } from 'react';
import { ChevronRightIcon } from '@chakra-ui/icons';
import { Edit3 as PenIcon } from 'react-feather';
import {
  Card,
  CardBody,
  Divider,
  Drawer,
  DrawerContent,
  DrawerOverlay,
  Flex,
  Heading,
  IconButton,
  Spacer,
  Text,
  useDisclosure,
  useTheme,
} from '@chakra-ui/react';
import { SidebarConditions } from './SidebarConditions';
import { useAnalyticsService, useAuthService, usePublicSettings } from '@lib/state';
import GenericProfileCard from '../../components/GenericProfileCard';
import { useFamilyMemberHistoryList } from '../../../lib/medplum-state';
import { hexOpacity } from '../../../../../components/theme/utils';
import { FamilyMemberForm } from './FamilyMemberForm';
import { Modal } from 'src/components/Modal';
import { getAge } from 'src/utils/utils';
import { FluentHealthLoader } from 'src/components/FluentHealthLoader';
import { FamilyMemberHistory } from '@lib/models/family-member-history';
import { recordFamilyMemberHistoryEvents } from '@user/lib/events-analytics-manager';
import { useIsDesktop } from '@components/ui/hooks/device.hook';
import { medplumApi } from '@user/lib/medplum-api';
import { FACT_CODE_SYSTEM } from '@lib/constants';

function FamilyMemberCard({ member, onEditButtonClick }: { member: any; onEditButtonClick: Function }) {
  const theme = useTheme();
  const { isPublicMode } = usePublicSettings();
  const { trackEventInFlow } = useAnalyticsService();
  const isDesktop = useIsDesktop();
  const [hoveredElement, setHoveredElement] = useState<string | null>(null);
  const [bloodGroup, setBloodGroup] = useState<any[]>([]);
  const [ethnicityGrp, setEthnicityGrp] = useState<any[]>([]);

  const handleMouseEnter = (contactId: string) => {
    setHoveredElement(contactId);
  };

  const handleMouseLeave = () => {
    setHoveredElement(null);
  };

  useEffect(() => {
    Promise.all([
      medplumApi.valueSetList.getAllValueSetFromDirectus(isPublicMode, FACT_CODE_SYSTEM.BLOOD_GROUP),
      medplumApi.valueSetList.getAllValueSetFromDirectus(isPublicMode, FACT_CODE_SYSTEM.ETHNICITY),
    ]).then(([inBlood_type, inEthnicity]) => {
      setBloodGroup(inBlood_type.map((e: { display: string; code: string }) => ({ label: e.display, value: e.code })));
      setEthnicityGrp(inEthnicity.map((e: { display: string; code: string }) => ({ label: e.display, value: e.code })));
    });
  }, []);

  const showFamilyMembersConditions = isPublicMode ? member?.conditions?.length > 0 : true;
  const ethnicityURL =
    member?.extension && member?.extension.find((extension: any) => extension.url.includes('FACT-eth'));
  const bloodGroupURL =
    member?.extension && member?.extension.find((extension: any) => extension.url.includes('FACT-Category-bg'));
  const bloodType = bloodGroup.find((item) => item.value === bloodGroupURL?.valueCode);
  const ethnicity = ethnicityGrp.find((item) => item.value === ethnicityURL?.valueCode);
  const conditionSidebar = useDisclosure();

  const openConditionsSidebar = () => {
    conditionSidebar.onOpen();
    recordFamilyMemberHistoryEvents(trackEventInFlow, {
      EventName: 'FMConditionAddStarted',
      fm_id: member?.id,
    });
  };

  return (
    <>
      <Drawer
        isOpen={conditionSidebar.isOpen}
        placement="right"
        onClose={conditionSidebar.onClose}
        size="sm"
        blockScrollOnMount={false}
      >
        <DrawerOverlay />
        <DrawerContent bg="gradient.profileDrawer">
          <Suspense
            fallback={
              <FluentHealthLoader
                position="absolute"
                top={0}
                bottom={0}
                left={0}
                right={0}
                my="auto"
              />
            }
          >
            <SidebarConditions
              relatedPerson={member}
              onClose={conditionSidebar.onClose}
            />
          </Suspense>
        </DrawerContent>
      </Drawer>
      <Flex
        gap={{ base: '16px', md: '20px' }}
        direction={{ base: 'column', md: 'row' }}
      >
        <Card
          bgColor="fluentHealthSecondary.500"
          w={{ base: 'full', md: '240px' }}
          boxShadow="none"
          borderRadius="8px"
          role="group"
          onMouseEnter={() => handleMouseEnter(member.id)}
          onMouseLeave={handleMouseLeave}
        >
          <CardBody
            padding="12px 8px 12px 12px"
            display="flex"
            flexDirection="column"
            justifyContent="space-between"
          >
            <Flex
              direction="column"
              px="2"
              gap="1"
            >
              {!isPublicMode && (
                <>
                  <Heading
                    fontSize="20px"
                    color="fluentHealthSecondary.100"
                    lineHeight="8"
                  >
                    {member?.name || ''}
                  </Heading>
                  <Heading
                    fontSize="16px"
                    color="fluentHealthSecondary.200"
                    mt="4px"
                  >
                    {member?.relationship?.coding[0]?.display || ''}
                  </Heading>
                </>
              )}
            </Flex>
            <Flex
              height="28px"
              px="2"
              justifyContent="end"
              alignItems="center"
              mt={{ base: '1', md: '0' }}
            >
              {!isPublicMode && ((isDesktop && hoveredElement === member.id) || !isDesktop) && (
                <IconButton
                  aria-label="Edit"
                  variant="ghost"
                  size="sm"
                  pos="absolute"
                  right="22px"
                  bottom="18px"
                  color="fluentHealthSecondary.100"
                  icon={<PenIcon size={16} />}
                  onClick={() => (isPublicMode ? null : onEditButtonClick(member))}
                />
              )}
            </Flex>
          </CardBody>
        </Card>
        <Flex
          direction="column"
          flex={1}
          gap="16px"
        >
          <Flex
            justifyContent="space-between"
            direction={{ base: 'column', md: 'row' }}
            gap={{ base: '8px', md: '0' }}
            paddingBottom={{ base: '2', md: '3' }}
            borderBottom="1px solid"
            borderColor="gray.100"
            mx="2"
          >
            <Text color={{ base: 'gray.300', md: 'iris.500' }}>Age</Text>
            <Text color="fluentHealthText.100">
              {member?.bornDate && !Number.isNaN(getAge(member?.bornDate)) ? getAge(member?.bornDate) : '—'}
            </Text>
          </Flex>
          <Flex
            justifyContent="space-between"
            direction={{ base: 'column', md: 'row' }}
            gap={{ base: '8px', md: '0' }}
            paddingBottom={{ base: '2', md: '3' }}
            borderBottom={{ base: 'none', md: '1px solid' }}
            borderColor={{ base: 'none', md: 'gray.100' }}
            mx="2"
          >
            <Text color="iris.500">Status</Text>
            <Text color="fluentHealthText.100">
              {member?.deceasedBoolean === true ? 'Deceased' : member?.deceasedBoolean === false ? 'Living' : '—'}
            </Text>
          </Flex>
          <Flex
            justifyContent="space-between"
            mx="2"
            pb="2"
            display={{ base: 'none', md: 'flex' }}
          >
            <Text color={{ base: 'gray.300', md: 'iris.500' }}>Blood group</Text>
            <Text color="fluentHealthText.100">{bloodType?.label || '—'}</Text>
          </Flex>
          <Flex
            justifyContent="space-between"
            mx="2"
            pb="2"
            display={{ base: 'none', md: 'flex' }}
          >
            <Text color="iris.500">Ethnicity</Text>
            <Text color="fluentHealthText.100">{ethnicity?.label || '—'}</Text>
          </Flex>
          <Flex justifyContent="space-between">
            <Flex
              alignItems="center"
              direction="column"
              w="100%"
            >
              <Card
                bgColor="transparent"
                borderRadius="xl"
                border="1px solid"
                borderColor="periwinkle.400"
                boxShadow={`0px 1px 4px ${hexOpacity(theme.colors.royalBlue['500'], 0.12)}`}
                w="100%"
              >
                <Flex
                  direction="column"
                  p="2"
                  gap="1"
                >
                  {showFamilyMembersConditions && (
                    <Flex
                      h="12"
                      alignItems="center"
                      px="2"
                      fontSize="lg"
                      fontWeight="medium"
                      lineHeight="short"
                      _hover={{ cursor: 'pointer', bgColor: 'periwinkle.200', borderRadius: 'lg' }}
                      onClick={() => openConditionsSidebar()}
                    >
                      Conditions
                      <Spacer />
                      <ChevronRightIcon
                        fontSize="xl"
                        color="papaya.600"
                      />
                    </Flex>
                  )}
                </Flex>
              </Card>
            </Flex>
          </Flex>
        </Flex>
      </Flex>
    </>
  );
}

function FamilyMemberSection({
  familyMemberList,
  onAddButtonClick,
  onEditButtonClick,
}: {
  familyMemberList: FamilyMemberHistory;
  onAddButtonClick: () => void;
  onEditButtonClick: (value: FamilyMemberHistory) => void;
}) {
  if (!Array.isArray(familyMemberList) || !familyMemberList.length) {
    return (
      <GenericProfileCard
        title="Update family member details"
        info="Your family medical history is a powerful tool for managing your health and uncovering health risks. Add blood relatives here-like parents, grandparents, siblings-to help guide diagnoses and treatments."
        actionButtonText="family members"
        actionButton={onAddButtonClick}
        // profileCompletionPercentage={5}
      />
    );
  }

  return (
    <GenericProfileCard
      title="FAMILY MEMBERS"
      titleTextColor="charcoal.100"
      // info="Your family medical history is a powerful tool for managing your health and uncovering health risks. Add blood relatives here—like parents, grandparents, siblings—to help guide diagnoses and treatments."
      actionButton={onAddButtonClick}
    >
      <Flex
        direction="column"
        gap={{ base: '6', md: '5' }}
      >
        {familyMemberList &&
          familyMemberList.map((member: any, index: number) => {
            return (
              <React.Fragment key={member?.id}>
                <FamilyMemberCard
                  member={member}
                  onEditButtonClick={onEditButtonClick}
                />
                {index !== familyMemberList.length - 1 && <Divider borderColor="gray.100" />}
              </React.Fragment>
            );
          })}
      </Flex>
    </GenericProfileCard>
  );
}

export function FamilyMemberSections() {
  const familyMemberModal = useDisclosure();
  const [currentFamilyMember, setCurrentFamilyMember] = useState<FamilyMemberHistory>({});
  const { authenticatedUser } = useAuthService();
  const { trackEventInFlow } = useAnalyticsService();
  const { familyMemberList } = useFamilyMemberHistoryList(authenticatedUser?.id);

  const relationships = familyMemberList?.FamilyMemberHistoryList?.map(
    (member: any) => member.relationship?.coding?.[0]?.display
  );

  const onceAddedRelationipLabel = [
    'Natural Mother',
    'Natural Father',
    'Adoptive Mother',
    'Adoptive Father',
    'Step Mother',
    'Step Father',
  ];
  // Filter relationships that match with onceAddedRelationipLabel
  const matchingRelationships = relationships?.filter((relationship: string) =>
    onceAddedRelationipLabel.includes(relationship)
  );

  const onAddButtonClick = () => {
    setCurrentFamilyMember({});
    familyMemberModal.onOpen();
    recordFamilyMemberHistoryEvents(trackEventInFlow, {
      EventName: 'FamilyMemberHistoryAddStarted',
      fm_entry_point: 'family_history',
    });
  };

  const onEditButtonClick = (familyMember: FamilyMemberHistory) => {
    setCurrentFamilyMember(familyMember);
    familyMemberModal.onOpen();
    recordFamilyMemberHistoryEvents(trackEventInFlow, {
      EventName: 'FamilyMemberEditStarted',
    });
  };

  return (
    <>
      <FamilyMemberSection
        familyMemberList={familyMemberList?.FamilyMemberHistoryList}
        onAddButtonClick={onAddButtonClick}
        onEditButtonClick={onEditButtonClick}
      />
      <Modal
        title="Family Member"
        showModalFooter={false}
        isCentered
        {...familyMemberModal}
        scrollY="false"
      >
        <FamilyMemberForm
          familyMember={currentFamilyMember}
          relationships={matchingRelationships}
          closeDialog={familyMemberModal.onClose}
        />
      </Modal>
    </>
  );
}
