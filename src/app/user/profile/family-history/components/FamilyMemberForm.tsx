// Package modules
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import {
  Button,
  FormControl,
  FormLabel,
  Grid,
  GridItem,
  HStack,
  Input,
  Text,
  useDisclosure,
  useToast,
} from '@chakra-ui/react';
import { FormProvider, useForm } from 'react-hook-form';
import { Trash as TrashIcon } from 'react-feather';
import dayjs from 'dayjs';
import { z } from 'zod';
import _ from 'lodash';
import { zodResolver } from '@hookform/resolvers/zod/dist/zod';
// Local modules
import { recordFamilyMemberHistoryEvents } from '@user/lib/events-analytics-manager';

import { Select, SelectOptionProps } from '../../../../../components/ui/Select';
import { useAnalyticsService, useAuthService } from '@lib/state';
import { DatePickerField } from 'src/components/ui/Form';
import { parseValueSet } from '@lib/utils/utils';
// import { AnalyticsService, PatientPropsNames } from '@lib/analyticsService';
import { useFamilyMemberHistoryList, useValueSet } from '../../../lib/medplum-state';
import {
  ConsentModal,
  ConsentModalContent,
  ConsentModalFooter,
  ConsentModalHeading,
  ConsentModalPrimaryButton,
  ConsentModalSecondaryButton,
} from '../../components/ConsentModal';
import { FamilyMemberHistory, FamilyMemberHistoryPayload } from '@lib/models/family-member-history';

// Constants

type FamilyMemberHistoryFormValues = FamilyMemberHistory;
// Helpers
function getInitialFormData(
  familyMember: any,
  ethnicityOptions: any,
  bloodGroupOptions: any
): FamilyMemberHistoryFormValues {
  const ethnicityURL =
    familyMember?.extension && familyMember?.extension.find((extension: any) => extension.url.includes('FACT-eth'));
  const bloodGroupURL =
    familyMember?.extension && familyMember?.extension.find((extension: any) => extension.url.includes('FACT-bg'));
  const bloodType = bloodGroupOptions.find((item: any) => item.value === bloodGroupURL?.valueCode);
  const ethnicity = ethnicityOptions.find((item: any) => item.value === ethnicityURL?.valueCode);
  const date = familyMember?.bornDate;
  const statusFlag =
    familyMember && (familyMember?.deceasedBoolean === true || familyMember?.deceasedBoolean === false)
      ? familyMember?.deceasedBoolean
      : '';
  const relation = familyMember?.relationship?.coding[0];
  return {
    relationship: {
      value: relation?.code || '',
      label: relation?.display || '',
    },
    date_of_birth: dayjs(date).isValid() ? date : '',
    blood_type: {
      value: bloodType?.value || '',
      label: bloodType?.label || '',
    },
    status: statusFlag,
    ethnicity,
    name: familyMember?.name || '',
  };
}

export function FamilyMemberForm({
  familyMember,
  closeDialog,
  relationships,
}: {
  familyMember?: FamilyMemberHistory;
  closeDialog: (familyMember?: any) => void;
  relationships: any[];
}) {
  // Add/edit form type
  const isEditForm = familyMember && Object.keys(familyMember).length > 0;
  // Determine copies based on form type
  const buttonText = isEditForm ? 'Save changes' : 'Add';

  const toast = useToast();
  const [, setError] = useState<any>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false); // Used for contact remove -- cannot bind to form's isLoading
  const { authenticatedUser } = useAuthService();
  const { valueSetList } = useValueSet();
  const { trackEventInFlow } = useAnalyticsService();
  const OPTIONS_RELATION: any = parseValueSet(valueSetList?.fmhRelationshipValueSet);
  const OPTIONS_STATUS: any = parseValueSet(valueSetList?.fmhHealthStatus);
  const OPTIONS_BLOOD_TYPE: any = parseValueSet(valueSetList?.bloodGroupValueSet);
  const OPTIONS_ETHNICITY = useMemo(() => {
    if (valueSetList?.ethnicityValueSet) {
      return parseValueSet(valueSetList.ethnicityValueSet);
    }
    return [];
  }, [valueSetList?.ethnicityValueSet]);

  const { addFamilyMember, updateFamilyMember, deleteFamilyMember } = useFamilyMemberHistoryList(authenticatedUser?.id);

  const form = useForm<FamilyMemberHistoryFormValues>({
    mode: 'all',
    resolver: zodResolver(
      z.object({
        relationship: z.object({
          value: z.string().min(1, 'Biological relationship is required'),
          label: z.string().min(1, 'Biological relationship label is required'),
        }),
        name: z.string().min(1),
        date_of_birth: z
          .string()
          .optional()
          .refine((value) => !value || /^\d{4}-\d{2}-\d{2}$/.test(value), {
            message: 'Invalid date format, must be DD-MM-YYYY',
          }),

        status: z.union([z.boolean(), z.string().optional()]).optional(),
        blood_type: z
          .object({
            value: z.string(),
            label: z.string(),
          })
          .optional(),

        ethnicity: z
          .object({
            value: z.string(),
            label: z.string(),
          })
          .optional(),
      })
    ),
    defaultValues: getInitialFormData(familyMember, OPTIONS_ETHNICITY, OPTIONS_BLOOD_TYPE),
  });
  const {
    trigger,
    handleSubmit,
    formState: { isSubmitting, isValid },
    reset,
    watch,
  } = form;

  const watchedValues = watch();
  const initialValues = getInitialFormData(familyMember, OPTIONS_ETHNICITY, OPTIONS_BLOOD_TYPE);

  // to check whether there is change in form fields to enable submit button
  const hasChanged = !_.isEqual(initialValues, watchedValues);

  const dateOfBirthField = form.watch('date_of_birth');
  const relationField = form.watch('relationship');
  const bloodTypeField = form.watch('blood_type');
  const status = form.watch('status');
  const statusField = status === '' ? '' : status === true ? 'deceased' : 'living';
  const ethnicityField = form.watch('ethnicity');
  const removeMemberModal = useDisclosure();
  const datePickerPopover = useDisclosure();

  async function onRemove() {
    try {
      setIsLoading(true);
      removeMemberModal.onOpen();
      recordFamilyMemberHistoryEvents(trackEventInFlow, {
        EventName: 'FamilyMemberHistoryRemoved',
      });
    } catch (err) {
      setError(err);
    } finally {
      setIsLoading(false);
    }
  }

  async function onSubmit(data: FamilyMemberHistory) {
    try {
      const payload: FamilyMemberHistoryPayload = {
        relationship: {
          label: data?.relationship?.label ?? '',
          value: data?.relationship?.value ?? '',
        },
        date_of_birth: data?.date_of_birth ?? '',
        name: data?.name,
        blood_type: {
          label: data?.blood_type?.label ?? '',
          value: data?.blood_type?.value ?? '',
        },
        status: data?.status ?? '',
        ethnicity: {
          label: data?.ethnicity?.label ?? '',
          value: data?.ethnicity?.value ?? '',
        },
      };

      if (isEditForm) {
        const updatedPayload = {
          resourceType: 'FamilyMemberHistory',
          id: familyMember?.id,
          ...payload,
        };
        await updateFamilyMember({ familyMemberId: familyMember!.id, payload: updatedPayload });
        recordFamilyMemberHistoryEvents(trackEventInFlow, {
          EventName: 'FamilyMemberAddCompleted',
          fm_relationship: data?.relationship?.label,
          fm_entry_point: 'family_history',
        });
        closeDialog(familyMember);
      } else {
        const details: any = await addFamilyMember(payload);

        // AnalyticsService.instance.identifyUser(authenticatedUser, {
        //   [PatientPropsNames.HasFamilyMembers]: true,
        // });
        recordFamilyMemberHistoryEvents(trackEventInFlow, {
          EventName: 'FamilyMemberAddCompleted',
          fm_relationship: data?.relationship?.label,
          fm_entry_point: 'family_history',
        });

        // Clear form if "add" mode
        if (!isEditForm) reset();

        closeDialog(details?.entry?.[0]?.resource);
      }
      toast({
        title: `Successfully ${isEditForm ? 'edit' : 'add'}ed the family member`,
        status: 'success',
        duration: 4000,
        isClosable: true,
      });
    } catch (err) {
      setError(err);
    }
  }

  // Function to handle the removal of the family member
  async function onRemoveFamilyMember() {
    try {
      setIsLoading(true);
      await deleteFamilyMember({ familyMemberId: familyMember!.id });

      toast({
        title: 'Successfully removed the family member',
        status: 'success',
        duration: 4000,
        isClosable: true,
      });

      removeMemberModal.onClose();
      closeDialog();
    } catch (err) {
      setError(err);
    } finally {
      setIsLoading(false);
    }
  }

  const datePickerChangeHandler = (date: Date | null) => {
    if (dayjs(date).isValid()) {
      form.setValue('date_of_birth', dayjs(date).format('YYYY-MM-DD'));
      if (
        (!Array.isArray(familyMember) && (!familyMember || Object.keys(familyMember).length === 0)) ||
        (Array.isArray(familyMember) && familyMember.length === 0)
      ) {
        recordFamilyMemberHistoryEvents(trackEventInFlow, {
          EventName: 'FamilyMemberHistoryInProgBirthDate',
        });
      }
    } else {
      form.setValue('date_of_birth', '');
    }
    datePickerPopover.onClose();
  };

  const datePickerClearHandler = () => {
    form.setValue('date_of_birth', '');
    datePickerPopover.onClose();
  };

  const customRelationSelect = useCallback((value: SelectOptionProps | any) => {
    form.setValue('relationship', value);
    form.trigger('relationship');

    if (
      (!Array.isArray(familyMember) && (!familyMember || Object.keys(familyMember).length === 0)) ||
      (Array.isArray(familyMember) && familyMember.length === 0)
    ) {
      recordFamilyMemberHistoryEvents(trackEventInFlow, {
        EventName: 'FamilyMemberHistoryInProgRelationship',
        fm_relationship: value?.label,
      });
    }
  }, []);

  useEffect(() => {
    const subscription = form.watch(() => {
      form.trigger();
    });
    return () => subscription.unsubscribe();
  }, [form]);

  return (
    <FormProvider {...form}>
      <ConsentModal {...removeMemberModal}>
        <ConsentModalHeading>Are you sure you want to remove this entry?</ConsentModalHeading>
        <ConsentModalContent>This user will be removed as a family member. This cannot be undone.</ConsentModalContent>
        <ConsentModalFooter>
          <ConsentModalSecondaryButton
            variant="quietDanger"
            color="red.100"
            isLoading={isLoading}
            onClick={onRemoveFamilyMember}
          >
            Remove
          </ConsentModalSecondaryButton>
          <ConsentModalPrimaryButton onClick={removeMemberModal.onClose}>Cancel</ConsentModalPrimaryButton>
        </ConsentModalFooter>
      </ConsentModal>
      <Grid
        templateColumns="repeat(2, 1fr)"
        gap={8}
      >
        <GridItem
          colSpan={2}
          gap={24}
          pt="8"
        >
          <FormControl variant="floating">
            <Input
              name="name"
              onChange={(e: any) => {
                form.setValue('name', e?.target?.value);
              }}
              value={form.getValues('name')}
              placeholder=""
            />
            <FormLabel>Full name*</FormLabel>
          </FormControl>
        </GridItem>

        <GridItem colSpan={2}>
          <FormControl isInvalid={!!form.formState.errors.relationship}>
            <Select
              value={OPTIONS_RELATION.find((item: any) => item.value === relationField?.value)}
              onChange={customRelationSelect}
              options={OPTIONS_RELATION}
              isSearchable={false}
              isClearable
              disabledOptions={relationships}
              labelText="Biological relationship*"
            />
          </FormControl>
        </GridItem>
        <GridItem colSpan={2}>
          <DatePickerField
            name="date_of_birth"
            labelText="Date of birth"
            rules={{ required: true }}
            datePickerChangeHandler={datePickerChangeHandler}
            datePickerClearHandler={datePickerClearHandler}
            datePickerPopover={datePickerPopover}
            isClearDateButtonDisabled={dateOfBirthField?.length === 0}
            selected={dayjs(dateOfBirthField).isValid() ? dayjs(dateOfBirthField).toDate() : null}
            maxDate={new Date()}
          />
        </GridItem>

        <GridItem
          colSpan={[2, 1]}
          gap={24}
        >
          <FormControl>
            <Select
              value={OPTIONS_STATUS.find((item: any) => item?.value === statusField)}
              onChange={(option: SelectOptionProps | any) => {
                if (option) {
                  form.setValue('status', option.value !== 'living');
                  recordFamilyMemberHistoryEvents(trackEventInFlow, {
                    EventName: 'FamilyMemberHistoryInProgStatus',
                  });
                } else {
                  // When the user clicks the "X" to clear the selection, set a default value (e.g., "Living" or any other desired value)
                  form.setValue('status', '');
                }
              }}
              options={OPTIONS_STATUS}
              isSearchable={false}
              isClearable
              labelText="Status"
            />
          </FormControl>
        </GridItem>

        <GridItem colSpan={[2, 1]}>
          <FormControl>
            <Select
              value={OPTIONS_BLOOD_TYPE.find((item: any) => item.value === bloodTypeField?.value)}
              onChange={(option: SelectOptionProps | any) => {
                if (option) {
                  // Set the value when an option is selected
                  form.setValue('blood_type', option);
                  if (
                    (!Array.isArray(familyMember) && (!familyMember || Object.keys(familyMember).length === 0)) ||
                    (Array.isArray(familyMember) && familyMember.length === 0)
                  ) {
                    recordFamilyMemberHistoryEvents(trackEventInFlow, {
                      EventName: 'FamilyMemberHistoryInProgBloodGroup',
                    });
                  }
                } else {
                  // When the user clicks the "X" to clear the selection, set `blood_type` with empty value and label
                  form.setValue('blood_type', {
                    value: '',
                    label: '',
                  });
                }
              }}
              options={OPTIONS_BLOOD_TYPE}
              isSearchable={false}
              isClearable
              labelText="Blood group"
            />
          </FormControl>
        </GridItem>
        <GridItem colSpan={2}>
          <FormControl>
            <Select
              value={OPTIONS_ETHNICITY.find((item: any) => item.value === ethnicityField?.value) || null}
              onChange={(option: SelectOptionProps | any) => {
                trigger('ethnicity');

                if (option) {
                  // Set the selected option when a valid option is chosen
                  form.setValue('ethnicity', option);
                  if (
                    (!Array.isArray(familyMember) && (!familyMember || Object.keys(familyMember).length === 0)) ||
                    (Array.isArray(familyMember) && familyMember.length === 0)
                  ) {
                    recordFamilyMemberHistoryEvents(trackEventInFlow, {
                      EventName: 'FamilyMemberHistoryInProgEthnicity',
                    });
                  }
                } else {
                  // Set the value to blank when the selection is cleared
                  form.setValue('ethnicity', {
                    value: '',
                    label: '',
                  });
                }
              }}
              options={OPTIONS_ETHNICITY}
              isSearchable={false}
              isClearable
              labelText="Ethnicity"
            />
          </FormControl>
        </GridItem>
      </Grid>

      <HStack
        flex="row"
        justifyContent="space-between"
        alignItems="flex-end"
        mt="10"
      >
        {isEditForm && (
          <Button
            variant="quiet"
            opacity="0.6"
            color="fluentHealthComplementary.Red"
            _hover={{
              opacity: '1',
            }}
            leftIcon={<TrashIcon size={16} />}
            onClick={onRemove}
          >
            <Text
              height="5"
              lineHeight="6"
            >
              Remove
            </Text>
          </Button>
        )}
        <Button
          px="20px"
          ml="auto"
          isLoading={isSubmitting || isLoading}
          isDisabled={!isValid || !hasChanged}
          onClick={handleSubmit(onSubmit)}
          type="submit"
          flexShrink={0}
        >
          {buttonText}
        </Button>
      </HStack>
    </FormProvider>
  );
}
