import { ReactNode, Suspense, useCallback, useEffect, useState } from 'react';
import {
  Box,
  Button,
  Checkbox,
  CheckboxGroup,
  Flex,
  FormControl,
  FormErrorMessage,
  FormHelperText,
  FormLabel,
  Grid,
  GridItem,
  Heading,
  Input,
  InputGroup,
  InputRightElement,
  Text,
  UseDisclosureReturn,
  useDisclosure,
  useTheme,
  useToast,
} from '@chakra-ui/react';
import { FormProvider, useForm } from 'react-hook-form';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import { Clock as ClockIcon } from 'react-feather';
import { zodResolver } from '@hookform/resolvers/zod';
import { DropdownIndicatorProps } from 'react-select';
import { z } from 'zod';
import { google, ics } from 'calendar-link';
import { recordReminderEvents } from '@user/lib/events-analytics-manager';

import { IModal, Modal } from 'src/components/Modal';
import { DatePickerField, FormSkeleton, SuggestionDropdown } from 'src/components/ui/Form';
import {
  ALERT_FREQUENCY,
  // ALERT_TYPES,
  Alert,
  AlertPayload,
  CUSTOM_ALERT_FREQUENCY_UNITS,
  Reminder,
} from '@lib/models/alert';
import { CustomDropdownIndicatorRenderer, Select, SelectOptionProps } from '../../../components/ui/Select';
import {
  EDIT_ALERT_FREQUENCY_VALUESET_URLS,
  MEDICAL_RECORD_ICON_BG_MAP,
  MEDICAL_RECORD_ICON_MAP,
  MEDICAL_RECORD_NAME_MAP,
} from '../lib/constants';
import { hexOpacity } from 'src/components/theme/utils';
import { HOURLY_BASE_OPTIONS } from '@lib/constants';
import { useAlertList, useAnalyticsService, useAuthService } from '@lib/state';
import { AutoExpandedTextarea } from '../../../components/ui/Form/AutoExpandedTextarea';
import { parsePatientName } from '@lib/utils/utils';
// import { EventPropsNames } from '@lib/analyticsService';
import { formatListToSelectOptions } from '../lib/utils';
import { medplumApi } from '../lib/medplum-api';
import { useDocRefDetails } from '../lib/state';
import { ValueSet } from 'src/gql/graphql';
// import { MEDICAL_RECORD_BEHOLDER_TYPES } from '@lib/models/medical-record';
import {
  FHIR_HL7_CODE_SYSTEM_COMMUNICATION_CATEGORY,
  FHIR_HL7_CODE_SYSTEM_COMMUNICATION_OCCURRENCE_TIMING,
  FHIR_HL7_CODE_SYSTEM_COMMUNICATION_REMINDER_ALL_DAY,
} from 'src/constants/medplumConstants';

import { ReactComponent as TimeRepeatIcon } from '@assets/icons/time-repeat.svg';
import { ReactComponent as ReminderSuccess } from '@assets/objects/success-like.svg';

type FormValues = Pick<Alert, 'title' | 'alert_message' | 'alert_time' | 'all_day'> & {
  date: string;
  every?: string;
  customFrequency?: string;
  frequency: Alert['frequency'] | typeof CUSTOM_ALERT_FREQUENCY;
};

const CUSTOM_ALERT_FREQUENCY = 'rf-custom';
const DEFAULT_ALERT_TIME = '12:00';
const MAX_ALLOWED_CHARACTERS = 200;
const TIME_REGEX = /^(?:2[0-3]|[01][0-9]):[0-5][0-9]$/;

const ONCE_IN_FREQUENCY_LABEL_MAP = {
  [CUSTOM_ALERT_FREQUENCY_UNITS.HOURS]: 'HOURLY',
  [CUSTOM_ALERT_FREQUENCY_UNITS.DAYS]: 'DAILY',
  [CUSTOM_ALERT_FREQUENCY_UNITS.WEEKS]: 'WEEKLY',
  [CUSTOM_ALERT_FREQUENCY_UNITS.MONTHS]: 'MONTHLY',
  [CUSTOM_ALERT_FREQUENCY_UNITS.YEARS]: 'YEARLY',
};

const CALENDAR_FREQUENCY_LABEL_MAP = {
  [ALERT_FREQUENCY.ONCE]: 'FREQ=ONCE',
  [ALERT_FREQUENCY.DAILY]: 'FREQ=DAILY',
  [ALERT_FREQUENCY.WEEKLY]: 'FREQ=WEEKLY',
  [ALERT_FREQUENCY.MONTHLY]: 'FREQ=MONTHLY',
  [ALERT_FREQUENCY.YEARLY]: 'FREQ=YEARLY',
};

const getDropdownIndicator = (icon: ReactNode) =>
  function CustomDropdownIndicator(props: DropdownIndicatorProps) {
    return <CustomDropdownIndicatorRenderer {...props}>{icon}</CustomDropdownIndicatorRenderer>;
  };

// eslint-disable-next-line complexity
function ReminderForm({
  medRecordProps,
  setModalState,
  onSubmit,
  alert,
}: {
  medRecordProps?: NonNullable<ReturnType<typeof useDocRefDetails>>;
  alert?: NonNullable<ReturnType<typeof useDocRefDetails>['alert']>;
  setModalState: (modalState: IModal) => void;
  onSubmit?: (formValues: any) => void;
}) {
  const [timeOfRecordingOptions, setTimeOfRecordingOptions] = useState(HOURLY_BASE_OPTIONS);

  const [valueSetOptions, setValueSetOptions] = useState<Record<string, ValueSet[]>>({});

  const { authenticatedUser }: any = useAuthService();

  const { addAlert, updateAlert, linkCommRequestWithDocRef } = useAlertList(authenticatedUser?.id);
  const { trackEventInFlow } = useAnalyticsService();
  const [isLoading, setIsLoading] = useState(false);

  const toast = useToast();
  const datePickerPopover = useDisclosure();
  dayjs.extend(utc);
  const { id: docRefId, title, medicalRecordType, docRefCreationDate } = medRecordProps || {};
  const { code: recordCode, display } = medicalRecordType ?? {};
  const recordName = MEDICAL_RECORD_NAME_MAP[recordCode] || display;

  const isCommRequest = alert?.__typename === 'CommunicationRequest';
  const alertFrequency =
    isCommRequest &&
    alert?.extension?.find((item: any) => item?.url === FHIR_HL7_CODE_SYSTEM_COMMUNICATION_OCCURRENCE_TIMING);

  const isCustomAlert = alert?.extension?.find((item: any) => item?.url === 'isCustomFequency')?.valueBoolean;

  const isAllDay =
    isCommRequest &&
    alert?.extension?.find((item: any) => item?.url === FHIR_HL7_CODE_SYSTEM_COMMUNICATION_REMINDER_ALL_DAY);

  const periodUnitMap: Record<string, string> = {
    h: 'rf-hours',
    d: 'rf-everyday',
    wk: 'rf-everyweek',
    mo: 'rf-everymonth',
    a: 'rf-everyyear',
  };

  const repeat = alert?.extension?.find((ext: any) => ext.url === FHIR_HL7_CODE_SYSTEM_COMMUNICATION_OCCURRENCE_TIMING)
    ?.valueTiming?.repeat;

  const selectedFrequencyValue = (!isCustomAlert && periodUnitMap[repeat?.periodUnit]) || CUSTOM_ALERT_FREQUENCY;

  const selectedFrequencyObj = formatListToSelectOptions(valueSetOptions?.frequencyList)?.find(
    (option) => option.value === selectedFrequencyValue
  );

  const defaultSelectedCustomFreqObj = formatListToSelectOptions(valueSetOptions?.customFrequencyList)?.find(
    (option) => option.value === repeat?.periodUnit
  );

  const defalutAlert: any = alert;
  const form: any = useForm({
    mode: 'all',
    defaultValues: {
      title: defalutAlert?.payload?.[0]?.contentString || '',
      date:
        isCommRequest && alert.occurrenceDateTime
          ? dayjs(alert.occurrenceDateTime).utc().format('YYYY-MM-DD')
          : DEFAULT_ALERT_TIME,
      alert_time: isCommRequest && alert.occurrenceDateTime ? dayjs(alert.occurrenceDateTime).format('HH:mm') : '',
      frequency: (isCommRequest && alertFrequency && alertFrequency.valueCode) || isCustomAlert ? 'rf-custom' : '',
      all_day: (isCommRequest && isAllDay && isAllDay.valueBoolean) || false,
      every: isCustomAlert ? alertFrequency?.valueTiming?.repeat?.frequency || '1' : undefined,
      customFrequency: defaultSelectedCustomFreqObj?.value ?? undefined,
      alert_message: (isCommRequest && alert.note?.[0]?.text) || '',
    },
    resolver: zodResolver(
      z.object({
        title: z.string().min(1),
        alert_time: z.string().regex(TIME_REGEX).min(1),
        date: z.string().min(1),
        frequency: z.string().min(1),
        all_day: z.boolean().optional(),
        alert_message: z.string().trim().nullable().optional(),
      })
    ),
  });
  const titleField = form.watch('title');
  const dateField = form.watch('date');
  const allDayField = form.watch('all_day');
  const timeField = form.watch('alert_time');

  const frequencyField = form.watch('frequency');
  const everyField = form.watch('every');
  const customFrequencyField = form.watch('customFrequency');
  const noteField = form.watch('alert_message');

  const {
    handleSubmit,
    register,
    formState: { isValid },
    getValues,
  } = form;

  function getTimingRepeat({
    every,
    customFrequency,
    frequency,
  }: {
    every?: string;
    customFrequency?: string;
    frequency?: string;
    defaultTiming: { frequency: number; period: number; periodUnit: string };
  }) {
    if (every && customFrequency) {
      return { frequency: 1, period: parseInt(every, 10), periodUnit: customFrequency };
    }

    const freqMap: Record<string, { frequency: number; period: number; periodUnit: string }> = {
      'rf-everyday': { frequency: 1, period: 1, periodUnit: 'd' },
      'rf-everyweek': { frequency: 1, period: 1, periodUnit: 'wk' },
      'rf-everymonth': { frequency: 1, period: 1, periodUnit: 'mo' },
      'rf-everyyear': { frequency: 1, period: 1, periodUnit: 'a' },
    };

    return freqMap[frequency ?? ''];
  }

  const formSubmitHandler = async () => {
    const {
      alert_message,
      alert_time,
      all_day,
      customFrequency,
      date,
      every,
      frequency,
      title: attachmentTitle,
    } = getValues();

    setIsLoading(true);
    const defaultTiming = { frequency: 1, period: 1, periodUnit: 'h' };

    const timingRepeat = getTimingRepeat({
      every,
      customFrequency,
      frequency,
      defaultTiming,
    });

    const extensions = [
      ...(timingRepeat
        ? [
            {
              url: FHIR_HL7_CODE_SYSTEM_COMMUNICATION_OCCURRENCE_TIMING,
              valueTiming: { repeat: timingRepeat },
            },
          ]
        : []),
      {
        url: FHIR_HL7_CODE_SYSTEM_COMMUNICATION_REMINDER_ALL_DAY,
        valueBoolean: all_day || false,
      },
      ...(customFrequency
        ? [
            {
              url: 'scheduleType',
              valueCodeableConcept: {
                coding: [
                  {
                    system: 'https://fluentinhealth.com/FHIR/CodeSystem/FluentHealthUI',
                    code: 'rf-custom',
                    display: 'Custom',
                  },
                ],
                text: 'Custom',
              },
            },
          ]
        : []),
    ];

    const formattedDateTime =
      date && alert_time
        ? dayjs(`${dayjs(date).format('YYYY-MM-DD')}T${alert_time}`)
            .utcOffset(-5 * 60)
            .utc()
            .format('YYYY-MM-DDTHH:mm:ss.SSS[Z]')
        : null;

    const payload: AlertPayload = {
      resourceType: 'CommunicationRequest',
      category: [
        {
          coding: [
            {
              system: FHIR_HL7_CODE_SYSTEM_COMMUNICATION_CATEGORY,
              code: 'reminder',
              display: 'Reminder',
            },
          ],
        },
      ],
      subject: { reference: `Patient/${authenticatedUser?.id}`, display: parsePatientName(authenticatedUser?.name) },
      payload: [{ contentString: attachmentTitle }],
      occurrenceDateTime: formattedDateTime,
      ...(alert_message && { note: [{ text: alert_message }] }),
      status: 'draft',
      extension: extensions,
    };
    try {
      if (alert) {
        payload.id = alert.id;
        await updateAlert({
          alertId: (alert?.__typename === 'CommunicationRequest' && alert?.id) || undefined,
          payload,
        });
        recordReminderEvents(trackEventInFlow, {
          EventName: 'ReminderEditCompleted',
          // 'rm_entry_point': ,
          rm_name: titleField,
          rm_date: dateField,
          rm_all_day: allDayField,
          rm_time: timeField,
          rm_frequency: frequencyField,
          rm_notes: noteField,
        });
      } else {
        const createdAlert = await addAlert(payload);

        const commReqId = createdAlert?.id;

        if (commReqId && docRefId) {
          await linkCommRequestWithDocRef({
            commReqId,
            docRefId,
          });
        }
      }
      recordReminderEvents(trackEventInFlow, {
        EventName: 'ReminderAddCompleted',
        // 'rm_entry_point': ,
        rm_name: titleField,
        rm_date: dateField,
        rm_all_day: allDayField,
        rm_time: timeField,
        rm_frequency: frequencyField,
        rm_notes: noteField,
      });

      onSubmit?.(form.getValues());

      toast({
        title: alert ? 'Your reminder has been successfully updated.' : 'Reminder successfully created!',
        status: 'success',
        duration: 1500,
        isClosable: true,
      });
    } catch (err) {
      toast({
        title: (err as any).message,
        status: 'error',
        duration: 4000,
        isClosable: true,
      });
    } finally {
      form.reset();
      setIsLoading(false);
    }
  };

  useEffect(() => {
    setModalState((prevState: IModal) => ({
      ...prevState,
      onPrimaryButtonClick: handleSubmit(formSubmitHandler),
      primaryButtonEnabled: isValid,
      isPrimaryButtonLoading: isLoading,
    }));
  }, [isValid, isLoading, handleSubmit]);

  const datePickerChangeHandler = (date: Date | null) => {
    if (dayjs(date).isValid()) {
      form.setValue('date', dayjs(date).format('YYYY-MM-DD'));
    } else {
      form.setValue('date', '');
    }
    datePickerPopover.onClose();
    recordReminderEvents(trackEventInFlow, {
      EventName: 'ReminderAddInProgDate',
      rm_date: dateField ?? '',
    });
  };

  const datePickerClearHandler = () => {
    form.setValue('date', '');
    datePickerPopover.onClose();
  };

  const handleAllDayReminderChange = () => {
    if (allDayField) form.setValue('alert_time', '');
    if (!allDayField) form.setValue('alert_time', DEFAULT_ALERT_TIME);
    form.setValue('all_day', !allDayField);
    form.trigger('all_day');
    recordReminderEvents(trackEventInFlow, {
      EventName: 'ReminderAddInProgAllDay',
      rm_all_day: allDayField ?? '',
    });
  };

  const onTimeOfRecordingSearchChange = useCallback(
    async (searchText: string) => {
      const newList = HOURLY_BASE_OPTIONS.filter((option) => option.label.includes(searchText));
      setTimeOfRecordingOptions(newList);
      return newList;
    },
    [form]
  );

  const onFrequencySelect = useCallback((value: SelectOptionProps | any) => {
    form.setValue('frequency', value.value);
    if (value.value === CUSTOM_ALERT_FREQUENCY) return;
    form.resetField('every');
    form.resetField('customFrequency');
    form.trigger('frequency');
    recordReminderEvents(trackEventInFlow, {
      EventName: 'ReminderAddInProgFrequency',
      rm_frequency: frequencyField ?? '',
    });
  }, []);

  const onCustomFrequencySelect = useCallback((value: SelectOptionProps | any) => {
    form.setValue('customFrequency', value.value);
    form.trigger('customFrequency');
    recordReminderEvents(trackEventInFlow, {
      EventName: 'ReminderAddInProgFrequency',
      rm_frequency: frequencyField ?? '',
    });
  }, []);

  const onTimeOfRecordingSelect = useCallback((value: SelectOptionProps | any) => {
    form.setValue('alert_time', value.value);
    form.trigger('alert_time');
    recordReminderEvents(trackEventInFlow, {
      EventName: 'ReminderAddInProgTime',
      rm_time: timeField ?? '',
    });
  }, []);

  const selectedCustomFrequency = formatListToSelectOptions(valueSetOptions?.customFrequencyList)?.find(
    (frequencyObj) => frequencyObj?.value === customFrequencyField
  );

  function getCustomFrequencyDescription() {
    const frequencyLabel = selectedCustomFrequency?.label?.toLowerCase() ?? '';
    let description = '';

    if (everyField && customFrequencyField) {
      if (everyField === '1') {
        description = `Reminder will occur every ${everyField} ${frequencyLabel?.slice(
          0,
          (frequencyLabel?.length ?? 0) - 1
        )}.`;
      } else {
        description = `Reminder will occur every ${everyField} ${frequencyLabel}.`;
      }
    }

    return description;
  }

  useEffect(() => {
    const fetchData = async () => {
      try {
        const result = (await medplumApi?.valueSets?.getAll({ urls: EDIT_ALERT_FREQUENCY_VALUESET_URLS })) || {};
        setValueSetOptions(result);
      } catch (error) {
        console.error('error ::', error);
      }
    };

    fetchData();
  }, []);

  return (
    <FormProvider {...form}>
      {medicalRecordType && (
        <Flex
          align="flex-start"
          justify="space-between"
          p="8px"
          gap="12px"
          borderRadius="16px"
          borderWidth="1px"
          borderStyle="solid"
          borderColor="fluentHealthText.500"
          boxShadow="0px 0px 12px -2px rgba(73, 90, 228, 0.10)"
          background="rgba(255, 255, 255, 0.50)"
        >
          <Flex
            align="center"
            gap="16px"
          >
            <Flex
              align="center"
              justify="center"
              width={{ base: '64px', md: '60px' }}
              height={{ base: '64px', md: '60px' }}
              borderRadius="12px"
              bgColor={MEDICAL_RECORD_ICON_BG_MAP[recordCode || '']} // * The rationale is to use the codes defined by us for valueSets for mapping
            >
              <Box
                maxW="35px"
                height="43px"
              >
                {MEDICAL_RECORD_ICON_MAP[recordCode || '']}
              </Box>
            </Flex>
            <Flex flexDirection="column">
              {medicalRecordType && <Text fontSize={{ base: 'lg', md: 'xl' }}>{title || '-'}</Text>}
              <Text
                color="fluentHealthText.300"
                fontSize={{ base: 'sm', md: 'md' }}
              >
                {recordName} from {docRefCreationDate ? dayjs(docRefCreationDate).format('DD MMM. YYYY') : ''}
              </Text>
            </Flex>
          </Flex>
        </Flex>
      )}
      <FormControl
        variant="floating"
        mt={medicalRecordType ? '40px' : '15px'}
        gap="4px"
        isInvalid={!!form.formState.errors.title}
      >
        <Input
          placeholder=" "
          {...register('title')}
          maxLength={50}
          onBlur={() => {
            recordReminderEvents(trackEventInFlow, {
              EventName: 'ReminderAddInProgName',
              rm_name: titleField ?? '',
            });
          }}
        />
        <FormLabel>Remind me to... *</FormLabel>
        <FormErrorMessage>This field is required</FormErrorMessage>
        <FormHelperText
          color="gray.300"
          lineHeight="20px"
          mt="4px"
          display={{ base: 'none', md: 'block' }}
        >
          ...Go for my appointment, take my medicine, schedule my physical, screenings or procedures.
        </FormHelperText>
      </FormControl>
      <FormControl
        mt="45px"
        isInvalid={!!form.formState.errors.date}
      >
        <DatePickerField
          name="date"
          errorText="This field is required"
          rules={{ required: true }}
          datePickerChangeHandler={datePickerChangeHandler}
          datePickerClearHandler={datePickerClearHandler}
          datePickerPopover={datePickerPopover}
          selected={dayjs(dateField).isValid() ? dayjs(dateField).toDate() : null}
          minDate={new Date()}
          labelText="Reminder date*"
          showClearDateButton={false}
          popoverProps={{
            placement: 'bottom-start',
            strategy: 'fixed',
          }}
        />
        <FormErrorMessage>{form.formState.touchedFields.date && 'This field is required'}</FormErrorMessage>
      </FormControl>
      <FormControl
        marginTop={{ base: '16px', md: '24px' }}
        marginBottom={{ base: '16px', md: '0' }}
      >
        <CheckboxGroup defaultValue={[allDayField ? 'allDay' : '']}>
          <Checkbox
            colorScheme="fluentHealth"
            value="allDay"
            isChecked={allDayField}
            onChange={handleAllDayReminderChange}
          >
            All day
          </Checkbox>
        </CheckboxGroup>
      </FormControl>
      <Grid
        templateColumns={{ base: '1fr', md: 'repeat(auto-fit, minmax(100px, 1fr))' }}
        gap={{ md: '24px' }}
        marginTop={{ md: '50px' }}
      >
        {!allDayField && (
          <GridItem py={{ base: '24px', md: '0' }}>
            <FormControl
              variant="floating"
              isDisabled={false}
              isInvalid={!!form.formState.errors.alert_time}
            >
              <SuggestionDropdown
                options={timeOfRecordingOptions}
                textValue={timeField}
                onSelect={onTimeOfRecordingSelect}
                onChange={onTimeOfRecordingSearchChange}
                onClear={() => {}}
                keepOpenAfterBlur={false}
                resetTextValueAfterBlur={false}
                debounceDelay={100}
                isFreeInput
                visibleScrollbar
              >
                {({ searchInputChangeHandler, searchInputBlueHandler, suggestionDropdownPopover }) => (
                  <InputGroup display="block">
                    <Input
                      placeholder=" "
                      {...register('alert_time', {
                        onChange: searchInputChangeHandler,
                        onBlur: searchInputBlueHandler,
                      })}
                    />
                    <FormLabel>Time *</FormLabel>
                    <FormErrorMessage>Format should be hh:mm</FormErrorMessage>
                    <InputRightElement
                      w="36px"
                      h="36px"
                      cursor="pointer"
                      color="papaya.600"
                      onClick={() => {
                        setTimeOfRecordingOptions(HOURLY_BASE_OPTIONS);
                        suggestionDropdownPopover.onOpen();
                      }}
                    >
                      <ClockIcon size={18} />
                    </InputRightElement>
                  </InputGroup>
                )}
              </SuggestionDropdown>
            </FormControl>
          </GridItem>
        )}
        <GridItem py={{ base: '24px', md: '0' }}>
          <Select
            key={selectedFrequencyObj?.value}
            menuPosition="fixed"
            labelText="Frequency *"
            options={formatListToSelectOptions(valueSetOptions?.frequencyList)}
            onChange={onFrequencySelect}
            DropdownIndicator={getDropdownIndicator(<TimeRepeatIcon />)}
            maxMenuHeight={200}
            isClearable={false}
            isSearchable={false}
            defaultValue={selectedFrequencyObj}
            {...(alertFrequency &&
              alertFrequency.valueCode && {
                defaultValue: {
                  label: selectedFrequencyObj?.label,
                  value: selectedFrequencyObj?.value,
                },
              })}
          />
        </GridItem>
      </Grid>
      {frequencyField === CUSTOM_ALERT_FREQUENCY && (
        <Grid
          templateColumns={{ base: '1fr', md: 'repeat(auto-fit, minmax(100px, 1fr))' }}
          gap={{ md: '24px' }}
          width="100%"
          marginTop={{ md: '48px' }}
        >
          <GridItem
            colSpan={1}
            py={{ base: '24px', md: '0' }}
          >
            <FormControl
              variant="floating"
              isInvalid={!!form.formState.errors.every}
            >
              <InputGroup>
                <Input
                  placeholder=" "
                  type="number"
                  {...register('every')}
                  min="1"
                />
                <FormLabel>Every*</FormLabel>
              </InputGroup>
              <FormHelperText
                color="fluentHealthText.300"
                lineHeight="20px"
                height="20px"
                display={{ base: 'none', md: 'block' }}
              >
                {getCustomFrequencyDescription()}
              </FormHelperText>
            </FormControl>
          </GridItem>
          <GridItem
            colSpan={1}
            py={{ base: '24px', md: '0' }}
          >
            <Select
              key={defaultSelectedCustomFreqObj?.value}
              menuPosition="fixed"
              labelText="Custom frequency*"
              options={formatListToSelectOptions(valueSetOptions?.customFrequencyList) ?? []}
              onChange={onCustomFrequencySelect}
              isSearchable={false}
              maxMenuHeight={200}
              defaultValue={{
                label: defaultSelectedCustomFreqObj?.label,
                value: defaultSelectedCustomFreqObj?.value,
              }}
            />
          </GridItem>
        </Grid>
      )}
      <FormControl
        variant="floating"
        marginTop={{ base: '24px', md: '32px' }}
        marginBottom="10px"
        isInvalid={!!form.formState.errors.alert_message}
      >
        <AutoExpandedTextarea
          placeholder=" "
          controllerProps={{
            name: 'alert_message',
            control: form.control,
            rules: {
              maxLength: MAX_ALLOWED_CHARACTERS,
              required: false,
              onChange: () => {
                form.trigger();
              },
              onBlur: () => {
                recordReminderEvents(trackEventInFlow, {
                  EventName: 'ReminderAddInProgNotes',
                  rm_notes: noteField ?? '',
                });
              },
            },
          }}
          maxH="80px"
          borderColor="fluentHealthText.300"
        />
        <FormLabel>Notes</FormLabel>
        <FormErrorMessage>
          {noteField && noteField.length > MAX_ALLOWED_CHARACTERS
            ? `The maximum number of characters allowed is ${MAX_ALLOWED_CHARACTERS}`
            : ''}
        </FormErrorMessage>
      </FormControl>
    </FormProvider>
  );
}

export function ReminderFlow({
  medRecordProps,
  selectedAlert,
  reminderRecordModal,
  calendarModal,
  onSuccess,
  onFormClose,
  onCalendarClose,
}: {
  medRecordProps?: NonNullable<ReturnType<typeof useDocRefDetails>>;
  selectedAlert?: Reminder;
  reminderRecordModal: UseDisclosureReturn;
  calendarModal: UseDisclosureReturn;
  onSuccess?: Function;
  onFormClose?: Function;
  onCalendarClose?: Function;
}) {
  const [modalState, setModalState] = useState<IModal>({});
  const [formData, setFormData] = useState<FormValues>({
    title: '',
    date: '',
    alert_time: DEFAULT_ALERT_TIME,
    frequency: ALERT_FREQUENCY.ONCE,
    all_day: true,
    alert_message: '',
  });
  const [shouldShowIcsWarning, setShouldShowIcsWarning] = useState(false);

  const theme = useTheme();

  const { alert: medAlert } = medRecordProps || {};
  const alert = selectedAlert?.id ? selectedAlert : medAlert;
  const alertId = (alert?.__typename === 'CommunicationRequest' && alert?.id) || undefined;
  const modalTitle = alertId ? 'Edit reminder' : 'Set a reminder';
  const primaryButtonLabel = alertId ? 'Save changes' : 'Set reminder';

  async function onSubmit(formsValues: FormValues) {
    setFormData(formsValues);
    reminderRecordModal.onClose();
    calendarModal.onOpen();
    onSuccess?.();
  }

  const getEventDetails = (rRuleFrequency: string) => ({
    title: formData.title,
    description: formData.alert_message ? formData.alert_message : `Reminder for: ${formData.title}`,
    start: `${formData.date} ${formData.alert_time}:00`,
    end: `${formData.date} ${formData.alert_time}:00`,
    allDay: formData.all_day,
    rRule: rRuleFrequency,
  });

  const recurrationFrequencyRule =
    formData.frequency === CUSTOM_ALERT_FREQUENCY
      ? `FREQ=${
          ONCE_IN_FREQUENCY_LABEL_MAP[formData.customFrequency as keyof typeof ONCE_IN_FREQUENCY_LABEL_MAP]
        };INTERVAL=${formData.every}`
      : CALENDAR_FREQUENCY_LABEL_MAP?.[formData.frequency ?? ''] ?? '';

  const handleGenerateGoogleLink = () => {
    const event = getEventDetails(recurrationFrequencyRule);

    const googleLink = google(event);
    window.open(googleLink, '_blank');
  };

  const handleGenerateAppleLink = () => {
    setShouldShowIcsWarning(true);
    const event = getEventDetails(recurrationFrequencyRule);

    const appleLink = ics(event);
    window.open(appleLink, '_blank');
  };

  useEffect(() => {
    if (!calendarModal.isOpen) {
      setShouldShowIcsWarning(false);
    }
  }, [calendarModal.isOpen]);

  return (
    <>
      <Modal
        title={modalTitle}
        showSecondaryButton={false}
        isCentered
        primaryButtonLabel={primaryButtonLabel}
        onPrimaryButtonClick={modalState.onPrimaryButtonClick}
        primaryButtonEnabled={modalState.primaryButtonEnabled}
        isPrimaryButtonLoading={modalState.isPrimaryButtonLoading}
        modalContentProps={{
          background: 'gradient.gradient3',
        }}
        minWidth="509px"
        scrollBehavior="inside"
        footerAlert={
          <Box
            padding={{ md: '8px 16px' }}
            borderRadius="8px"
            bg={{ base: 'white', md: 'periwinkle.50' }}
            mr="32px"
            mb="12px"
          >
            <Text
              fontSize={{ base: 'sm', md: 'md' }}
              color={{ base: 'fluentHealthText.400', md: 'fluentHealthSecondary.100' }}
            >
              Reminders are automatically synced with your Fluent mobile app.
            </Text>
          </Box>
        }
        {...reminderRecordModal}
        onClose={() => {
          reminderRecordModal.onClose();
          onFormClose?.();
        }}
      >
        <Suspense fallback={<FormSkeleton />}>
          <ReminderForm
            setModalState={setModalState}
            onSubmit={onSubmit}
            medRecordProps={medRecordProps}
            {...(alert && { alert })}
          />
        </Suspense>
      </Modal>
      <Modal
        isCentered
        showModalFooter={false}
        minWidth="509px"
        showModalHeading={false}
        modalContentProps={{
          bg: 'linear-gradient(180deg, #FFF2DF 0%, #DADCFF 100%)',
        }}
        {...calendarModal}
        onClose={() => {
          calendarModal.onClose();
          onCalendarClose?.();
        }}
      >
        <Box
          position="absolute"
          pointerEvents="none"
          bottom="0px"
          left="0px"
        >
          <svg
            width="509"
            height="360"
            viewBox="0 0 509 360"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <circle
              opacity="0.2"
              cx="188"
              cy="333"
              r="332.5"
              transform="rotate(-90 188 333)"
              stroke="#4956E4"
            />
          </svg>
        </Box>
        <Box
          position="absolute"
          pointerEvents="none"
          top="0px"
          right="0px"
        >
          <svg
            width="456"
            height="261"
            viewBox="0 0 456 261"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <circle
              opacity="0.2"
              cx="296"
              cy="-35"
              r="295.5"
              transform="rotate(-90 296 -35)"
              stroke="#4956E4"
            />
          </svg>
        </Box>
        <Box
          display="flex"
          justifyContent="center"
          alignItems="center"
          flexDirection="column"
          borderRadius="8px"
        >
          <Flex
            marginTop="56px"
            justifyContent="center"
            flexDirection="column"
            alignItems="center"
            gap="20px"
          >
            <ReminderSuccess
              style={{
                zIndex: 10,
              }}
            />
            <Heading
              fontSize="28px"
              margin="auto"
              maxWidth="250px"
              textAlign="center"
            >
              You&apos;ve successfully {alertId ? 'updated' : 'set'} a reminder!
            </Heading>
          </Flex>
          <Text
            fontSize="lg"
            color="fluentHealthText.200"
            marginTop="47px"
          >
            Add this reminder to your
          </Text>
          <Flex
            display="flex"
            justifyContent="center"
            alignItems="stretch"
            marginTop="5px"
            gap="8px"
            flexDirection="column"
            marginBottom="16px"
            width="190px"
          >
            <Button
              variant="outlined"
              border="1px solid"
              borderColor={hexOpacity(theme.colors.gray['600'], 0.24)}
              onClick={handleGenerateGoogleLink}
            >
              Google Calendar
            </Button>
            <Button
              variant="outlined"
              border="1px solid"
              borderColor={hexOpacity(theme.colors.gray['600'], 0.24)}
              onClick={handleGenerateAppleLink}
            >
              Apple Calendar
            </Button>
          </Flex>
          {shouldShowIcsWarning && (
            <Flex
              bg="periwinkle.50"
              borderRadius="8px"
              padding="8px 16px"
              maxWidth="300px"
            >
              <Text color="periwinkle.700">
                Please open the downloaded .ics file to add the event to your Apple calendar.
              </Text>
            </Flex>
          )}
        </Box>
      </Modal>
    </>
  );
}
