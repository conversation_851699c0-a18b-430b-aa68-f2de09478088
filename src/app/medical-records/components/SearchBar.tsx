// Package modules
import React, { ChangeEvent, KeyboardEvent, useCallback, useEffect, useRef, useState } from 'react';
import {
  Box,
  Flex,
  IconButton,
  Input,
  InputGroup,
  InputLeftElement,
  InputRightElement,
  Popover,
  PopoverContent,
  PopoverTrigger,
  Spinner,
  Text,
  useDisclosure,
  useOutsideClick,
  useTheme,
} from '@chakra-ui/react';
import { ArrowUpLeft as ArrowUpLeftIcon, X as CloseIcon, Search as SearchIcon } from 'react-feather';
import { useSearchParams } from 'react-router-dom';
import { useDebounce } from 'usehooks-ts';
// Local modules
import { recordHealthRecordHubEvents } from '@user/lib/events-analytics-manager';

import { useAnalyticsService, useAuthService, usePublicRecordSettings } from '@lib/state';
import { evalConditions } from '@lib/utils/utils';
import { MEDICAL_RECORD_FILTERS } from '@lib/models/medical-record';
import { useIsMobile } from 'src/components/ui/hooks/device.hook';
import { useMedicalRecordSuggestionListLazyPagination } from '../lib/state';

// Helpers
const getKeywordSearchParamValue = (searchParams: URLSearchParams) => {
  const queryValue = searchParams.get(MEDICAL_RECORD_FILTERS.KEYWORD);
  return queryValue && queryValue.length > 0 ? queryValue : '';
};

export function SearchBar({ fetchMedicalRecord }: { fetchMedicalRecord: (docId: string[]) => void }) {
  // const { pathname } = useLocation();
  const hasFetchedRecords = useRef(false);
  const [searchParams, setSearchParams] = useSearchParams();
  const [searchText, setSearchText] = useState<string>(getKeywordSearchParamValue(searchParams));
  const debouncedSearchText = useDebounce(searchText, 500);
  const [isExpanded, setIsExpanded] = useState(searchText?.length > 0);

  const theme = useTheme();
  const resultsPopover = useDisclosure();
  const { authenticatedUser } = useAuthService();

  const patientId = authenticatedUser?.id;

  const { myDocumentReferenceList } = usePublicRecordSettings();
  // TODO: Will need to change this in Share Eastic search task
  const {
    suggestionList,
    loadingElementRef,
    hasNextPage,
    isFetching: isFetchingSuggestions,
  } = useMedicalRecordSuggestionListLazyPagination(
    patientId,
    debouncedSearchText,
    myDocumentReferenceList,
    debouncedSearchText,
    searchParams
  );

  const searchInputRef = useRef<HTMLInputElement | null>(null);
  const searchInputContainerRef = useRef<HTMLDivElement | null>(null);
  const isMobile = useIsMobile();
  useOutsideClick({
    ref: searchInputContainerRef,
    handler: () => {
      if (searchText.length > 0 || isMobile) {
        return false;
      }

      resultsPopover.onClose();
      if (!isMobile) {
        setIsExpanded(false);
      }
      return true;
    },
  });

  const updateSearchParams = (searchQuery: string) => {
    if (searchQuery.length > 0) {
      searchParams.set(MEDICAL_RECORD_FILTERS.KEYWORD, searchQuery);
    } else {
      searchParams.delete(MEDICAL_RECORD_FILTERS.KEYWORD);
    }
    setSearchParams(searchParams);
  };

  const handleExpand = () => {
    if (!isMobile) {
      setIsExpanded((prev) => !prev);
    }
    searchInputRef.current?.focus();
  };

  const searchTextChangeHandler = async (value: string) => {
    setSearchText(value);
    resultsPopover.onOpen();
    return true;
  };

  const searchInputChangeHandler = useCallback((event: ChangeEvent<HTMLInputElement>) => {
    const { value } = event.target;
    searchTextChangeHandler(value);
  }, []);
  const { trackEventInFlow } = useAnalyticsService();
  const searchPrefillHandler = (value: string) => {
    setSearchText(value);
    updateSearchParams(value);
    if (suggestionList?.length) {
      fetchMedicalRecord(suggestionList?.map((suggestion) => suggestion.id));
    }
    recordHealthRecordHubEvents(trackEventInFlow, {
      EventName: 'HealthRecordHubSearched',
      hrh_searched_term: value,
    });
    resultsPopover.onClose();
  };

  const clearSearchStateHandler = () => {
    setSearchText('');
    if (!isMobile) {
      setIsExpanded(false);
    }
    fetchMedicalRecord([]);
    updateSearchParams('');
    resultsPopover.onClose();
  };

  const keyUpHandler = (event: KeyboardEvent<HTMLInputElement>) => {
    if (event.key === 'Enter' && searchText.length > 0) {
      searchPrefillHandler(searchText.trim());
    }
    if (event.key === 'Enter' && searchText.length === 0) {
      clearSearchStateHandler();
    }
  };

  useEffect(() => {
    if (isMobile) {
      setIsExpanded(true); // Set the search bar as expanded to show the input
      searchInputRef.current?.focus();
    }
  }, [isMobile]);
  useEffect(() => {
    const searchParamText = getKeywordSearchParamValue(searchParams);
    if (!hasFetchedRecords.current && searchParamText && suggestionList?.length) {
      fetchMedicalRecord(suggestionList.map((suggestion) => suggestion.id));
      hasFetchedRecords.current = true; // Prevent future executions
    }
  }, [searchParams, suggestionList]);
  return (
    <Box
      ref={searchInputContainerRef}
      alignSelf="center"
    >
      <Popover
        autoFocus={false}
        returnFocusOnClose={false}
        isOpen={resultsPopover.isOpen}
        onClose={resultsPopover.onClose}
        placement="bottom-start"
        closeOnBlur={false}
      >
        <PopoverTrigger>
          <InputGroup width="max-content">
            <InputLeftElement
              w="36px"
              h="36px"
              cursor="pointer"
              onClick={handleExpand}
              display={{ base: 'none', md: 'flex' }}
              _hover={{
                '& ~ input': {
                  borderColor: 'fluentHealth.500',
                },
              }}
            >
              <SearchIcon
                size={18}
                color={theme.colors.gray[500]}
              />
            </InputLeftElement>
            <Input
              ref={searchInputRef}
              value={searchText}
              onInput={searchInputChangeHandler}
              onKeyUp={keyUpHandler}
              type="text"
              onBlur={(e: any) => {
                if (e.target.value) {
                  searchPrefillHandler(e.target.value);
                } else {
                  clearSearchStateHandler();
                }
              }}
              placeholder={isMobile ? 'Search' : 'Search...'}
              fontSize="sm"
              w={isExpanded ? { base: '212px', lg: '300px', md: '163px' } : '36px'}
              h="36px"
              pl={{ base: '16px', md: '34px' }}
              bg={isExpanded && !isMobile ? 'white' : 'transparent'}
              fontWeight={{ base: '500', md: '400' }}
              color={{ base: 'gray.500', md: 'inherit' }}
              _placeholder={isMobile ? { color: 'gray.500' } : undefined}
              borderColor="iris.100"
              marginRight={{ base: '8px', md: 0 }}
              borderRadius="24px"
              transition="width .2s ease, border .2s ease"
              {...(isExpanded ? {} : { paddingInlineEnd: 0 })}
              _hover={{ borderColor: 'fluentHealth.500' }}
              _focus={{ borderColor: 'fluentHealth.500' }}
              _focusVisible={{ borderColor: 'fluentHealth.500' }}
              _active={{ borderColor: 'fluentHealth.500' }}
            />
            {isExpanded && !isMobile && (
              <InputRightElement
                w="36px"
                h="36px"
                cursor="pointer"
              >
                <IconButton
                  icon={<CloseIcon size={18} />}
                  aria-label="Icon Button"
                  bgColor="transparent"
                  color="fluentHealthText.300"
                  size="18px"
                  p="6px"
                  borderRadius="50%"
                  flexShrink="0"
                  _hover={{
                    color: 'fluentHealthText.100',
                    bgColor: 'fluentHealthSecondary.300',
                  }}
                  onClick={clearSearchStateHandler}
                />
              </InputRightElement>
            )}
            {isMobile && (
              <InputRightElement
                w="36px"
                h="36px"
                cursor="pointer"
                opacity={0.5}
                marginRight="12px"
              >
                <SearchIcon
                  size={18}
                  color={theme.colors.fluentHealthSecondary[100]}
                />
              </InputRightElement>
            )}
          </InputGroup>
        </PopoverTrigger>
        <PopoverContent
          width="300px"
          borderRadius="12px"
          borderColor="fluentHealthSecondary.300"
          p="4px"
          boxShadow="0px 1px 4px rgba(7, 16, 84, 0.1), 0px 10px 28px -2px rgba(7, 16, 84, 0.14)"
          maxH="300px"
          overflowY="auto"
        >
          {suggestionList?.length === 0 && !isFetchingSuggestions && (
            <Text
              color="gray.300"
              p="10px"
            >
              No results found. Check your spelling or try a different search term.
            </Text>
          )}
          {suggestionList &&
            suggestionList?.length > 0 &&
            suggestionList?.map((result: any) => (
              <Flex
                key={result?.id}
                justify="space-between"
                align="center"
                borderRadius="8px"
                py="10px"
                px="8px"
                cursor="pointer"
                _hover={{ bg: 'fluentHealthSecondary.500' }}
                _active={{ bg: 'fluentHealthSecondary.500' }}
                _focus={{ bg: 'fluentHealthSecondary.500' }}
                onClick={() => searchPrefillHandler(result?.description || '')}
              >
                <Text maxW="200px">{result?.description}</Text>
                {/* // * description has been used as a keyword for search filter */}
                <IconButton
                  icon={<ArrowUpLeftIcon size={18} />}
                  aria-label="Icon Button"
                  bgColor="transparent"
                  color="fluentHealthText.300"
                  size="18px"
                  p="6px"
                  borderRadius="50%"
                  flexShrink="0"
                  _hover={{
                    color: 'fluentHealthText.100',
                    bgColor: 'fluentHealthSecondary.300',
                  }}
                />
              </Flex>
            ))}
          {evalConditions([
            [
              'OR',
              hasNextPage,
              suggestionList?.length === 0 && isFetchingSuggestions,
              <Flex
                ref={loadingElementRef}
                justify="center"
                align="center"
                py="4"
                width="full"
              >
                <Spinner
                  width="18px"
                  height="18px"
                />
              </Flex>,
            ],
          ])}
        </PopoverContent>
      </Popover>
    </Box>
  );
}
