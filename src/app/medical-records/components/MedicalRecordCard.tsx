import { PropsWithChildren, Suspense, useState } from 'react';
import {
  Box,
  <PERSON>ton,
  Card,
  CardBody,
  CardFooter,
  CardHeader,
  Checkbox,
  Collapse,
  Flex,
  Grid,
  GridItem,
  Heading,
  Hide,
  IconButton,
  Link,
  Show,
  Skeleton,
  // Spinner,
  Tag,
  Text,
  UseDisclosureReturn,
  useDisclosure,
  useTheme,
} from '@chakra-ui/react';
import {
  ChevronDown as ArrowDownIcon,
  ChevronUp as ArrowUpIcon,
  // Bookmark as BookmarkIcon,
  Edit3 as EditIcon,
  FileText as FileTextIcon,
  Slash as SlashIcon,
  Trash as TrashIcon,
} from 'react-feather';
import dayjs from 'dayjs';
// import { useClient } from 'urql';
import { DownloadIcon } from '@chakra-ui/icons';
import {
  // recordBookmarkEvents,
  recordEditMedicalRecordEvents,
  recordHealthRecordHubEvents,
  recordRecordDownloadEvents,
  recordReminderEvents,
} from '@user/lib/events-analytics-manager';

import { useAnalyticsService } from '@lib/state';
import { enumContentType, useDocRefDetails, useMedicalRecordList } from '../lib/state';
import { MEDICAL_RECORD_ICON_BG_MAP, MEDICAL_RECORD_ICON_MAP, MEDICAL_RECORD_NAME_MAP } from '../lib/constants';
import {
  MoreActionsMenu,
  MoreActionsMenuButton,
  MoreActionsMenuItem,
  MoreActionsMenuList,
} from 'src/components/ui/Menu';
import { EditMedicalRecordFlow } from './EditMedicalRecordFlow';
import { ReminderFlow } from './ReminderFlow';
import { Modal, ModalProvider, useModal } from 'src/components/Modal';
import { FluentHealthLoader } from 'src/components/FluentHealthLoader';
import { FilesPreview } from './FilesPreview';
import { pluralize } from '@lib/utils/utils';
import { useIsMobile } from 'src/components/ui/hooks/device.hook';
import { BREAKPOINTS } from '@lib/constants';
import { hexOpacity } from '../../../components/theme/utils';
// import { bookmarkARecord } from '../lib/medplum-api';
import { getPercentFromLabReport } from '../lib/utils';
import { ShareLinkModal } from './ShareLinkModal';
import { MEDICAL_RECORD_BEHOLDER_TYPES } from '@lib/models/medical-record';

import { ReactComponent as ShareArrowIcon } from '@assets/icons/share-arrow.svg';
import { ReactComponent as BellIcon } from '@assets/icons/bell.svg';
import { ReactComponent as BellActiveIcon } from '@assets/icons/bell-active.svg';

function MedicalRecordBodySkeleton() {
  return (
    <Flex gap="24px">
      <Flex
        direction="column"
        gap="24px"
        flex="1"
      >
        <Skeleton
          height="24px"
          borderRadius="4px"
          startColor="fluentHealthSecondary.300"
          endColor="fluentHealthSecondary.500"
        />
        <Skeleton
          height="24px"
          borderRadius="4px"
          startColor="fluentHealthSecondary.300"
          endColor="fluentHealthSecondary.500"
        />
        <Skeleton
          height="24px"
          borderRadius="4px"
          startColor="fluentHealthSecondary.300"
          endColor="fluentHealthSecondary.500"
        />
        <Skeleton
          height="24px"
          borderRadius="4px"
          startColor="fluentHealthSecondary.300"
          endColor="fluentHealthSecondary.500"
        />
        <Skeleton
          height="104px"
          borderRadius="8px"
          startColor="fluentHealthSecondary.300"
          endColor="fluentHealthSecondary.500"
        />
      </Flex>
      <Skeleton
        height="300px"
        width="200px"
        mx="8px"
        borderRadius="8px"
        startColor="fluentHealthSecondary.300"
        endColor="fluentHealthSecondary.500"
      />
    </Flex>
  );
}

function DetailsRow({
  labelText,
  value,
  url,
  hasBottomBorder,
}: {
  labelText: string;
  value: string | number;
  url?: string | null;
  hasBottomBorder?: boolean;
}) {
  return (
    <Flex
      py={{ base: '12px', md: '16px' }}
      borderBottom={hasBottomBorder ? 'none' : '1px solid'}
      borderColor="iris.500"
      direction={{ base: 'column', md: 'row' }}
      _last={{ border: 'none' }}
    >
      <Box flex="1">
        <Text color="iris.500">{labelText}</Text>
      </Box>
      <Box
        flex="1"
        wordBreak="break-word"
      >
        {url ? (
          <Link
            href={url}
            isExternal
            color="royalBlue.500"
          >
            {value}
          </Link>
        ) : (
          <Text>{value}</Text>
        )}
      </Box>
    </Flex>
  );
}

interface IRecordDate {
  isFirstItem: boolean;
  isLastItem: boolean;
  docRef: any;
}

export function RecordDate({ isFirstItem, isLastItem = false, docRef }: IRecordDate) {
  const medRecordProps = useDocRefDetails({ docRef });
  const { docRefCreationDate } = medRecordProps || {};

  const period = docRefCreationDate ? new Date(docRefCreationDate).getFullYear() : null;

  return (
    <Flex
      position="relative"
      direction="column"
      align="center"
      display={{ base: 'none', md: 'flex' }}
    >
      {period && (
        <Box
          position="absolute"
          top="-24px"
          left="0"
          right="0"
          mx="auto"
          width="1px"
          height="20px"
          bgColor="fluentHealthSecondary.300"
          bg="#F0F1FF"
          flexShrink="0"
        />
      )}
      <Box
        width={!isFirstItem && !period ? '1px' : '0px'}
        height="26px"
        bgColor="fluentHealthSecondary.300"
        flexShrink="0"
      />
      {period && (
        <Flex
          as={Text}
          justify="center"
          position="absolute"
          top="-5px"
          left="0"
          right="0"
          fontSize="sm"
          lineHeight="1"
          fontWeight="500"
          fontFamily="Apercu Mono"
          p="2px"
          mx="auto"
          color="fluentHealthSecondary.200"
        >
          {period}
        </Flex>
      )}
      <Flex
        direction="column"
        align="center"
        width="60px"
        height="60px"
        borderRadius="8px"
        border="1px solid"
        borderColor="fluentHealthSecondary.300"
        py="8px"
        flexShrink="0"
        gap="8px"
      >
        <Text
          fontSize="xs"
          fontWeight="700"
          lineHeight="1"
          color="fluentHealthSecondary.200"
          textTransform="uppercase"
        >
          {docRefCreationDate ? dayjs(docRefCreationDate).format('MMM') : ''}
        </Text>
        <Text
          fontFamily="heading"
          fontSize="2xl"
          fontWeight="500"
          lineHeight="1"
          color="fluentHealthSecondary.200"
        >
          {docRefCreationDate ? dayjs(docRefCreationDate).format('D') : ''}
        </Text>
      </Flex>
      <Box
        width={isLastItem ? '0px' : '1px'}
        height="100%"
        bg="fluentHealthSecondary.300"
      />
    </Flex>
  );
}

function RemoveRecordModalContent({
  recordId,
  taskId,
  modalState,
  onSuccess,
}: {
  recordId: string;
  taskId: string;
  modalState: UseDisclosureReturn;
  onSuccess: Function;
}) {
  const [isDeleting, setIsDeleting] = useState<boolean>(false);
  // TODO - Need to change the delete flow as flow changed
  const { deleteMedicalRecord } = useMedicalRecordList();

  const deleteRecordHandler = async () => {
    setIsDeleting(true);

    await deleteMedicalRecord(recordId, taskId);

    setIsDeleting(false);
    modalState.onClose();
    onSuccess();
  };

  return (
    <Flex
      direction="column"
      align="center"
      py="32px"
    >
      <Heading
        size="lg"
        maxW="400px"
        textAlign="center"
        marginBottom="16px"
      >
        Are you sure you want
        <br /> to remove this record?
      </Heading>

      <Text
        align="center"
        maxW="400px"
        marginBottom="32px"
        color="gray.400"
      >
        The record you’re deleting might be linked
        <br /> to your medical details. This action cannot
        <br /> be undone.
      </Text>
      <Flex gap="42px">
        <Button
          variant="quietDanger"
          size="xl"
          onClick={deleteRecordHandler}
          isLoading={isDeleting}
        >
          Remove
        </Button>
        <Button
          variant="solid"
          size="xl"
          onClick={modalState.onClose}
          isDisabled={isDeleting}
        >
          Cancel
        </Button>
      </Flex>
    </Flex>
  );
}

// eslint-disable-next-line complexity
function MedicalRecordHeader({
  isCardCollapsed,
  isPublicRecordMode,
  setIsCardCollapsed,
  medRecordProps,
  fetchMedicalRecordList,
  docRef,
  onDownloadRecord,
  isSelected,
  onSelectRecord,
}: PropsWithChildren<{
  isCardCollapsed: boolean;
  isPublicRecordMode: boolean;
  setIsCardCollapsed: () => void;
  medRecordProps: NonNullable<ReturnType<typeof useDocRefDetails>>;
  fetchMedicalRecordList: Function;
  docRef: any;
  onDownloadRecord: (docRefId: any) => void;
  isSelected: any;
  onSelectRecord?: (docRefId: any, isSelected: boolean) => void;
}>) {
  const theme = useTheme();
  // const client = useClient();
  const editRecordModal = useDisclosure();
  const removeRecordModal = useDisclosure();
  const shareRecordModal = useModal();
  const reminderModal = useDisclosure();
  const calendarModal = useDisclosure();
  const { dateOnRecord } = useDocRefDetails({ docRef });

  const period = dateOnRecord ? new Date(dateOnRecord) : null;

  // const [isBookmarkLoading, setIsBookmarkLoading] = useState(false);

  const { trackEventInFlow } = useAnalyticsService();

  const {
    id,
    taskId,
    alert,
    title,
    medicalRecordType,
    nameOnRecord,
    medicalRecordFor,
    // isBookmarked
  } = medRecordProps;
  const { code: recordCode, display } = medicalRecordType || {};
  const recordName = MEDICAL_RECORD_NAME_MAP[recordCode] || display;
  const docRefId = id || '';
  const alertId = (alert?.__typename === 'CommunicationRequest' && alert?.id) || undefined;

  // TODO: Removal of filters and bookmark for now
  // const setFavoriteHandler = async () => {
  //   const operationType = isBookmarked ? 'remove' : 'add';
  //   setIsBookmarkLoading(true);

  //   await bookmarkARecord({
  //     id: docRefId,
  //     client,
  //     operationType,
  //     onSuccess: () => {
  //       recordBookmarkEvents(trackEventInFlow, {
  //         EventName: !isBookmarked ? 'RecordBookmarked' : 'RecordUnbookmarked',
  //         rb_record_type: recordCode,
  //       });
  //     },
  //   });

  //   setIsBookmarkLoading(false);
  //   fetchMedicalRecordList();
  // };
  const isMobile = useIsMobile();

  const onTrackShareEvent = () => {
    shareRecordModal.modalDisclosure.onOpen();
  };
  const expandCardHandler = () => {
    recordHealthRecordHubEvents(trackEventInFlow, {
      EventName: 'ApprovedRecordInteracted',
      ri_record_type: medRecordProps?.medicalRecordType?.display,
      document_id: medRecordProps?.id,
    });
    setIsCardCollapsed();
  };

  const onEditModalOpen = () => {
    editRecordModal.onOpen();
  };

  const onSetReminderHandler = () => {
    reminderModal.onOpen();
    recordReminderEvents(trackEventInFlow, {
      EventName: 'ReminderAddStarted',
      rm_entry_point: 'records',
    });
  };

  const onUpdateReminderHandler = () => {
    reminderModal.onOpen();
    recordReminderEvents(trackEventInFlow, {
      EventName: 'ReminderEditStarted',
      rm_entry_point: 'records',
    });
  };

  return (
    <CardHeader
      display="flex"
      alignItems="flex-start"
      justifyContent="space-between"
      p="8px"
    >
      <Flex
        align="center"
        gap="16px"
      >
        {!isPublicRecordMode && (
          <Checkbox
            isChecked={isSelected}
            size="md"
            ml="10px"
            borderColor="gray.700"
            sx={{
              '& .chakra-checkbox__control[data-checked]': {
                bg: 'royalBlue.500', // ✅ Background when checked
                borderColor: 'royalBlue.500',
              },
              '& .chakra-checkbox__control[data-checked]::before': {
                color: 'royalBlue.500', // ✅ Checkmark color
              },
            }}
            onChange={(e) => onSelectRecord?.(docRefId, e.target.checked)}
          />
        )}
        <Flex
          align="center"
          justify="center"
          width={{ base: '64px', md: '68px' }}
          height={{ base: '64px', md: '68px' }}
          borderRadius="12px"
          bgColor={MEDICAL_RECORD_ICON_BG_MAP[recordCode ?? ''] ?? ''}
        >
          <Box
            maxW="35px"
            height="43px"
          >
            {MEDICAL_RECORD_ICON_MAP[recordCode ?? ''] ?? ''}
          </Box>
        </Flex>
        <Flex
          direction="column"
          gap="2px"
        >
          <Heading
            fontSize={{ base: 'lg', md: '2xl' }}
            fontFamily="Apercu"
            wordBreak="break-word"
          >
            {title || 'No title'}
          </Heading>
          <Flex
            direction="row"
            gap="2px"
          >
            <Text
              color="fluentHealthText.300"
              fontSize={{ base: 'sm', md: 'md' }}
            >
              {recordName && `${recordName}${period ? ` from ${dayjs(period).format('DD MMM. YYYY')}` : ''}`}
              &nbsp;
            </Text>
            {medicalRecordFor === MEDICAL_RECORD_BEHOLDER_TYPES.OTHERS && !isMobile && (
              <Flex
                backgroundColor="papaya.10"
                gap="2px"
                borderRadius="17px"
                padding="2px 8px"
                alignItems="center"
                width="max-content"
                marginTop="-0.5"
              >
                <Text
                  color="papaya.400"
                  fontSize={{ base: 'sm', md: 'md' }}
                >
                  {nameOnRecord}
                </Text>
              </Flex>
            )}
          </Flex>
          {medicalRecordFor === MEDICAL_RECORD_BEHOLDER_TYPES.OTHERS && isMobile && (
            <Flex
              backgroundColor="papaya.20"
              gap="2px"
              borderRadius="17px"
              padding="2px 8px"
              alignItems="center"
              width="max-content"
            >
              <Text
                color="papaya.600"
                fontSize={{ base: 'sm', md: 'md' }}
              >
                {nameOnRecord}
              </Text>
            </Flex>
          )}
        </Flex>
      </Flex>
      {isPublicRecordMode ? (
        !isCardCollapsed && (
          <Button
            variant="ghost"
            iconSpacing="4px"
            rightIcon={<ArrowDownIcon size={14} />}
            fontSize="sm"
            color="fluentHealthText.200"
            gap="0px"
            height="auto"
            py="0"
            px="0"
            mt="auto"
            onClick={setIsCardCollapsed}
          >
            Expand
          </Button>
        )
      ) : (
        <>
          <Flex
            direction="column"
            alignSelf="stretch"
          >
            <Flex
              align="center"
              gap="4px"
              mt="4px"
            >
              {!isPublicRecordMode && (
                <IconButton
                  icon={
                    alertId ? (
                      <BellActiveIcon
                        width="26px"
                        height="26px"
                        {...(alertId
                          ? {
                              fill: theme.colors.royalBlue[500],
                              color: theme.colors.royalBlue[500],
                            }
                          : {})}
                      />
                    ) : (
                      <BellIcon
                        width="18px"
                        height="18px"
                        {...(alertId
                          ? {
                              fill: theme.colors.royalBlue[500],
                              color: theme.colors.royalBlue[500],
                            }
                          : {})}
                      />
                    )
                  }
                  aria-label="Reminder Button"
                  bgColor="transparent"
                  color="fluentHealthText.300"
                  size="28px"
                  p="6px"
                  borderRadius="50%"
                  _hover={{
                    color: 'fluentHealthText.100',
                    bgColor: 'fluentHealthSecondary.300',
                  }}
                  onClick={alertId ? onUpdateReminderHandler : onSetReminderHandler}
                />
              )}
              {/* <IconButton
                icon={
                  isBookmarkLoading ? (
                    <Spinner
                      width="18px"
                      height="18px"
                    />
                  ) : (
                    <BookmarkIcon
                      size={18}
                      {...(isBookmarked
                        ? {
                            fill: theme.colors.royalBlue[500],
                          }
                        : {})}
                    />
                  )
                }
                aria-label="Bookmark Button"
                bgColor="transparent"
                color={isBookmarked ? 'royalBlue.500' : 'fluentHealthText.300'}
                size="28px"
                p="6px"
                borderRadius="50%"
                _hover={{
                  color: isBookmarked ? 'royalBlue.500' : 'fluentHealthText.100',
                  bgColor: 'fluentHealthSecondary.300',
                }}
                onClick={setFavoriteHandler}
                isDisabled={isBookmarkLoading}
              /> */}
              <MoreActionsMenu>
                <MoreActionsMenuButton
                  iconSize={18}
                  aria-label="Bookmark Button"
                />
                <MoreActionsMenuList>
                  <MoreActionsMenuItem
                    icon={<EditIcon size={16} />}
                    onClick={onEditModalOpen}
                  >
                    Edit record
                  </MoreActionsMenuItem>

                  <MoreActionsMenuItem
                    icon={<DownloadIcon />}
                    onClick={() => onDownloadRecord(docRefId)}
                  >
                    Download record
                  </MoreActionsMenuItem>

                  <MoreActionsMenuItem
                    icon={<ShareArrowIcon />}
                    onClick={() => onTrackShareEvent()}
                  >
                    Share record
                  </MoreActionsMenuItem>

                  <MoreActionsMenuItem
                    icon={<TrashIcon size={16} />}
                    onClick={removeRecordModal.onOpen}
                  >
                    Remove record
                  </MoreActionsMenuItem>
                </MoreActionsMenuList>
              </MoreActionsMenu>
            </Flex>
            {!isCardCollapsed && (
              <Button
                variant="ghost"
                iconSpacing="4px"
                rightIcon={
                  <ArrowDownIcon
                    color={isMobile ? '#14181A' : '#686A6C'}
                    size={isMobile ? 16 : 14}
                  />
                }
                fontSize="sm"
                color="fluentHealthText.200"
                gap="0px"
                height="auto"
                py="0"
                px={{ base: '6px', md: '0px' }}
                mt="auto"
                width="auto"
                display="flex"
                justifyContent="flex-end"
                alignItems="center"
                onClick={expandCardHandler}
              >
                {!isMobile && 'Expand'}
              </Button>
            )}
          </Flex>
          {editRecordModal.isOpen && (
            <EditMedicalRecordFlow
              editRecordModal={editRecordModal}
              medRecordProps={medRecordProps}
              fetchMedicalRecordList={fetchMedicalRecordList}
            />
          )}
          {(reminderModal.isOpen || calendarModal.isOpen) && (
            <ReminderFlow
              medRecordProps={medRecordProps}
              reminderRecordModal={reminderModal}
              calendarModal={calendarModal}
              onSuccess={() => {
                fetchMedicalRecordList();
              }}
            />
          )}
          <Modal
            showModalHeading={false}
            showModalFooter={false}
            isCentered
            {...removeRecordModal}
          >
            <Suspense fallback={<FluentHealthLoader my="12" />}>
              <RemoveRecordModalContent
                recordId={id ?? ''}
                taskId={taskId ?? ''}
                modalState={removeRecordModal}
                onSuccess={() => {
                  fetchMedicalRecordList();
                  recordEditMedicalRecordEvents(trackEventInFlow, {
                    EventName: 'RecordRemoved',
                    re_record_type: medRecordProps?.medicalRecordType?.display,
                    document_id: medRecordProps?.id,
                  });
                }}
              />
            </Suspense>
          </Modal>
          <ModalProvider {...shareRecordModal}>
            <ShareLinkModal records={[medRecordProps]} />
          </ModalProvider>
        </>
      )}
    </CardHeader>
  );
}

function MedicalRecordFooter() {
  return (
    <CardFooter
      bgColor="fluentHealthSecondary.500"
      color="fluentHealthSecondary.100"
      p="20px"
      borderTop="1px solid"
      borderColor="fluentHealthSecondary.400"
      borderBottomLeftRadius="20px"
      borderBottomRightRadius="20px"
      marginTop="12px"
    >
      <Text>
        Your original files are the most reliable source of truth. The data provided has been reviewed but may contain
        errors or inaccuracies. We make no representations that this summary is complete.
      </Text>
    </CardFooter>
  );
}

function ItemAnalysed({
  name,
  value,
  unit,
  low,
  high,
  redLabelLow,
  redLabelHigh,
  pointerPosition,
}: {
  name: string;
  value: string;
  unit: string;
  low: string;
  high: string;
  redLabelLow: boolean | undefined;
  redLabelHigh: boolean | undefined;
  pointerPosition: number | undefined;
}) {
  let pointer = '85px';
  if (redLabelHigh) {
    pointer = '5px';
  } else if (redLabelLow) {
    pointer = '105px';
  } else if (pointerPosition) {
    pointer = `${pointerPosition}px`;
  }
  return (
    <Flex
      padding="0px 12px"
      width="100%"
      alignItems="center"
    >
      <Flex flex="1">
        <Text width="80%">{name}</Text>
      </Flex>
      <Flex
        alignItems="center"
        justifyContent="space-between"
        flex="1"
      >
        <Flex
          gap="8px"
          alignItems="center"
          minHeight="28px"
        >
          <Text
            fontWeight="700"
            fontSize="sm"
            color={redLabelLow || redLabelHigh ? '#F55656' : 'fluentHealthText.100'}
          >
            {value}
          </Text>
          <Text
            fontSize="xs"
            color="fluentHealthText.400"
          >
            {unit && unit}
          </Text>
        </Flex>
        {(low || high) && (
          <Flex
            position="relative"
            alignItems="center"
            height="32px"
          >
            <Box
              height="2px"
              bg={redLabelLow ? '#F55656' : 'fluentHealthText.500'}
              width="19px"
            />
            <Box
              height="6px"
              bg="#4B5154"
              width="1px"
              borderRadius="6px"
            />
            <Box
              height="2px"
              bg="#4B5154"
              width="59px"
            />
            <Box
              height="6px"
              bg="#4B5154"
              width="1px"
              borderRadius="6px"
            />
            <Box
              height="2px"
              bg={redLabelHigh ? '#F55656' : 'fluentHealthText.500'}
              width="20px"
            />
            <Box
              position="relative"
              right={pointer}
              top="-5px"
              fontSize="10px"
              color={redLabelLow || redLabelHigh ? '#F55656' : 'fluentHealthText.100'}
            >
              &#9660;
            </Box>
            {low && (
              <Box
                position="absolute"
                top="20px"
                left="10px"
              >
                <Text
                  fontSize="xs"
                  color="fluentHealthText.400"
                >
                  {!high ? `> ${low}` : low}
                </Text>
              </Box>
            )}
            {high && (
              <Box
                position="absolute"
                top="20px"
                right="10px"
              >
                <Text
                  fontSize="xs"
                  color="fluentHealthText.400"
                >
                  {!low ? `< ${high}` : high}
                </Text>
              </Box>
            )}
          </Flex>
        )}
      </Flex>
    </Flex>
  );
}

function MedicalRecordDetailsPanel({ observations }: any) {
  const renderObservation = (observation: any) => {
    if (!observation?.resource) return null;
    const { code, referenceRange, valueQuantity, valueString } = observation?.resource || {};
    const valueData = valueString || valueQuantity?.value;
    const unit = valueQuantity?.unit;
    const lowValue = referenceRange?.find((r: any) => r.low?.value != null)?.low?.value;
    const highValue = referenceRange?.find((r: any) => r.high?.value != null)?.high?.value;
    const reportInPercent = getPercentFromLabReport(valueData, lowValue, highValue);
    let redLabelLow = false;
    let redLabelHigh = false;
    let pointerPosition = 85;

    if (typeof reportInPercent === 'number') {
      if (reportInPercent > 100) {
        redLabelHigh = true;
      } else if (reportInPercent < 0) {
        redLabelLow = true;
      } else {
        const reportInPixel = Math.trunc((reportInPercent * 60) / 100);
        pointerPosition = 85 - reportInPixel;
      }
    }

    const display = code?.coding?.find((key: any) => key.system?.includes('loinc'))?.display;

    return (
      <Flex
        flexDirection="column"
        width="100%"
        key={observation?.id}
      >
        <Flex
          width="100%"
          flexDirection="column"
        >
          <ItemAnalysed
            name={display}
            value={valueData}
            unit={unit}
            low={lowValue}
            high={highValue}
            redLabelLow={redLabelLow}
            redLabelHigh={redLabelHigh}
            pointerPosition={pointerPosition}
          />
        </Flex>
      </Flex>
    );
  };

  return (
    <Flex
      flexDirection="column"
      mt="54px"
    >
      <Flex
        bg="periwinkle.50"
        padding="32px 9px 24px 9px"
        borderRadius="12px"
      >
        <Flex
          flexDirection="column"
          width="100%"
          gap="32px"
        >
          {observations?.map(renderObservation)}
        </Flex>
      </Flex>
    </Flex>
  );
}

function MedicalRecordBody({
  setIsCardCollapsed,
  isPublicRecordMode = false,
  medRecordProps,
}: // currentTabName,
PropsWithChildren<{
  medRecordProps: NonNullable<ReturnType<typeof useDocRefDetails>>;
  setIsCardCollapsed: () => void;
  isPublicRecordMode: boolean;
  // currentTabName: string;
}>) {
  const showAllInfoDisclosure = useDisclosure();
  const isMobile = useIsMobile();
  const theme = useTheme();

  const {
    tags,
    notes,
    supportingURL,
    typeOfVisit,
    speciality,
    location,
    nameOnRecord,
    doctorName,
    wasReferred,
    conditionType,
    // medicalRecordType,
    creationDate,
    recordAttachments: allFiles,
    isLabReport,
    diagnosticReport,
  } = medRecordProps;
  const recordAttachments = allFiles?.filter(
    (file: any) =>
      file.contentType !== enumContentType.JSON &&
      file.contentType !== enumContentType.DICOM &&
      file.contentType !== enumContentType.PLAINTEXT
  );
  const observations = diagnosticReport?.result;
  return (
    <>
      <Grid
        templateColumns="repeat(12, 1fr)"
        gap={6}
        mb="42px"
      >
        <GridItem colSpan={{ base: 12, md: 9 }}>
          {recordAttachments?.length && (
            <Hide breakpoint={BREAKPOINTS.TABLET}>
              <FilesPreview files={recordAttachments}>
                {(props) => (
                  <Flex
                    gap="4px"
                    align="center"
                    mb="18px"
                    {...props}
                  >
                    <FileTextIcon
                      size={20}
                      style={{ color: hexOpacity(theme.colors.iris[500], 0.5) }}
                    />
                    <Text
                      color="iris.500"
                      fontSize="lg"
                      lineHeight={1}
                    >
                      {`View ${pluralize('file', recordAttachments.length)} (${recordAttachments.length})`}
                    </Text>
                  </Flex>
                )}
              </FilesPreview>
            </Hide>
          )}
          <Box
            p="16px"
            borderRadius="16px"
            border="1px solid"
            borderColor="iris.500"
            bg={hexOpacity(theme.colors.iris[500], 0.2)}
          >
            <DetailsRow
              labelText="Name on record"
              value={nameOnRecord ?? '-'}
            />

            <DetailsRow
              labelText="Doctor"
              value={doctorName ?? '-'}
            />
            <DetailsRow
              labelText="My Notes"
              value={notes ?? '-'}
            />
            <DetailsRow
              labelText="Supporting URL"
              value={supportingURL ?? '-'}
              url={supportingURL}
            />
            <DetailsRow
              labelText="Condition associated with this record"
              value={conditionType?.display || '-'}
              hasBottomBorder={!showAllInfoDisclosure.isOpen}
            />
            {/* // Todo can be looped over */}
            <Collapse
              in={showAllInfoDisclosure.isOpen}
              animateOpacity
            >
              <DetailsRow
                labelText="Speciality"
                value={speciality?.display || '-'}
              />

              <DetailsRow
                labelText="Did a doctor prescribe this?"
                value={wasReferred ? 'Yes' : 'No'}
              />
              <DetailsRow
                labelText="Hospital/clinic"
                value={location ?? '-'}
              />
              <DetailsRow
                labelText="Type of visit"
                value={typeOfVisit?.display || '-'}
              />
            </Collapse>
            <Button
              variant="ghost"
              iconSpacing="4px"
              rightIcon={showAllInfoDisclosure.isOpen ? <ArrowUpIcon size={14} /> : <ArrowDownIcon size={14} />}
              color="fluentHealth.500"
              gap="0px"
              height="auto"
              py="0"
              px="0"
              mt="12px"
              onClick={() => {
                showAllInfoDisclosure.onToggle();
              }}
            >
              Show {showAllInfoDisclosure.isOpen ? 'less' : 'more'}
            </Button>
          </Box>
        </GridItem>
        <Show breakpoint={BREAKPOINTS.TABLET}>
          <GridItem
            colSpan={3}
            rotate="20"
          >
            <Flex
              direction="column"
              align="center"
              gap="18px"
            >
              {recordAttachments?.[0]?.url ? (
                <>
                  <FilesPreview files={recordAttachments} />
                  <Text color="fluentHealthText.300">
                    {recordAttachments.length}
                    &nbsp;
                    {pluralize('file', recordAttachments?.length ?? 0)}
                  </Text>
                </>
              ) : (
                <>
                  <Box mt="20">
                    <SlashIcon
                      size={32}
                      color="grey"
                    />
                  </Box>
                  <Text color="fluentHealthText.300">No files</Text>
                </>
              )}
            </Flex>
          </GridItem>
        </Show>
      </Grid>

      {isLabReport && observations?.length && <MedicalRecordDetailsPanel observations={observations} />}
      {/* {isDocRefInReview && <FileNotAnalyzed fileCount={recordAttachments?.length ?? 0} />} */}

      {/* TODO: Only one of these 2 components (FileNotAnalyzed / MedicalRecordDetailsPanel) will be shown in the future */}
      {/* {record.type.slug === 'mr-lab' ? ( */}
      {/* {isActive && isLabReport && (
        <MedicalRecordDetailsPanel
          fileName={fileName}
          observations={observations}
        />
      )} */}
      <Flex
        mt="40px"
        align={{ base: 'flex-start', md: 'center' }}
        direction={{ base: 'column', md: 'row' }}
      >
        {!isPublicRecordMode && (
          <Flex
            gap="8px"
            ml="4px"
            mt="-8px"
            alignItems="center"
          >
            <Text color="fluentHealthText.300">Tags</Text>
            <Flex
              gap="8px"
              wrap="wrap"
            >
              {tags?.length ? (
                tags?.map((tag: any) => (
                  <Tag
                    key={tag?.code}
                    bgColor="periwinkle.50"
                    color="periwinkle.700"
                    fontWeight="400"
                    paddingY="4px"
                    lineHeight="24px"
                    borderRadius="8px"
                  >
                    {tag?.code}
                  </Tag>
                ))
              ) : (
                <Text>No tags</Text>
              )}
            </Flex>
          </Flex>
        )}

        <Flex
          gap="16px"
          ml="auto"
          mt={{ base: '16px', md: '6px' }}
          width={{ base: '100%', md: 'auto' }}
          justify="space-between"
        >
          {creationDate && (
            <Text
              fontSize="sm"
              color="fluentHealthText.300"
            >
              Created at {dayjs(creationDate).format('D MMMM YYYY [at] h:mm A')}
            </Text>
          )}
          <Button
            variant="ghost"
            iconSpacing="4px"
            rightIcon={<ArrowUpIcon size={14} />}
            fontSize="sm"
            color="fluentHealthText.200"
            gap="0px"
            height="auto"
            py="0"
            px="0"
            minWidth="auto"
            onClick={setIsCardCollapsed}
          >
            {!isMobile && 'Close'}
          </Button>
        </Flex>
      </Flex>
    </>
  );
}

export function MedicalRecordCard({
  isCardCollapsedByDefault = false,
  isPublicRecordMode = false,
  taskId,
  docRef,
  onDownloadRecord,
  fetchMedicalRecordList,
  onSelectRecord,
  isSelected,
}: {
  isCardCollapsedByDefault?: boolean;
  isPublicRecordMode?: boolean;
  taskId: string;
  docRef: any;
  onDownloadRecord: (docRefId: any) => void;
  fetchMedicalRecordList: Function;
  onSelectRecord?: (docRefId: any, isSelected: boolean) => void;
  isSelected: boolean;
}) {
  const { isOpen: isCardCollapsed, onToggle: setIsCardCollapsed } = useDisclosure({
    defaultIsOpen: isCardCollapsedByDefault,
  });

  const { trackEventInFlow } = useAnalyticsService();
  const medRecordProps = useDocRefDetails({ docRef, taskId });
  const { id: docRefId } = medRecordProps;

  return (
    <Card
      width="100%"
      bgColor="transparent"
      border="1px solid"
      borderRadius="20px"
      borderColor="fluentHealthSecondary.300"
      boxShadow="0px 1px 4px rgba(73, 90, 228, 0.12)"
      p="0"
      my={{ base: '4px', md: '12px' }}
    >
      <MedicalRecordHeader
        isCardCollapsed={isCardCollapsed}
        isPublicRecordMode={isPublicRecordMode}
        setIsCardCollapsed={setIsCardCollapsed}
        medRecordProps={medRecordProps}
        fetchMedicalRecordList={fetchMedicalRecordList}
        docRef={docRef}
        onDownloadRecord={() => {
          recordRecordDownloadEvents(trackEventInFlow, {
            EventName: 'RecordDownloaded',
            rd_record_type: medRecordProps?.medicalRecordType?.display,
            document_id: medRecordProps?.id,
          });
          onDownloadRecord(docRefId);
        }}
        onSelectRecord={onSelectRecord}
        isSelected={isSelected}
      />
      <Collapse
        in={isCardCollapsed}
        animateOpacity
        unmountOnExit
      >
        <CardBody
          px="0"
          pt={{ base: '14px', md: '20px' }}
          paddingBottom={isPublicRecordMode ? '24px' : '0px'}
        >
          <Box px="20px">
            <Suspense fallback={<MedicalRecordBodySkeleton />}>
              <MedicalRecordBody
                setIsCardCollapsed={() => {
                  setIsCardCollapsed();
                }}
                isPublicRecordMode={isPublicRecordMode}
                medRecordProps={medRecordProps}
                // currentTabName={currentTabName}
              />
            </Suspense>
          </Box>
          {!isPublicRecordMode && <MedicalRecordFooter />}
        </CardBody>
      </Collapse>
    </Card>
  );
}
