import { useSearchParams } from 'react-router-dom';
import { Checkbox, Flex } from '@chakra-ui/react';
import { recordMedicalRecordFilterEvents } from '@user/lib/events-analytics-manager';

import { useIsMobile } from 'src/components/ui/hooks/device.hook';
import { MEDICAL_RECORD_FILTERS } from '@lib/models/medical-record';
import { SearchBar } from './SearchBar';
// TODO: Removal of filters and bookmark for now
// import { SidebarFilter } from './SidebarFilter';
import { PeriodSelector } from '../../../components/PeriodSelector';
// import { BookmarkButton } from './BookmarkButton';
import { DateRange } from 'src/components/ui/DatePicker';
import { getDateSearchParamValue } from '../lib/utils';
import { useAnalyticsService, usePublicRecordSettings } from '@lib/state';

export function PeriodFilter({
  strategy = 'absolute',
  fullWidth = false,
}: {
  strategy?: 'absolute' | 'fixed';
  fullWidth?: boolean;
}) {
  const [searchParams, setSearchParams] = useSearchParams();
  const { trackEventInFlow } = useAnalyticsService();

  const initialDateRange: DateRange = [
    getDateSearchParamValue(MEDICAL_RECORD_FILTERS.FROM_DATE, searchParams),
    getDateSearchParamValue(MEDICAL_RECORD_FILTERS.TO_DATE, searchParams),
  ];

  const periodChangeHandler = async (fromDate: Date | null = null, toDate: Date | null = null) => {
    if (fromDate && fromDate?.toString()?.length > 0) {
      searchParams.set(MEDICAL_RECORD_FILTERS.FROM_DATE, fromDate?.toISOString());
    } else {
      searchParams.delete(MEDICAL_RECORD_FILTERS.FROM_DATE);
    }

    if (toDate && toDate?.toString()?.length > 0) {
      recordMedicalRecordFilterEvents(trackEventInFlow, {
        EventName: 'RecordsFilterRemoved',
        filter_type: 'Date Range',
        filter_value: `${fromDate} - ${toDate}`,
      });
      const toEndTime = new Date(toDate?.setHours(23, 59, 59));
      searchParams.set(MEDICAL_RECORD_FILTERS.TO_DATE, toEndTime?.toISOString());
    } else {
      searchParams.delete(MEDICAL_RECORD_FILTERS.TO_DATE);
    }

    await setSearchParams(searchParams, { replace: true });
  };

  const clearDateRange = () => {
    recordMedicalRecordFilterEvents(trackEventInFlow, {
      EventName: 'RecordsFilterRemoved',
      filter_type: 'Date Range',
    });
  };

  return (
    <PeriodSelector
      initialDate={initialDateRange}
      onChange={periodChangeHandler}
      clearDateRange={clearDateRange}
      strategy={strategy}
      fullWidth={fullWidth}
    />
  );
}

export function MedicalRecordListFilters({
  allSelected,
  handleSelectAll,
  fetchMedicalRecord,
  displayMedicalRecordList,
}: {
  allSelected: boolean;
  handleSelectAll: (isChecked: boolean) => void;
  fetchMedicalRecord: (docId: string[]) => void;
  displayMedicalRecordList: any;
}) {
  const isMobile = useIsMobile();
  const { isPublicRecordMode, isMultiRecord } = usePublicRecordSettings();
  return (
    <Flex
      justifyContent={isMobile ? 'space-between' : undefined}
      width={{ base: '100%', md: 'auto' }}
      gap="8px"
    >
      <Flex>
        {isMobile && !isPublicRecordMode && !!displayMedicalRecordList?.length && (
          <Checkbox
            mr="8px"
            isChecked={allSelected}
            onChange={(e: any) => handleSelectAll(e.target.checked)}
            size="md"
            borderColor="gray.700"
            sx={{
              '& .chakra-checkbox__control[data-checked]': {
                bg: 'royalBlue.500', // ✅ Background when checked
                borderColor: 'royalBlue.500',
              },
              '& .chakra-checkbox__control[data-checked]::before': {
                color: 'royalBlue.500', // ✅ Checkmark color
              },
            }}
          />
        )}
        {!!displayMedicalRecordList?.length && (!isPublicRecordMode || isMultiRecord) && (
          <SearchBar fetchMedicalRecord={fetchMedicalRecord} />
        )}
      </Flex>
      {!isMobile && !isPublicRecordMode && !!displayMedicalRecordList?.length && (
        <Flex
          as="label"
          align="center"
          gap="8px"
          px="12px"
          py="4px"
          borderRadius="full"
          border="1px solid"
          color={allSelected ? 'royalBlue.500' : 'charcoal.100'}
          borderColor={allSelected ? 'fluentHealth.500' : 'iris.100'}
          boxShadow={allSelected ? '0 0 0 2px #DADCFF' : 'none'}
          cursor="pointer"
          transition="all 0.2s ease-in-out"
        >
          <Checkbox
            isChecked={allSelected}
            onChange={(e: any) => handleSelectAll(e.target.checked)}
            size="md"
            borderColor="gray.700"
            wordBreak="keep-all"
            sx={{
              '& .chakra-checkbox__control[data-checked]': {
                bg: 'royalBlue.500', // ✅ Background when checked
                borderColor: 'royalBlue.500',
              },
              '& .chakra-checkbox__control[data-checked]::before': {
                color: 'royalBlue.500', // ✅ Checkmark color
              },
            }}
          >
            Select All
          </Checkbox>
        </Flex>
      )}
      {/* {!isMobile && !isPublicRecordMode && (
        <Flex
          gap="8px"
          alignItems="center"
        >
          <SidebarFilter />
          <PeriodFilter />
          <BookmarkButton />
        </Flex>
      )} */}
      {/* {isMobile && !isPublicRecordMode && (
        <Flex
          minHeight="36px"
          gap="8px"
        >
          <SidebarFilter />
          <BookmarkButton />
        </Flex>
      )} */}
    </Flex>
  );
}
