import dayjs from 'dayjs';
import { Buffer } from 'buffer';
import { Suspense, useCallback, useEffect, useState } from 'react';
import {
  Button,
  Checkbox,
  Collapse,
  Divider,
  Flex,
  FormControl,
  FormErrorMessage,
  FormLabel,
  Grid,
  GridItem,
  Input,
  Text,
  UseDisclosureReturn,
  useDisclosure,
  useToast,
} from '@chakra-ui/react';
import { FormProvider, useForm, useFormContext } from 'react-hook-form';
import { ChevronDown as ArrowDownIcon, ChevronUp as ArrowUpIcon } from 'react-feather';
import { recordEditMedicalRecordEvents } from '@user/lib/events-analytics-manager';
import { DOCUMENT_REF } from '@user/lib/constants';

import {
  EDIT_RECORD_VALUESET_URLS,
  FH_UI_CODESYSTEM,
  MEDICAL_RECORD_NAME_MAP,
  USER_META_CODESYSTEM,
} from '../lib/constants';
import { MODAL_VARIANTS, Modal } from 'src/components/Modal';
import { DatePickerField, FormSkeleton, SuggestionOptionProps } from 'src/components/ui/Form';
import { AutoExpandedTextarea } from '../../../components/ui/Form/AutoExpandedTextarea';
import { SearchableSelect, Select, SelectOptionProps } from '../../../components/ui/Select';
import { MedicalRecord } from '@lib/models/medical-record';
import { formatListToSelectOptionsExpand } from '../lib/utils';
import { AddTagsFlow } from './AddTagsFlow';
import { DragAndDropFilePreview } from 'src/components/ui/DragAndDrop';
import { SidebarHelperTooltip } from 'src/app/user/profile/components/SidebarComponents';
import { medplumApi } from '../lib/medplum-api';
import { medplumApi as medplumApi2 } from '../../user/lib/medplum-api';
import { enumContentType, useDocRefDetails, useMedicalRecordTagList } from '../lib/state';
import { pluralize } from '@lib/utils/utils';
import { useAnalyticsService, useAuthService } from '@lib/state';
import { documentReferenceTags } from '@lib/constants';
import {
  CONDITION_URN_UUID,
  ENCOUNTER_URN_UUID,
  FH_STRUCTURE_DEFINITION_DOCUMENTREFERENCE_NOTE,
  FH_STRUCTURE_DEFINITION_PRESCRIBED,
  MEDICATION_STATEMENT_URN_UUID,
  ORGANIZATION_URN_UUID,
  PRACTITIONER_ROLE_URN_UUID,
  PRACTITIONER_URN_UUID,
} from 'src/constants/medplumConstants';

const MAX_ALLOWED_CHARACTERS = 5000;

function DropdownSelect({
  dropdownList = [],
  initialValue = {},
  labelText = '',
  linkId = '',
}: {
  dropdownList: any;
  initialValue: any;
  labelText: string;
  linkId: string;
}) {
  const form = useFormContext();
  const [conditionValue, setConditionValue] = useState<SuggestionOptionProps | null>({
    label: initialValue?.display,
    value: initialValue?.code,
  });

  const onChange = useCallback((option: SelectOptionProps | any) => {
    if (option) {
      form.setValue(linkId, {
        value: option.value,
        label: option.label,
      });
      setConditionValue(option);
    } else {
      form.setValue(linkId, '');
      setConditionValue(null);
    }
    form.trigger([linkId, linkId]);
  }, []);

  return (
    <SearchableSelect
      labelText={labelText}
      defaultValue={{
        value: conditionValue?.value,
        label: conditionValue?.label,
      }}
      options={dropdownList}
      onChange={onChange}
    />
  );
}

function EditMedicalRecordForm({
  medRecordProps,
  setOverwrittenModalProps,
  onSuccessCallback,
  onSuccessOrFailure,
  valueSetsData,
}: {
  medRecordProps: NonNullable<ReturnType<typeof useDocRefDetails>>;
  setOverwrittenModalProps: any;
  onSuccessCallback: Function;
  onSuccessOrFailure: Function;
  valueSetsData: Record<string, { code: string; display: string }[]>;
}) {
  const showAllInfoDisclosure = useDisclosure();
  const { authenticatedUser } = useAuthService();
  const patientId = authenticatedUser?.id;

  const datePickerPopover = useDisclosure();
  const toast = useToast();
  interface ListType {
    item: any;
    title: string;
  }
  const [medicalRecordTagList, setMedicalRecordTagList] = useState<ListType[]>([]);
  useMedicalRecordTagList(authenticatedUser?.id, setMedicalRecordTagList);
  const {
    id,
    isBookmarked,
    tags,
    notes,
    supportingURL,
    title,
    typeOfVisit,
    nameOnRecord,
    dateOnRecord,
    recordAttachments = [],
    recordContentAttachments,
    location,
    medicalRecordType,
    doctorName,
    wasReferred,
    conditionType,
    communicationRequest,
    diagnosticReport,
    familyMemberHistory,
    speciality,
    practitioner,
    practitionerRole,
    condition: conditionData,
    medicationStatement,
    organization,
    encounterId,
  } = medRecordProps;
  // * TS is not able to infer the type when filter(Boolean) is directly used
  const existingTags = tags?.map((tag: any) => tag?.code)?.filter((tag: any): tag is string => Boolean(tag)) || [];
  // Format dateOnRecord to YYYY-MM-DD format for form compatibility
  const formattedDateOnRecord = dateOnRecord
    ? dayjs(dateOnRecord).isValid()
      ? dayjs(dateOnRecord).format('YYYY-MM-DD')
      : dateOnRecord
    : '';

  const form = useForm({
    mode: 'onChange',
    defaultValues: {
      'medical-record-title-of-record': title,
      'medical-record-my-notes': notes,
      'medical-record-speciality': speciality?.code,
      'medical-record-type-of-record': medicalRecordType?.code,
      'medical-record-conditions-associated-with-this-record': conditionType?.code,
      'medical-record-name-of-hospital-clinic': location,
      'medical-record-where-you-referred': wasReferred,
      'medical-record-tags': existingTags,
      'medical-record-date-of-record': formattedDateOnRecord,
      'supporting-url': supportingURL,
      'medical-record-doctor-name': doctorName,
      'medical-record-type-of-visit': typeOfVisit?.code,
      'medical-record-patient-name-listed-on-record': nameOnRecord ?? '',
    },
  });

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    control,
    formState: { isValid, errors, touchedFields },
  } = form;
  const dateField = watch('medical-record-date-of-record');
  interface TagItem {
    type?: null | string; // Adjust type as per your actual requirement
    display: string;
  }

  const tagData: { item: TagItem }[] = medicalRecordTagList || [];
  const { trackEventInFlow } = useAnalyticsService();

  const onSubmit = async (data: any) => {
    const {
      'medical-record-title-of-record': editedTitle,
      'medical-record-date-of-record': dateOfRecord,
      'medical-record-doctor-name': drName,
      'medical-record-my-notes': editedNotes,
      'supporting-url': supportingURLText,
      'medical-record-conditions-associated-with-this-record': condition,
      'medical-record-speciality': editedSpeciality,
      'medical-record-where-you-referred': wereYouReferred,
      'medical-record-name-of-hospital-clinic': hospital,
      'medical-record-type-of-visit': editedTypeOfVisit,
      'medical-record-tags': tagList,
    } = data ?? {};

    const patientRef = patientId && `Patient/${patientId}`;

    recordEditMedicalRecordEvents(trackEventInFlow, {
      EventName: 'EditRecordCompleted',
      re_record_type: medRecordProps?.medicalRecordType?.display,
      document_id: medRecordProps?.id,
    });

    let content = recordContentAttachments;

    if (supportingURLText) {
      const hasDicom = content.some((item: any) => item.attachment?.contentType === enumContentType.DICOM);
      content = hasDicom
        ? content.map((item: any) =>
            item.attachment?.contentType === enumContentType.DICOM
              ? { ...item, attachment: { ...item.attachment, url: supportingURLText } }
              : item
          )
        : [
            ...content,
            { attachment: { contentType: enumContentType.DICOM, url: supportingURLText, title: 'DICOM File' } },
          ];
    }

    const metaTag =
      tagList?.filter(Boolean).map((tag: string) => ({
        system: USER_META_CODESYSTEM,
        code: tag,
        display: tag,
      })) ?? [];

    if (isBookmarked) {
      metaTag.push({
        system: FH_UI_CODESYSTEM,
        code: 'mr-book',
        display: 'Bookmarked',
      });
    }

    const related: any[] = [];
    const entry: any[] = [];

    const pushEntryAndRelated = (
      conditionCheck: boolean,
      urn: string,
      referenceType: string,
      resource: any,
      resourceData: any
    ) => {
      if (!conditionCheck) return;

      const resourceId = resourceData?.id;

      if (resourceId) {
        const fieldsToPatch = ['name', 'specialty', 'code', 'extension'];
        const resourcePatchData = fieldsToPatch
          .filter((field) => resource?.[field])
          .map((field) => ({
            op: 'add',
            path: `/${field}`,
            value: resource[field],
          }));

        if (resourcePatchData.length > 0) {
          const resourcePatchDataBinary = Buffer.from(JSON.stringify(resourcePatchData)).toString('base64');

          related.push({ reference: `${referenceType}/${resourceId}` });

          entry.push({
            request: { method: 'PATCH', url: `${referenceType}/${resourceId}` },
            resource: {
              resourceType: 'Binary',
              contentType: 'application/json-patch+json',
              data: `{{${resourcePatchDataBinary}}}`,
            },
          });
        }
      } else {
        related.push({ reference: urn });

        entry.push({
          fullUrl: urn,
          request: { method: 'POST', url: referenceType },
          resource: {
            ...resource,
            resourceType: referenceType,
            meta: {
              accounts: [{ reference: patientRef }],
            },
          },
        });
      }
    };

    pushEntryAndRelated(
      !!drName,
      PRACTITIONER_URN_UUID,
      'Practitioner',
      {
        name: [{ text: drName }],
      },
      practitioner
    );

    pushEntryAndRelated(
      !!editedSpeciality,
      PRACTITIONER_ROLE_URN_UUID,
      'PractitionerRole',
      {
        specialty: [
          {
            coding: [
              {
                system: 'http://snomed.info/sct',
                code: editedSpeciality?.value,
                display: editedSpeciality?.label,
              },
            ],
          },
        ],
      },
      practitionerRole
    );
    if (condition?.value) {
      const newCoding = await medplumApi2.valueSetList.getFHIRCodingFromCMS(condition?.value);
      pushEntryAndRelated(
        true, // condition already validated above
        CONDITION_URN_UUID,
        'Condition',
        {
          code: {
            coding: newCoding,
          },
          subject: { reference: patientRef },
        },
        conditionData
      );
    }

    pushEntryAndRelated(
      !!hospital,
      ORGANIZATION_URN_UUID,
      'Organization',
      {
        name: hospital,
      },
      organization
    );

    pushEntryAndRelated(
      typeof wereYouReferred === 'boolean',
      MEDICATION_STATEMENT_URN_UUID,
      'MedicationStatement',
      {
        extension: [
          {
            url: FH_STRUCTURE_DEFINITION_PRESCRIBED,
            valueBoolean: wereYouReferred,
          },
        ],
        status: 'active',
        medicationCodeableConcept: { text: 'Did a doctor prescribe this?' },
        subject: { reference: patientRef },
      },
      medicationStatement
    );

    // Special case: no `related.push()` for Encounter
    if (encounterId) {
      entry.push({
        request: { method: 'PUT', url: `Encounter/${encounterId}` },
        resource: {
          id: encounterId,
          status: 'planned',
          class: {
            system: 'http://terminology.hl7.org/CodeSystem/v3-ActCode',
            code: editedTypeOfVisit.value,
            display: editedTypeOfVisit.label,
          },
          resourceType: 'Encounter',
          meta: { accounts: [{ reference: patientRef }] },
        },
      });
    } else {
      entry.push({
        fullUrl: ENCOUNTER_URN_UUID,
        request: { method: 'POST', url: 'Encounter' },
        resource: {
          status: 'planned',
          class: {
            system: 'http://terminology.hl7.org/CodeSystem/v3-ActCode',
            code: editedTypeOfVisit.value,
            display: editedTypeOfVisit.label,
          },
          resourceType: 'Encounter',
          meta: { accounts: [{ reference: patientRef }] },
        },
      });
    }
    if (communicationRequest?.id) {
      related.push({ reference: `CommunicationRequest/${communicationRequest.id}` });
    }
    if (diagnosticReport?.id) {
      related.push({ reference: `DiagnosticReport/${diagnosticReport.id}` });
    }
    if (familyMemberHistory?.id) {
      related.push({ reference: `FamilyMemberHistory/${familyMemberHistory.id}` });
    }
    const context: any = {
      encounter: encounterId ? [{ reference: `Encounter/${encounterId}` }] : [{ reference: ENCOUNTER_URN_UUID }],
      period: { end: dateOfRecord, start: dateOfRecord },
      related,
    };

    const documentPayload: any[] = [
      { op: 'add', path: '/meta/tag', value: metaTag },
      { op: 'add', path: '/description', value: editedTitle },
      {
        op: 'replace',
        path: '/content',
        value: content?.map((item: any) => ({
          attachment: Object.entries(item.attachment).reduce(
            (acc, [key, value]) => (value !== null ? { ...acc, [key]: value } : acc),
            {}
          ),
        })),
      },
      { op: 'replace', path: '/context', value: context },
    ];

    const extension: any[] = editedNotes
      ? [{ url: FH_STRUCTURE_DEFINITION_DOCUMENTREFERENCE_NOTE, valueString: editedNotes }]
      : [];

    if (extension.length > 0) {
      documentPayload.push({ op: 'add', path: '/extension', value: extension });
    }

    const dataDocumentReference: string = Buffer.from(JSON.stringify(documentPayload), 'utf8').toString('base64');

    tagList.forEach((tag: string) =>
      tagData.push({
        item: { display: tag },
      })
    );

    entry.push({
      resource: {
        resourceType: 'Binary',
        contentType: 'application/json-patch+json',
        data: `{{${dataDocumentReference}}}`,
      },
      request: { method: 'PATCH', url: `${DOCUMENT_REF}/${id}` },
    });

    entry.push({
      resource: {
        resourceType: 'Task',
        status: 'requested',
        intent: 'unknown',
        description: 'Update LIST resources with up to date Document Reference Tags',
        identifier: [{ value: documentReferenceTags }],
        input: [
          {
            type: { coding: [{ code: DOCUMENT_REF, display: 'Document Reference' }] },
            valueString: `${DOCUMENT_REF}/${id}`,
          },
        ],
        for: { reference: patientRef, type: 'Patient' },
      },
      request: { method: 'POST', url: 'Task' },
    });

    const payload = {
      resourceType: 'Bundle',
      type: 'transaction',
      entry,
    };

    try {
      setOverwrittenModalProps((prev: any) => ({
        ...prev,
        isPrimaryButtonLoading: true,
      }));
      await medplumApi.medicalRecord.updateOne(payload);
      onSuccessCallback();
      toast({
        title: 'The medical record has been successfully updated.',
        status: 'success',
        duration: 2000,
        isClosable: true,
      });
    } catch (error) {
      toast({
        title: (error as any).message,
        status: 'error',
        duration: 4000,
        isClosable: true,
      });
    } finally {
      onSuccessOrFailure();
    }
  };

  const tagsChangeHandler = (tagList: MedicalRecord['tags']) => {
    setValue('medical-record-tags', tagList);
  };

  const datePickerChangeHandler = (date: Date | null) => {
    setValue('medical-record-date-of-record', dayjs(date).isValid() ? dayjs(date).format('YYYY-MM-DD') : '');
    datePickerPopover.onClose();
  };

  const datePickerClearHandler = () => {
    setValue('medical-record-date-of-record', '');
    datePickerPopover.onClose();
  };

  useEffect(() => {
    if (conditionType) {
      form.setValue('medical-record-conditions-associated-with-this-record', {
        value: conditionType.code,
        label: conditionType.display,
      });
    }
    if (speciality) {
      form.setValue('medical-record-speciality', {
        value: speciality.code,
        label: speciality.display,
      });
    }

    if (typeOfVisit) {
      form.setValue('medical-record-type-of-visit', {
        value: typeOfVisit.code,
        label: typeOfVisit.display,
      });
    }
  }, [speciality, typeOfVisit, conditionType, form]);

  useEffect(() => {
    setOverwrittenModalProps({
      onPrimaryButtonClick: handleSubmit(onSubmit),
      primaryButtonEnabled: isValid,
    });
  }, [isValid]);

  return (
    <FormProvider {...form}>
      <Flex
        direction="column"
        gap="42px"
        py="16px"
      >
        <DragAndDropFilePreview
          file={
            new File(
              Array(10e3),
              recordAttachments?.length > 1
                ? `${recordAttachments.length} ${pluralize('file', recordAttachments.length)} uploaded`
                : title ?? ''
            )
          }
          medicalRecord={
            recordAttachments?.length > 1
              ? {
                  content: recordAttachments,
                }
              : undefined
          }
        />

        <Select
          labelText="Name on record*"
          isDisabled
          options={[
            {
              label: nameOnRecord,
              value: nameOnRecord,
            },
          ]}
          defaultValue={{
            label: nameOnRecord,
            value: nameOnRecord,
          }} //* need to change for others
          onChange={(option: SelectOptionProps | any) =>
            setValue('medical-record-patient-name-listed-on-record', option.value)
          }
          isSearchable={false}
        />
        <Select
          labelText="Type of record*"
          defaultValue={{
            label: MEDICAL_RECORD_NAME_MAP[medicalRecordType?.code] || medicalRecordType?.display,
            value: medicalRecordType?.code,
          }}
          onChange={(option: SelectOptionProps | any) => setValue('medical-record-type-of-record', option.value)}
          isSearchable={false}
          isDisabled
        />
        <FormControl
          variant="floating"
          isInvalid={!!errors?.['medical-record-title-of-record']}
        >
          <Input
            placeholder=" "
            {...register('medical-record-title-of-record', { required: true, minLength: 1 })}
          />
          <FormLabel>Title of record*</FormLabel>
          <FormErrorMessage>This field is required</FormErrorMessage>
        </FormControl>
        <DatePickerField
          // Field props
          name="medical-record-date-of-record"
          labelText="Date of record*"
          errorText="This field is required"
          rules={{ required: true }}
          isInvalid={touchedFields['medical-record-date-of-record'] && control._formValues.date?.length === 0}
          // Datepicker props
          datePickerChangeHandler={datePickerChangeHandler}
          datePickerClearHandler={datePickerClearHandler}
          datePickerPopover={datePickerPopover}
          isClearDateButtonDisabled={dateField?.length === 0}
          selected={dayjs(dateField).isValid() ? dayjs(dateField).toDate() : null}
          popoverProps={{ placement: 'bottom-start' }}
          maxDate={new Date()}
        />
        <FormControl
          variant="floating"
          isInvalid={!!errors?.['medical-record-doctor-name']}
        >
          <Input
            placeholder=" "
            {...register('medical-record-doctor-name', { required: true, minLength: 1 })}
          />
          <FormLabel>Doctor*</FormLabel>
          <FormErrorMessage>This field is required</FormErrorMessage>
        </FormControl>
        <FormControl
          variant="floating"
          position="relative"
          isInvalid={!!errors?.['medical-record-my-notes']}
        >
          <AutoExpandedTextarea
            controllerProps={{
              name: 'medical-record-my-notes',
              control,
              rules: { maxLength: MAX_ALLOWED_CHARACTERS },
            }}
            maxH="220px"
          />
          <FormLabel>My notes</FormLabel>
          <FormErrorMessage>The maximum number of characters allowed is {MAX_ALLOWED_CHARACTERS}</FormErrorMessage>
        </FormControl>
        <Flex>
          <Button
            variant="ghost"
            iconSpacing="4px"
            rightIcon={showAllInfoDisclosure.isOpen ? <ArrowUpIcon size={14} /> : <ArrowDownIcon size={14} />}
            color="fluentHealth.500"
            gap="0px"
            px="0"
            _active={{
              bg: 'none',
            }}
            size="sm"
            onClick={showAllInfoDisclosure.onToggle}
            mt="-12px"
            mb="-20px"
          >
            Add more details
          </Button>
        </Flex>
        <Collapse
          in={showAllInfoDisclosure.isOpen}
          animateOpacity
          unmountOnExit
        >
          <Grid
            templateColumns="repeat(2, 1fr)"
            rowGap="48px"
            columnGap="24px"
          >
            <GridItem
              colSpan={2}
              mt="15px"
            >
              <FormControl
                variant="floating"
                isInvalid={!!errors?.['supporting-url']}
              >
                <Input
                  placeholder=" "
                  {...register('supporting-url', {
                    pattern: {
                      value: /^(https?:\/\/)[^\s/$.?#].[^\s]*$/,
                      message: 'Please enter a valid supporting URL',
                    },
                  })}
                />
                <FormLabel>Supporting URL</FormLabel>
                <FormErrorMessage>{errors?.['supporting-url']?.message || 'This field is required'}</FormErrorMessage>
              </FormControl>
            </GridItem>
            <GridItem colSpan={2}>
              <DropdownSelect
                dropdownList={formatListToSelectOptionsExpand(valueSetsData?.conditionList)}
                initialValue={conditionType}
                labelText="Conditions associated with this record"
                linkId="medical-record-conditions-associated-with-this-record"
              />
            </GridItem>
            <GridItem colSpan={2}>
              <DropdownSelect
                dropdownList={formatListToSelectOptionsExpand(valueSetsData?.specialityList)}
                initialValue={speciality}
                labelText="Speciality"
                linkId="medical-record-speciality"
              />
            </GridItem>
            <GridItem colSpan={2}>
              <Flex
                direction="column"
                gap="4px"
              >
                <Flex>
                  <Text
                    mr="auto"
                    fontSize="lg"
                    color="iris.500"
                  >
                    Did a doctor prescribe this?
                  </Text>
                  <Checkbox
                    variant="toggle"
                    defaultChecked={wasReferred ?? false}
                    onChange={(e) => {
                      setValue('medical-record-where-you-referred', e.target.checked);
                    }}
                  />
                </Flex>
                <Divider borderColor="iris.500" />
              </Flex>
            </GridItem>
            <GridItem colSpan={1}>
              <FormControl
                variant="floating"
                isInvalid={!!errors?.['medical-record-name-of-hospital-clinic']}
              >
                <Input
                  placeholder=" "
                  {...register('medical-record-name-of-hospital-clinic')}
                />
                <FormLabel>Hospital/clinic</FormLabel>
                <FormErrorMessage>This field is required</FormErrorMessage>
              </FormControl>
            </GridItem>
            <GridItem colSpan={1}>
              <DropdownSelect
                dropdownList={formatListToSelectOptionsExpand(valueSetsData?.typeOfVisitList)}
                initialValue={typeOfVisit}
                labelText="Type of visit"
                linkId="medical-record-type-of-visit"
              />
            </GridItem>
          </Grid>
        </Collapse>
        <Flex
          gap="8px"
          align="center"
        >
          <Text
            fontSize="sm"
            color="fluentHealthText.300"
          >
            Tags
          </Text>
          <AddTagsFlow
            maxLength={Infinity}
            initialTagList={existingTags}
            onChange={tagsChangeHandler}
          />
          <Flex ml="auto">
            <SidebarHelperTooltip
              text="Why add tags?"
              tooltipText="Tagging your health records can help you organise them by subject, making it easier to find your records and access them. This also makes sharing your information more efficient."
            />
          </Flex>
        </Flex>
      </Flex>
    </FormProvider>
  );
}

export function EditMedicalRecordFlow({
  medRecordProps,
  editRecordModal,
  fetchMedicalRecordList,
}: {
  editRecordModal: UseDisclosureReturn;
  medRecordProps: NonNullable<ReturnType<typeof useDocRefDetails>>;
  fetchMedicalRecordList: Function;
}) {
  const [overwrittenModalProps, setOverwrittenModalProps] = useState({});
  const [valueSetsData, setValueSetsData] = useState<Record<string, { code: string; display: string }[]>>({});
  const { trackEventInFlow } = useAnalyticsService();

  const onSuccessOrFailure = () => {
    setOverwrittenModalProps((prevState: any) => ({
      ...prevState,
      isPrimaryButtonLoading: false,
    }));

    editRecordModal.onClose();
  };

  useEffect(() => {
    const fetchData = async () => {
      try {
        const result = (await medplumApi?.valueSets?.getAllExpand({ urls: EDIT_RECORD_VALUESET_URLS })) || {};
        recordEditMedicalRecordEvents(trackEventInFlow, {
          EventName: 'EditRecordStarted',
          re_record_type: medRecordProps?.medicalRecordType?.display,
          document_id: medRecordProps?.id,
        });
        setValueSetsData(result);
      } catch (error) {
        console.error('error ::', error);
      }
    };

    fetchData();
  }, []);
  return (
    <Modal
      variant={MODAL_VARIANTS.PERIWINKLE}
      title="Edit record"
      primaryButtonLabel="Save changes"
      showSecondaryButton={false}
      scrollBehavior="inside"
      isCentered
      modalContentProps={{
        background: 'gradient.gradient3',
      }}
      {...editRecordModal}
      {...overwrittenModalProps}
    >
      <Suspense fallback={<FormSkeleton />}>
        <EditMedicalRecordForm
          medRecordProps={medRecordProps}
          valueSetsData={valueSetsData}
          onSuccessOrFailure={onSuccessOrFailure}
          onSuccessCallback={fetchMedicalRecordList}
          setOverwrittenModalProps={setOverwrittenModalProps}
        />
      </Suspense>
    </Modal>
  );
}
