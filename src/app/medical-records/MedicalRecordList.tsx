/* eslint-disable no-nested-ternary */
// Package modules
import { LegacyRef, Suspense, forwardRef, useCallback, useEffect, useRef, useState } from 'react';
import {
  Box,
  Button,
  ChakraProps,
  Container,
  Flex,
  Skeleton,
  Text,
  UseDisclosureReturn,
  useDisclosure,
  useToast,
} from '@chakra-ui/react';
import { useNavigate, useParams, useSearchParams } from 'react-router-dom';
import { PlusCircle as PlusCircleIcon } from 'react-feather';
import { FluentHealthLoader } from '@components/FluentHealthLoader';
import { useClient } from 'urql';
import { recordAddnewRecordsEvents, recordUploadRecordEvents } from '@user/lib/events-analytics-manager';
import { DOCUMENT_REF, ROUTE_ACTIONS, ROUTE_VARIABLES } from '@user/lib/constants';

import { createDownloadRecord } from './lib/medplum-api';
import { downloadFileQuery, downloadRecord, useMedicalRecordListLazyPagination } from './lib/state';
import { useAnalyticsService, useAuthService, usePublicRecordSettings } from '@lib/state';
import { MedicalRecordCard, RecordDate } from './components/MedicalRecordCard';
import { PeriodSelectorSkeleton } from '../../components/PeriodSelector';
import { BookmarkButtonSkeleton } from './components/BookmarkButton';
import { MedicalRecordListFilters } from './components/MedicalRecordListFilters';
import { ShareMedicalRecordFlow } from './components/ShareMedicalRecordFlow';
import { AddMedicalRecordFlow } from './components/add-mr-flow/AddMedicalRecordFlow';
import { useIsMobile } from 'src/components/ui/hooks/device.hook';
import { EmptyStateTabsCard } from './components/EmptyStateCardsTabs';
import { SORT_BY_VALUES, documentUploadWorkflow } from './lib/constants';
import {
  fetchMedicalRecordListActionNeededByDocIds,
  fetchMedicalRecordListByDocIds,
  getModifiedParams,
} from './lib/utils';
import { MedicalRecordInReviewList } from './MedicalRecordInReviewList';
import { MedicalRecordActionNeededList } from './MedicalRecordActionNeededList';
import { downloadFile as downloadFileUtil } from '@lib/utils/utils';
import { AnalyticsEventName, EventPropsNames } from '@lib/analyticsService';

const UPDATE_SETTINGS_DELAY = 5000;
const RECORD_DOWNLOAD = 'urn:uuid:b9606b4f-78bd-875c-aa99-61843b6b42d8';

// eslint-disable-next-line @typescript-eslint/naming-convention
const MedicalRecordCardSkeleton = forwardRef((props: ChakraProps, ref: LegacyRef<HTMLDivElement>) => {
  return (
    <Flex
      ref={ref}
      align="center"
      gap="24px"
      flex="1"
      {...props}
    >
      <Skeleton
        width="60px"
        height="60px"
        borderRadius="8px"
        startColor="fluentHealthSecondary.300"
        endColor="fluentHealthSecondary.500"
      />
      <Skeleton
        width="100%"
        height="70px"
        borderRadius="16px"
        startColor="fluentHealthSecondary.300"
        endColor="fluentHealthSecondary.500"
      />
    </Flex>
  );
});

export function MedicalRecordListSkeleton() {
  return (
    <>
      <Box
        mb="24px"
        mt="12px"
      >
        <hr />
      </Box>
      <Flex
        direction="column"
        gap="24px"
      >
        {[1, 2, 3, 4].map((item) => (
          <MedicalRecordCardSkeleton key={item} />
        ))}
      </Flex>
    </>
  );
}

function EmptyStateWrtFilters() {
  const { isPublicRecordMode } = usePublicRecordSettings();
  let buttonProps;

  return (
    <EmptyStateTabsCard
      title="Uh-oh!"
      description="Check your spelling or try a different search term."
      buttonProps={buttonProps}
      isPublicRecordMode={isPublicRecordMode}
    />
  );
}

function EmptyStateFilters() {
  const { isPublicRecordMode } = usePublicRecordSettings();
  const { trackEventInFlow } = useAnalyticsService();
  const navigate = useNavigate();
  const { DOCUMENTS } = ROUTE_VARIABLES;
  const { ADD } = ROUTE_ACTIONS;

  const buttonProps = {
    btnLabel: 'Upload',
    onAction: () => {
      recordUploadRecordEvents(trackEventInFlow, { EventName: 'UploadRecordStarted' });
      recordAddnewRecordsEvents(trackEventInFlow, {
        EventName: 'AddNewRecordInteracted',
        ar_entry_point: 'Record screen top right icon',
      });
      navigate(`/${DOCUMENTS}/${ADD}`);
    },
  };

  return (
    <EmptyStateTabsCard
      title="Add new health records"
      description="Go paperless. Store your health records here for easy access and convenience."
      buttonProps={buttonProps}
      isPublicRecordMode={isPublicRecordMode}
    />
  );
}

export function SuspendedMedicalRecordList({
  // currentTabName,
  patientId,
  handleSelectRecord,
  selectedRecords,
  fetchMedicalRecord,
  medicalRecordList,
  hasNextPage,
  loadingElementRef,
}: {
  patientId: any;
  handleSelectRecord: (docRefId: string, isSelected: boolean) => void;
  selectedRecords: any;
  fetchMedicalRecord: () => void;
  medicalRecordList: any[];
  hasNextPage: boolean | undefined;
  loadingElementRef: React.MutableRefObject<null>;
}) {
  const { isPublicRecordMode } = usePublicRecordSettings();

  const client = useClient();
  const pollingRef: any = useRef(null);
  const [isDownloading, setIsDownloading] = useState(false);
  const [searchParams] = useSearchParams();
  const showTimeline = ![SORT_BY_VALUES.ALPHABETIC, SORT_BY_VALUES.REVERSE_ALPHABETIC].includes(
    searchParams.get('sort_by') ?? ''
  );
  // const { trackEventInFlow } = useAnalyticsService();
  const toast = useToast();

  useEffect(() => {
    return () => {
      clearInterval(pollingRef.current);
    };
  }, []);

  const downloadFile = useCallback(async (fileUrl: string, fileName?: string) => {
    try {
      await downloadFileUtil(fileUrl, fileName || 'Medical-Record.pdf');
      setIsDownloading(false);
    } catch (error) {
      toast({
        title: 'Something went wrong! Try again later.',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
    }
  }, []);

  const onDownloadRecord = async (docRefId: any, title?: string | null) => {
    setIsDownloading(true);
    const payload = {
      resourceType: 'Bundle',
      type: 'transaction',
      entry: [
        {
          fullUrl: RECORD_DOWNLOAD,
          resource: {
            resourceType: 'Task',
            status: 'requested',
            statusReason: {
              coding: [
                {
                  code: 'Testing Generate PDF',
                  display: 'Testing Generate PDF',
                },
              ],
            },
            intent: 'option',
            priority: 'routine',
            for: {
              reference: `Patient/${patientId}`,
              type: 'Patient',
            },
            code: {
              coding: [
                {
                  code: 'downloadpdf-record',
                  display: 'downloadpdf-record',
                },
              ],
            },
            description: 'Generate PDF',
            input: [
              {
                type: {
                  coding: [
                    {
                      code: 'single',
                      display: 'single',
                    },
                  ],
                },
                valueString: `${DOCUMENT_REF}/${docRefId}`,
              },
            ],
          },
          request: {
            method: 'POST',
            url: 'Task',
          },
        },
      ],
    };
    const data = await createDownloadRecord(payload);
    let taskData: any = {};
    pollingRef.current = setInterval(async () => {
      try {
        taskData = data?.entry?.[0]?.resource?.id && (await downloadRecord(client, data?.entry?.[0]?.resource?.id));
        if (!taskData) {
          toast({
            title: 'Something went wrong! Try again later.',
            status: 'error',
            duration: 3000,
            isClosable: true,
          });
          clearInterval(pollingRef.current);
          setIsDownloading(false);
        }
      } catch (err) {
        toast({
          title: 'Something went wrong! Try again later.',
          status: 'error',
          duration: 3000,
          isClosable: true,
        });
        clearInterval(pollingRef.current);
        setIsDownloading(false);
      }
      if (taskData.status === 'failed') {
        toast({
          title: 'Something went wrong! Try again later.',
          status: 'error',
          duration: 3000,
          isClosable: true,
        });
        clearInterval(pollingRef.current);
        setIsDownloading(false);
      }
      if (taskData.status === 'completed') {
        clearInterval(pollingRef.current);
        const parts = taskData?.output[0]?.valueReference?.reference?.split('/');
        const documentReference = parts && parts[parts.length - 1];
        const fileData = await downloadFileQuery(client, documentReference);
        // Find the attachment with a non-null url (the actual file)
        const attachment = fileData?.content?.find((c: any) => c?.attachment?.url);
        const url = attachment?.attachment?.url;
        // Use the passed title if available and not null, otherwise fallback to attachment title or default
        const rawFileName = (title ?? undefined) || attachment?.attachment?.title || 'Medical-Record.pdf';

        // Remove existing extension and append '.pdf'
        const fileName = `${rawFileName.replace(/\.[^/.]+$/, '')}.pdf`;
        if (documentReference) downloadFile(url, fileName);
      }
    }, UPDATE_SETTINGS_DELAY); // Poll every UPDATE_SETTINGS_DELAY
  };
  if (!medicalRecordList.length) return null;

  return (
    <Box mt="12px">
      {isDownloading ? (
        <FluentHealthLoader />
      ) : (
        medicalRecordList?.map((data: any, index: number) => {
          const docRef = data?.focus?.resource || data?.resource;
          return (
            <Flex
              key={docRef?.id}
              gap="24px"
            >
              {showTimeline && (
                <RecordDate
                  isFirstItem={index === 0}
                  isLastItem={Array.isArray(medicalRecordList) ? index === medicalRecordList.length - 1 : false}
                  docRef={docRef}
                />
              )}

              <MedicalRecordCard
                key={data?.id}
                taskId={data?.id}
                isPublicRecordMode={isPublicRecordMode}
                docRef={docRef}
                fetchMedicalRecordList={fetchMedicalRecord}
                onDownloadRecord={onDownloadRecord}
                onSelectRecord={handleSelectRecord}
                isSelected={selectedRecords?.[docRef?.id] || false} // Pass selected state
              />
            </Flex>
          );
        })
      )}
      {hasNextPage && (
        <MedicalRecordCardSkeleton
          ref={loadingElementRef}
          mb="42px"
        />
      )}
    </Box>
  );
}

export function MedicalRecordListAndHeader({
  // currentTabName,
  isPublicRecordMode = true,
}: {
  // currentTabName: string;
  isPublicRecordMode?: boolean;
}) {
  const isMobile = useIsMobile();
  const stepperModal: UseDisclosureReturn = useDisclosure();
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const { authenticatedUser }: any = useAuthService();
  const { trackEventInFlow, trackEvent } = useAnalyticsService();
  const params = useParams();
  const { action } = params as any;
  const { DOCUMENTS } = ROUTE_VARIABLES;
  const { ADD } = ROUTE_ACTIONS;
  const [selectedRecords, setSelectedRecords] = useState<Record<string, boolean>>({});
  const [medicalRecordList, setMedicalRecordList] = useState<any>([]);
  const [medicalRecordListActionNeeded, setMedicalRecordListActionNeeded] = useState<any>([]);
  const [medicalRecordListReview, setMedicalRecordListReview] = useState<any>([]);
  const { isMultiRecord } = usePublicRecordSettings();
  const {
    medicalRecordList: localMedicalRecordList,
    medicalRecordListActionNeeded: localMedicalRecordListActionNeeded,
    medicalRecordListReview: localMedicalRecordListReview,
    hasNextPage,
    loadingElementRef,
    fetchMedicalRecordList,
  } = useMedicalRecordListLazyPagination(
    authenticatedUser?.id,
    getModifiedParams(searchParams),
    'myDocumentReferenceList',
    documentUploadWorkflow.documentCompleted.completed
  );
  const allSelected =
    medicalRecordList.length > 0 && medicalRecordList.every((data: any) => selectedRecords[data?.focus?.resource?.id]);
  const description = searchParams.get('description');
  const isEmptyMedicalRecordList = ![
    localMedicalRecordList,
    localMedicalRecordListActionNeeded,
    localMedicalRecordListReview,
  ].some((arr) => Array.isArray(arr) && arr.length > 0);
  const isEmptyMedicalRecordListForSearch =
    ![medicalRecordList, medicalRecordListReview, medicalRecordListActionNeeded].some(
      (arr) => Array.isArray(arr) && arr.length > 0
    ) && description;
  const fetchMedicalRecordDocIds = (docIds: string[]) => {
    if (!docIds?.length) {
      setMedicalRecordList(localMedicalRecordList);
      setMedicalRecordListActionNeeded(localMedicalRecordListActionNeeded);
      setMedicalRecordListReview(localMedicalRecordListReview);
    } else {
      // Process filtered records in parallel
      const [filteredRecords, filteredRecordsActionNeeded, filteredRecordsReview] = [
        fetchMedicalRecordListByDocIds(docIds, localMedicalRecordList),
        fetchMedicalRecordListActionNeededByDocIds(docIds, localMedicalRecordListActionNeeded),
        fetchMedicalRecordListByDocIds(docIds, localMedicalRecordListReview),
      ];

      setMedicalRecordList(filteredRecords);
      setMedicalRecordListActionNeeded(filteredRecordsActionNeeded);
      setMedicalRecordListReview(filteredRecordsReview);
    }
    // Batch state updates
  };
  const fetchMedicalRecord = () => {
    fetchMedicalRecordList();
  };
  const handleSelectAll = (isChecked: boolean) => {
    const updatedSelection = medicalRecordList.reduce((acc: any, record: any) => {
      acc[record?.focus?.resource?.id] = isChecked;
      return acc;
    }, {} as Record<string, boolean>);
    setSelectedRecords(updatedSelection);
  };

  const handleSelectRecord = (docRefId: string, isSelected: boolean) => {
    setSelectedRecords((prev) => ({
      ...prev,
      [docRefId]: isSelected,
    }));
  };

  useEffect(() => {
    if (action === ADD) {
      stepperModal.onOpen();
    }
  }, [action]);
  useEffect(() => {
    trackEvent(AnalyticsEventName.ScreenOpened, {
      [EventPropsNames.ScreenName]: 'View Records',
    });
  }, []);
  useEffect(() => {
    setMedicalRecordList(localMedicalRecordList);
    setMedicalRecordListActionNeeded(localMedicalRecordListActionNeeded);
    setMedicalRecordListReview(localMedicalRecordListReview);
    trackEvent(AnalyticsEventName.ScreenOpened, {
      [EventPropsNames.ScreenName]: 'View Records',
    });
  }, [localMedicalRecordList, localMedicalRecordListActionNeeded, localMedicalRecordListReview]);
  return (
    <>
      <>
        <Flex
          gap="8px"
          flexDirection={{ base: 'column', md: 'row' }}
        >
          <Suspense
            fallback={
              <>
                <PeriodSelectorSkeleton />
                <BookmarkButtonSkeleton />
              </>
            }
          >
            {!isMobile && (
              <Flex
                flexDirection="column"
                width="100%"
              >
                <MedicalRecordListFilters
                  allSelected={allSelected}
                  handleSelectAll={handleSelectAll}
                  fetchMedicalRecord={fetchMedicalRecordDocIds}
                  displayMedicalRecordList={medicalRecordList}
                  isEmptyMedicalRecordList={isEmptyMedicalRecordList}
                />
                {isMultiRecord && (
                  <Box
                    mt="12px"
                    height="1px"
                    backgroundColor="royalBlue.500"
                    opacity="0.12"
                    position="relative"
                    left="-20px"
                    width={isPublicRecordMode ? 'calc(100% + 40px)' : 'calc(100% + 336px)'}
                  />
                )}
              </Flex>
            )}
          </Suspense>
          {!isPublicRecordMode && (
            <Flex
              gap="8px"
              ml="auto"
              justifyContent={isMobile ? 'space-between' : ''}
              marginLeft={isMobile ? '' : 'auto'}
              width={isMobile ? '100%' : ''}
            >
              <Text
                fontSize="2xl"
                lineHeight="1.333"
                letterSpacing="-0.48px"
                fontFamily="P22 Mackinac"
                display={{ base: 'block', md: 'none' }}
              >
                My Records
              </Text>
              {!isEmptyMedicalRecordList && (
                <Flex gap="6px">
                  <Suspense fallback={<PeriodSelectorSkeleton />}>
                    <ShareMedicalRecordFlow selectedRecords={selectedRecords} />
                  </Suspense>

                  <Button
                    rightIcon={
                      <PlusCircleIcon
                        size={20}
                        opacity={isMobile ? '1' : '0.5'}
                      />
                    }
                    borderRadius={{ base: '100%', md: '30px' }}
                    bgColor="fluentHealth.500"
                    color="fluentHealthSecondary.500"
                    fontFamily="Apercu"
                    fontWeight="medium"
                    lineHeight="1"
                    transition="all .3s ease"
                    padding={{ base: '8px', md: '8px 16px' }}
                    minWidth={{ base: '0', md: '3rem' }}
                    iconSpacing={{ base: '0px', md: '4px' }}
                    onClick={() => {
                      recordUploadRecordEvents(trackEventInFlow, { EventName: 'UploadRecordStarted' });
                      recordAddnewRecordsEvents(trackEventInFlow, {
                        EventName: 'AddNewRecordInteracted',
                        ar_entry_point: 'Record screen top right icon',
                      });
                      navigate(`/${DOCUMENTS}/${ADD}`);
                    }}
                    _hover={{
                      iconSpacing: '8px',
                      borderColor: 'iris.500',
                      bgColor: 'royalBlue.600',
                    }}
                  >
                    {!isMobile && 'Add record'}
                  </Button>
                </Flex>
              )}
            </Flex>
          )}

          {isMobile && (
            <Flex>
              <MedicalRecordListFilters
                allSelected={allSelected}
                handleSelectAll={handleSelectAll}
                fetchMedicalRecord={fetchMedicalRecordDocIds}
                displayMedicalRecordList={medicalRecordList}
                isEmptyMedicalRecordList={isEmptyMedicalRecordList}
              />
            </Flex>
          )}
        </Flex>
        {isEmptyMedicalRecordList && <EmptyStateFilters />}
        {isEmptyMedicalRecordListForSearch && <EmptyStateWrtFilters />}
        {!isPublicRecordMode && (
          <MedicalRecordActionNeededList
            medicalRecordListActionNeeded={medicalRecordListActionNeeded}
            fetchMedicalRecord={fetchMedicalRecord}
          />
        )}
        {!isPublicRecordMode && (
          <MedicalRecordInReviewList
            medicalRecordListReview={medicalRecordListReview}
            medicalRecordListActionNeeded={medicalRecordListActionNeeded}
          />
        )}
        <Suspense fallback={<MedicalRecordListSkeleton />}>
          <SuspendedMedicalRecordList
            patientId={authenticatedUser?.id}
            handleSelectRecord={handleSelectRecord}
            selectedRecords={selectedRecords}
            fetchMedicalRecord={fetchMedicalRecord}
            medicalRecordList={medicalRecordList}
            hasNextPage={hasNextPage}
            loadingElementRef={loadingElementRef}
          />
        </Suspense>
      </>
      {stepperModal.isOpen && (
        <AddMedicalRecordFlow
          stepperModal={stepperModal}
          fetchMedicalRecord={fetchMedicalRecord}
        />
      )}
    </>
  );
}

export function MedicalRecordList() {
  const { isPublicRecordMode, isMultiRecord } = usePublicRecordSettings();

  return (
    <Container
      p="20px"
      pb={isPublicRecordMode ? '0px' : '20px'}
      pt={isPublicRecordMode && !isMultiRecord ? '0px' : '20px'}
      bg="periwinkle.100"
      borderRadius={{ base: '0px', md: '40px' }}
    >
      <Suspense fallback={<MedicalRecordListSkeleton />}>
        <MedicalRecordListAndHeader
          // currentTabName={currentTabName}
          isPublicRecordMode={isPublicRecordMode}
        />
      </Suspense>
    </Container>
  );
}
