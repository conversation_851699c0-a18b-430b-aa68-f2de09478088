import React, { PropsWithChildren, forwardRef, useEffect } from 'react';
import { RegisterOptions, useFormContext } from 'react-hook-form';
import {
  Box,
  FormControl,
  FormErrorMessage,
  FormLabel,
  Input,
  InputGroup,
  InputRightElement,
  PopoverProps,
  UseDisclosureReturn,
  useTheme,
} from '@chakra-ui/react';

import { hexOpacity } from '../../theme/utils';
import { DatePicker, DatePickerClearSelectionButton, DatePickerPopover } from '../DatePicker';

// Assets
import { ReactComponent as CalendarIcon } from '@assets/icons/calendar.svg';

export enum DATEPICKER_VARIANTS {
  FLOATING = 'floating',
  ROUNDED_TRANSPARENT = 'roundedTransparent',
}

type FieldProps = {
  variant?: DATEPICKER_VARIANTS;
  name: string;
  labelText?: string;
  errorText?: string;
  placeholderText?: string;
  rules?: RegisterOptions;
  isInvalid?: boolean;
  isDisabled?: boolean;
};

// eslint-disable-next-line @typescript-eslint/naming-convention
export const DatePickerInput = forwardRef(
  (
    {
      variant = DATEPICKER_VARIANTS.FLOATING,
      name,
      labelText = undefined,
      errorText = undefined,
      placeholderText = " ",
      rules = undefined,
      isInvalid = false,
      isDisabled = false,
    }: PropsWithChildren<FieldProps>,
    ref: React.ForwardedRef<HTMLDivElement>
  ) => {
    const theme = useTheme();

    const { register, watch, trigger } = useFormContext();
    const field = watch(name);

    // Since the date picker and the date input are not linked, we need to manually validate if the input is valied.
    useEffect(() => {
      trigger(name);
    }, [field]);

    if (variant === DATEPICKER_VARIANTS.ROUNDED_TRANSPARENT) {
      return (
        <FormControl isInvalid={isInvalid}>
          <InputGroup size="xl">
            <Input
              type="text"
              variant={variant}
              color={field.length > 0 ? 'gray.500' : hexOpacity(theme.colors.periwinkle[700], 0.6)}
              size="xl"
              sx={{
                '&::-webkit-inner-spin-button, &::-webkit-calendar-picker-indicator': {
                  display: 'none',
                  WebkitAppearance: 'none',
                },
                '&::-webkit-date-and-time-value': { textAlign: 'left' },
              }}
              isDisabled={isDisabled}
              {...(placeholderText ? { placeholder: placeholderText } : {})}
              {...register(name, rules)}
            />
            <InputRightElement>
              <CalendarIcon color={theme.colors.papaya[600]} />
            </InputRightElement>
          </InputGroup>
          <FormErrorMessage>This field is required</FormErrorMessage>
        </FormControl>
      );
    }

    return (
      <FormControl
        ref={ref}
        variant={variant}
        isInvalid={isInvalid}
      >
        <InputGroup display="block">
          <Input
            type="text"
            color={field?.length > 0 ? 'gray.500' : 'gray.300'}
            variant="flushed"
            isDisabled={isDisabled}
            sx={{
              '&::-webkit-inner-spin-button, &::-webkit-calendar-picker-indicator': {
                display: 'none',
                WebkitAppearance: 'none',
              },
              '&::-webkit-date-and-time-value': { textAlign: 'left' },
            }}
            {...(placeholderText ? { placeholder: placeholderText } : {})}
            {...register(name, rules)}
          />
          {labelText && <FormLabel>{labelText}</FormLabel>}
          {errorText && <FormErrorMessage>{errorText}</FormErrorMessage>}
          <InputRightElement>
            <CalendarIcon color={theme.colors.papaya[600]} />
          </InputRightElement>
        </InputGroup>
      </FormControl>
    );
  }
);

export function DatePickerField({
  // Field props
  variant,
  name,
  labelText = undefined,
  errorText = undefined,
  placeholderText = undefined,
  rules = undefined,
  isInvalid = false,
  isDisabled = false,
  popoverProps,

  // Datepicker props
  datePickerPopover,
  datePickerChangeHandler,
  selected,
  maxDate,
  minDate,
  excludeDates,
  datePickerClearHandler,
  onClickOutside,
  showClearDateButton = true,
  isClearDateButtonDisabled = false,
}: {
  datePickerPopover: UseDisclosureReturn;
  datePickerChangeHandler: (date: Date | null) => void;
  selected: Date | null;
  maxDate?: Date | null;
  minDate?: Date | null;
  excludeDates?: Date[];
  datePickerClearHandler?: Function;
  showClearDateButton?: boolean;
  popoverProps?: PopoverProps;
  onClickOutside?: () => void;
  isClearDateButtonDisabled?: boolean;
  isInvalid?: boolean;
} & FieldProps) {
  return (
    <DatePickerPopover
      popoverProps={popoverProps}
      datePickerPopover={datePickerPopover}
      isDisabled={isDisabled}
      onClickOutside={onClickOutside}
      popoverTriggerElement={
        <Box>
          <DatePickerInput
            variant={variant}
            name={name}
            labelText={labelText}
            errorText={errorText}
            placeholderText={placeholderText}
            rules={rules}
            isInvalid={isInvalid}
            isDisabled={isDisabled}
          />
        </Box>
      }
    >
      <DatePicker
        monthsShown={1}
        onChange={datePickerChangeHandler}
        selected={selected}
        maxDate={maxDate}
        minDate={minDate}
        excludeDates={excludeDates}
        inline
        isClearable
      />
      {showClearDateButton && (
        <DatePickerClearSelectionButton
          mt="12px"
          isDisabled={isClearDateButtonDisabled}
          onClick={() => datePickerClearHandler?.()}
        >
          Clear selection
        </DatePickerClearSelectionButton>
      )}
    </DatePickerPopover>
  );
}
