import mixpanel from 'mixpanel-browser';

import { PatientTelecom } from '@lib/models/patient';
import { extractId, generateRandomId } from '@lib/utils/utils';
import { MIXPANEL_PROJECT_TOKEN } from './constants';
import clevertap from './clevertap';
import { filterClearTapProperties, filterMixpanelProperties } from './analytics/analyticsFilters';

export enum EventPropsNames {
  FlowId = 'flow_id',
  FlowName = 'Flow Name',
  UniqueFlowID = 'Unique flow ID',
  DiagnosedDate = 'Diagnosed Date',
  LinkedTo = 'Linked to',
  NumLinkedTo = '# Linked to',
  Beholder = 'Beholder',
  Action = 'manage_action',
  // condition
  co_entry_point = 'co_entry_point',
  co_name = 'co_name',
  co_chronicity = 'co_chronicity',
  co_start_date = 'co_start_date',
  co_shared = 'co_shared',
  co_status = 'co_status',
  co_end_date = 'co_end_date',
  co_notes = 'co_notes',
  co_records_added = 'co_records_added',
  // Symptoms
  SymptomEntryPoint = 'sy_entry_point',
  SymptomName = 'sy_name',
  SymptomStartDate = 'sy_start_date',
  SymptomEndDate = 'sy_end_date',
  SymptomStatus = 'sy_status',
  SymptomLinkedRecordAdded = 'sy_record_added',
  SymptomNotes = 'sy_notes',
  // preventative screening
  Category = 'Category',
  DateOfScreening = 'Date of Screening',
  ScreeningID = 'Screening ID',
  ScreeningType = 'screening_type',
  ScreeningDate = 'screening_date',
  RecordsLinked = 'records_linked',
  FamilyHistory = 'family_history',
  HPVDiagnosed = 'hpv_diagnosed',
  SexuallyActive = 'sexually_active',
  // immunizations
  DateOfImmunization = 'Date of Immunization',
  // questionnaire
  Question = 'Question',
  Answer = 'Answer',
  // vitals
  UnitOfMeasurement = 'Unit of measurement',
  TimeOfRecording = 'Time of Recording',
  PreferredLanguage = 'Preferred Language',
  Ethnicity = 'Ethnicity',
  PhoneNumber = 'Phone Number',
  // hcp profile
  DoctorID = 'Doctor ID',
  DoctorName = 'Doctor Name',
  // Healthcare proxy
  HealthcareProxyLinkRecord = 'record_linked',
  HealthcareProxyContactID = 'Contact ID',
  HealthcareProxyContactName = 'Contact Name',
  HealthcareProxyContactRelation = 'healthcare_proxy_relationship',
  HealthcareProxyPhoneNumber = 'Phone Number',
  HealthcareProxyCompleted = 'completion_success',
  HealthcareProxyDeleted = 'healthcare_proxy_deleted',
  // health insurance
  PolicyTitle = 'Policy Title',
  // PolicyNumber = 'Policy Number',
  // ContactNumber = 'ContactNumber',
  PolicyID = 'Policy ID',
  UploadPdf = 'Upload PDF',
  // family member
  Relation = 'Relation',
  Status = 'Status',
  FirstName = 'First Name',
  LastName = 'Last Name',
  DateOfBirth = 'Date of birth',
  BloodType = 'Blood Type',
  MemberID = 'Member ID',
  // MR
  MedicalRecordID = 'Medical Record ID',
  UploadMethod = 'Upload Method',
  RecordTitle = 'Record Title',
  NumberOfFilesUploaded = 'Number of files uploaded',
  BeholderName = 'Beholder Name',
  DateOfRecord = 'Date of record',
  PhysicianName = 'Physician Name',
  PhysicianSpecialty = 'Physician Specialty',
  Notes = 'Notes',
  Tags = 'Tags',
  DateOfSampling = 'Date of sampling',
  Source = 'Source',
  SpecializationType = 'Specialization Type',
  ConditionAssociatedWith = 'Condition associated with',
  Location = 'Location',
  TypeOfLocation = 'Type of location',
  Refferral = 'Refferral',
  // Login
  UserID = 'User ID',
  // Dashboard
  ClickedElement = 'Clicked Element',
  ClickedItem = 'Clicked Item',
  // view chart
  TrendChartTimeFrameView = 'Trend Chart Time Frame View',
  // add reminder
  Message = 'Message',
  AllDay = 'All Day',
  Time = 'Time',
  Frequency = 'Frequency',
  CustomFrequency = 'Custom Frequency',
  CustomFrequencyCalendarDate = 'Custom Frequency calendar date',
  // ReminderDate = 'Reminder Date',
  // book offline
  Name = 'Name',
  Email = 'Email',
  // universal events
  MedicalInformationType = 'Medical Information Type',
  MedicalInformationName = 'Medical Information Name',
  // share medical record
  ShareType = 'Share type',
  FiltersSortBy = 'Filters Sort By',
  FiltersRecordType = 'Filters Record Type',
  FiltersTags = 'Filters Tags',
  SetExpiryDate = 'Set Expiry Date',
  EntryPoint = 'entry_point',
  ScreenName = 'screen_name',

  // Share Profile Events
  ShareProfileStarted = 'share_profile_started',
  ShareProfileCompleted = 'share_profile_completed',
  GenerateLinkButtonClicked = 'generate_link_button_clicked',
  DownloadProfileButtonClicked = 'download_profile_button_clicked',
  DownloadProfileSectionDownloaded = 'profile_sections_downloaded',
  AllConditionsDownloaded = 'all_conditions_downloaded',
  ProfileSectionsShared = 'profile_sections_shared',
  AllConditionsShared = 'all_conditions_shared',
  CopyLinkButtonClicked = 'copy_link_button_clicked',
  LinkActiveDuration = 'link_active_duration',

  // allergies
  AllergyEntryPoint = 'al_entry_point',
  AllergyType = 'al_type',
  AllergyName = 'al_name',
  AllergyCriticality = 'al_criticality',
  AllergyDiagnosedDate = 'al_date',
  AllergyRecordsAdded = 'al_records_added',
  AllergyStatus = 'al_status',
  AllergyEndDate = 'al_end_date',
  AllergyNotes = 'al_notes',

  // Reproductive Health
  'reproductive-health-sexually-active' = 'sexually_active',
  'reproductive-health-ever-been-pregnant' = 'pregnancy_history',
  'reproductive-health-terminated-pregnancy' = 'terminated_pregnancy',
  'reproductive-health-do-you-have-children' = 'have_children',
  'reproductive-health-you-ever-miscarried' = 'miscarried',
  'reproductive-health-you-undergone-fertility-treatments' = 'fertility_treatment',
  'reproductive-health-current-pregnancy-status' = 'pregnancy_status',
  'reproductive-health-sex-assigned-at-birth' = 'sex_at_birth',
  'reproductive-health-last-menstrual-period-date' = 'last_menstrual_date',

  // Lifestyle Nutrition
  LifestyleNutritionType = 'lifestyle_nutrition_type',
  // Exercise
  'lifestyle-nutrition-exercise-activities-you-take-part' = 'exercise_activity',
  'lifestyle-nutrition-exercise-in-week' = 'exercise_frequency_per_week',
  'lifestyle-nutrition-exercise-how-many-days-per-week-do-you-get' = 'exercise_frequency_min60',
  'lifestyle-nutrition-exercise-your-fitness-routine' = 'exercise_routine_type',
  // Diet
  'lifestyle-nutrition-diet-you-describe-your-diet' = 'diet',
  'lifestyle-nutrition-diet-any-other-diet-preferences-restrictions' = 'diet_preference',
  // Alcohol/Tobacco/Caffeine
  'lifestyle-nutrition-alcohol-tobacco-caffeine-alcoholic-drinks-per-week' = 'alcoholic_consumption_frequency',
  'lifestyle-nutrition-alcohol-tobacco-caffeine-smoke-tobacco' = 'smoke_tobacco',
  'lifestyle-nutrition-alcohol-tobacco-caffeine-caffeinated-beverages-per-day' = 'caffeine_frequency',
  // Mental Health Condition
  'lifestyle-nutrition-mental-health-sleep-personal-history-mental-health-conditions' = 'mental_condition_history',
  'lifestyle-nutrition-mental-health-sleep-family-history-mental-health-conditions' = 'fam_hist_mental_health',
  'lifestyle-nutrition-mental-health-sleep-feel-burned-out' = 'burnt_out_frequency',
  'lifestyle-nutrition-mental-health-sleep-hours-do-you-sleep' = 'sleep_hours',
  'lifestyle-nutrition-mental-health-sleep-generally-feel-you-wake-up' = 'wakeup_feel',
  'lifestyle-nutrition-mental-health-sleep-average-level-stress' = 'stress_level',
  // Occupation
  'lifestyle-nutrition-occupation-your-occupation' = 'occupation',
  'lifestyle-nutrition-occupation-required-extended-periods' = 'sitting_hours',

  // medical records

  // Insurance
  InsuranceProvider = 'insurance_provider',
  PolicyNumber = 'policy_number',
  ContactNumber = 'contact_number',
  FileUpload = 'file_upload',

  // Filters
  FilterType = 'type_of_filter',
  FilterValue = 'filter_value',
  FilterClear = 'clear_filters',
  ActionType = 'action_type',
  MedicationsName = 'medication_name',
  Dosage = 'dosage',
  CurrentStatus = 'current_status',
  MedicationsFrequency = 'frequency',
  BrandName = 'brand_name',
  DoctorPrescribed = 'doctor_prescribed',
  Type = 'type',
  LoginType = 'login_type',
  OTPCorrect = 'otp_correct',
  PINErrorCount = 'pin_error_count',
  ForgotPINClicked = 'forgot_pin_clicked',
  // Profile Photo events
  UploadSizeError = 'upload_size_error',
  // Medical Records
  TabName = 'tab_name',
  SubTabName = 'sub_tab_name',
  TypeOfRecord = 'type_of_record',
  ShowMoreClicked = 'show_more_clicked',
  Bookmarked = 'bookmarked',
  TagCount = 'tag_count',
  MedicalRecordType = 'record_type',
  RecordOwner = 'record_owner',
  AddMoreFilesInteractionCount = 'add_more_files_interaction_count',
  FileUploadCount = 'file_upload_count',
  RemoveFileInteractionCount = 'remove_file_interaction_count',
  FileSizeErrorCount = 'file_size_error_count',
  RecordNotes = 'record_notes',
  Count = 'count',
  SetExpiry = 'set_expiry',
  LinkExpiry = 'link_expiry',
  // Consent Management events
  ConsentModalContinueButtonClicked = 'consent_model_continue_button_clicked',
  ConsentExplainerContinueButtonClicked = 'consent_explainer_continue_button_clicked',
  ConsentFirstName = 'first_name',
  ConsentLastName = 'last_name',
  ConsentDateOfBirth = 'dob',
  ConsentLivingStatus = 'living_status',
  ConsentEmail = 'consentee_email',
  ConsentMobile = 'consentee_mobile_number',
  ConsentRequestId = 'consent_request_id',
  ConsentDeleted = 'consent_deleted',
  // Upload Record
  UploadRecordUser = 'ur_user',
  UploadRecordType = 'ur_type',
  UploadRecordVisitDate = 'ur_visit_date',
  UploadRecordDoctorName = 'ur_doctor_name',
  UploadRecordNotes = 'ur_notes',
  UploadRecordFileSize = 'ur_file_size',
  UploadRecordEncryption = 'ur_encryption',
  UploadRecordDocumentId = 'document_id',
  // Record Bookmark
  RecordBookmarkType = 'rb_record_type',
  // Edit MedicalRecord
  EditMedicalRecordType = 're_record_type',
  EditMedicalRecordFields = 're_edited_fields',
  // EditUploadRecordType = 're_type',
  // EditUploadRecordVisitDate = 're_visit_date',
  // EditUploadRecordDoctorName = 're_doctor_name',
  // EditUploadRecordNotes = 're_notes',

  // Consent Management events

  // Old to Be cleanup Enum
  CompletedSuccess = 'completion_success',
  ConditionDeleted = 'condition_deleted',
  RecordSearched = 'record_searched',
  LinkedRecordCount = '',

  // Health record Interaction
  RecordTabName = 'hrh_tab_name',
  RecordSearchedTerm = 'hrh_searched_term',
  HealthRecordType = 'ri_record_type',
  ReasonOfRejection = 'reason_of_rejection',
  ProcessingRecordType = 'pri_record_type',
  // Health Record Download
  RecordMedicalType = 'rd_record_type',
  RecordMedicalOwner = 'rd_record_owner',
  // Health Shared Medical Record
  SharedMedicalRecordType = 'rs_record_type',
  SharedMedicalRecordOwner = 'rs_record_owner',
  SharedMedicalRecordPlatform = 'rs_platform',
  // Health Record Filter
  MedicalRecordFilterType = 'filter_type',
  MedicalRecordFilterValue = 'filter_value',
  // Medication Flow
  MedicationEntryPoint = 'me_entry_point',
  MedicationType = 'me_type',
  MedicationName = 'me_name',
  MedicationStatus = 'me_status',
  MedicationDosage = 'me_dosage',
  MedicationFrequency = 'me_frequency',
  MedicationAdministered = 'me_administered',
  MedicationRecordsAdded = 'me_record_added',
  MedicationEditedFields = 'me_edited_fields',
  MedicationStartDate = 'me_start_date',
  MedicationEndDate = 'me_end_date',
  MedicationDosageMorning = 'me_dosage_morning',
  MedicationDosageAfternoon = 'me_dosage_afternoon',
  MedicationDosageEvening = 'me_dosage_evening',
  MedicationDosageNight = 'me_dosage_night',
  MedicationDosageDuration = 'me_dosage_duration',
  MedicationFood = 'me_food',
  MedicationNotes = 'me_notes',

  // Supplement Flow
  SupplementEntryPoint = 'su_entry_point',
  SupplementName = 'su_name',
  SupplementStartDate = 'su_start_date',
  SupplementStatus = 'su_status',
  SupplementEndDate = 'su_end_date',
  SupplementDosageMorning = 'su_dosage_morning',
  SupplementDosageAfternoon = 'su_dosage_afternoon',
  SupplementDosageEvening = 'su_dosage_evening',
  SupplementDosageNight = 'su_dosage_night',
  SupplementDosageDuration = 'su_dosage_duration',
  SupplementFood = 'su_food',
  SupplementDosage = 'su_dosage',
  SupplementFrequency = 'su_frequency',
  SupplementAdministered = 'su_administered',
  SupplementPrescribed = 'su_prescribed',
  SupplementRecordsAdded = 'su_record_added',
  SupplementNotes = 'su_notes',
  // Settings Flow
  SettingsSection = 'st_settings_section',
  SettingsObjective = 'st_settings_objective',
  SettingsWeightMeasurementUnit = 'st_weight_measurement_unit',
  SettingsHeightMeasurementUnit = 'st_height_measurement_unit',
  SettingsPushNotificationsPreference = 'st_push_notifications_preference',
  SettingsEmailPreference = 'st_email_preference',
  SettingsWhatsAppPreference = 'st_whatsapp_preference',
  SettingsConsentSectionName = 'st_consent_section_name',
  SettingsConsentStatus = 'st_consent_status',
  SettingsPolicyName = 'st_policy_name',
  SettingsFAQ = 'st_faq',
  SettingsSupportType = 'st_support_type',
  SettingsSocialMediaPlatform = 'st_follow_us_platform',

  // Profile Flow
  ProfileTabName = 'pi_tab_name',
  ProfileEditedFields = 'pi_edited_fields',
  ProfileMyHealthProfile = 'pi_my_health_profile',
  HealthcareProxyFirstName = 'hp_first_name',
  HealthcareProxyLastName = 'hp_last_name',
  HealthcareProxyRelationship = 'hp_relationship',
  HealthcareProxyMobileNumber = 'hp_mobile_number',
  HealthcareProxyUploadFile = 'hp_record_added',
  // Health Insurance Flow
  HealthInsurancePolicyProvider = 'hi_policy_provider',
  HealthInsuranceNumber = 'hi_insurance_number',
  HealthInsuranceMobile = 'hi_mobile_number',
  HealthInsuranceEditedFields = 'hi_edited_fields',
  HealthInsuranceRecordAdded = 'hi_record_added',
  // Emergency Contact Flow
  EmergencyContactFirstName = 'ec_first_name',
  EmergencyContactLastName = 'ec_last_name',
  EmergencyContactRelationship = 'ec_relationship',
  EmergencyContactMobileNumber = 'ec_mobile_number',
  EmergencyContactEditedFields = 'ec_edited_fields',
  //
  ProfilePictureAction = 'pp_action',
  ProfilePictureUploadError = 'pp_upload_error',
  // Add Record RI FLow
  AddNewRecordEntryPoint = 'ar_entry_point',
  // Family MemberHistory Flow
  FamilyMemberHistoryRelationship = 'fm_relationship',
  FamilyMemberHistoryBirthDate = 'fm_birthdate',
  FamilyMemberHistoryStatus = 'fm_status',
  FamilyMemberHistoryBloodGroup = 'fm_blood_group',
  FamilyMemberHistoryEthnicity = 'fm_ethnicity',
  FamilyMemberHistoryCondition = 'fm_condition',
  FamilyMemberHistoryStoreHealthRecords = 'fm_store_health_records',
  FamilyMemberHistoryBookOnlineConsults = 'fm_book_online_consults',
  FamilyMemberHistoryShareHealthInformation = 'fm_share_health_information',
  FamilyMemberHistoryKeepTrackOfHealthInfo = 'fm_keep_track_of_health_info',
  FamilyMemberHistoryOther = 'fm_other',
  FamilyMemberHistoryEntryPoint = 'fm_entry_point',
  FamilyMemberHistoryEditedFields = 'fm_edited_fields',
  // Consent Manager Flow
  ConsentManagerRelationship = 'cm_relationship',
  ConsentManagerStoreHealthRecords = 'cm_store_health_records',
  ConsentManagerBookOnlineConsults = 'cm_book_online_consults',
  ConsentManagerShareHealthInformation = 'cm_share_health_information',
  ConsentManagerKeepTrackOfHealthInfo = 'cm_keep_track_of_health_info',
  ConsentManagerOther = 'cm_other',
  ConsentManagerEditedFields = 'cm_edited_fields',
  // My CareTeam Flow
  CareTeamFirstName = 'ct_first_name',
  CareTeamLastName = 'ct_last_name',
  CareTeamSpeciality = 'ct_speciality',
  CareTeamPhoneNumber = 'ct_phone_number',
  CareTeamAltPhoneNumber = 'ct_alt_phone_number',
  CareTeamEmail = 'ct_email',
  CareTeamCity = 'ct_city',
  PrimaryDoctor = 'primary_doctor',

  // Vital Flow
  vitalType = 'vt_type',
  vitalState = 'vt_state',
  // Blood Pressure (BP) properties
  bpEntryPoint = 'vt_bp_entry_point',
  bpSystolic = 'vt_bp_systolic',
  bpDiastolic = 'vt_bp_diastolic',
  bpDate = 'vt_bp_date',
  bpTime = 'vt_bp_time',
  bpRecorder = 'vt_bp_recorder',
  bpPosition = 'vt_bp_position',
  // Body Temperature (BT) properties
  btEntryPoint = 'vt_bt_entry_point',
  btTemperature = 'vt_bt_temparature',
  btDate = 'vt_bt_date',
  btTime = 'vt_bt_time',
  // Pulse Rate (PR) properties
  prEntryPoint = 'vt_pr_entry_point',
  prRate = 'vt_pr_rate',
  prDate = 'vt_pr_date',
  prTime = 'vt_pr_time',
  // Oxygen Saturation Level (OSL) properties
  oslEntryPoint = 'vt_osl_entry_point',
  oslLevel = 'vt_osl_level',
  oslDate = 'vt_osl_date',
  oslTime = 'vt_osl_time',
  // Respiratory Rate (RR) properties
  rrEntryPoint = 'vt_rr_entry_point',
  rrRate = 'vt_rr_rate',
  rrDate = 'vt_rr_date',
  rrTime = 'vt_rr_time',
  // Surgery properties
  SurgeryEntryPoint = 'sur_entry_point',
  SurgeryType = 'sur_type',
  SurgeryStatus = 'sur_status',
  SurgeryDate = 'sur_date',
  SurgeryNotes = 'sur_notes',
  SurgeryRecordsAdded = 'sur_records_added',
  // Preventative Screening
  PreventativeScreeningEntryPoint = 'ps_entry_points',
  PreventativeScreeningType = 'ps_type',
  PreventativeScreeningFamilyHistory = 'ps_family_history',
  PreventativeScreeningHPV = 'ps_hpv',
  PreventativeScreeningSexuallyActive = 'ps_sexually_active',
  PreventativeScreeningDate = 'ps_date',
  PreventativeScreeningNotes = 'ps_notes',
  PreventativeScreeningRecordsAdded = 'ps_records_added',
  // Vaccine
  VaccineEntryPoint = 'vc_entry_point',
  VaccineType = 'vc_type',
  VaccineDate = 'vc_date',
  VaccineFullyVaccinated = 'vc_fully_vaccinated',
  VaccineDoseCount = 'vc_dose_count',
  VaccineRecordsAdded = 'vc_records_added',
  // LifeStyle and Nutrition
  LNExerciseEntryPoint = 'ln_exercise_entry_point',
  LNExerciseActivity = 'ln_exercise_activity',
  LNExerciseFrequency = 'ln_exercise_frequency',
  LNIntenseExercise = 'ln_intense_exercise',
  LNFitnessRoutine = 'ln_fitness_routine',
  LNDietEntryPoint = 'ln_diet_entry_point',
  LNDietType = 'ln_diet_type',
  LNDietPreference = 'ln_diet_preference',
  LNATCEntryPoint = 'ln_atc_entry_point',
  LNATCAlcohol = 'ln_atc_alcohol',
  LNATCTobacco = 'ln_atc_tobacco',
  LNATCCaffeine = 'ln_atc_caffeine',
  LNMHSEntryPoint = 'ln_mhs_entry_point',
  LNMHSMentalCondition = 'ln_mhs_mental_condition',
  LNMHSFamilyHistory = 'ln_mhs_family_history',
  LNMHSBurntOut = 'ln_mhs_burnt_out',
  LNMHSSleepDuration = 'ln_mhs_sleep_duration',
  LNMHSMorningFeel = 'ln_mhs_morning_feel',
  LNMHSStressLevel = 'ln_mhs_stress_level',
  LNOccupationEntryPoint = 'ln_occupation_entry_point',
  LNOccupation = 'ln_occupation',
  LNOccupationExtendedHours = 'ln_occupation_extended_hours',
  // Additional Medical Decision Maker
  AMDMFirstName = 'amdm_first_name',
  AMDMLastName = 'amdm_last_name',
  AMDMRelationship = 'amdm_relationship',
  AMDMMobileNumber = 'amdm_mobile_number',
  AMDMRecordAdded = 'amdm_record_added',
  // Family Member Condition properties
  FamilyMemberID = 'fm_id',
  FamilyMemberConditionName = 'fm_co_name',
  FamilyMemberConditionChronicity = 'fm_co_chronicity',
  FamilyMemberConditionDate = 'fm_co_date',
  FamilyMemberConditionShared = 'fm_co_shared',
  FamilyMemberConditionStatus = 'fm_co_status',
  FamilyMemberConditionEndDate = 'fm_co_end_date',
  FamilyMemberConditionNotes = 'fm_co_notes',
  FamilyMemberConditionRecordsAdded = 'fm_co_records_added',
  // Reproductive Health properties
  ReproductiveHealthEntryPoint = 'rh_entry_point',
  ReproductiveHealthSexuallyActive = 'rh_sexually_active',
  ReproductiveHealthLastMenstrualDate = 'rh_last_menstrual_date',
  ReproductiveHealthPregnancyHistory = 'rh_pregnancy_history',
  ReproductiveHealthPregnancyTerminated = 'rh_pregnancy_terminated',
  ReproductiveHealthChildren = 'rh_children',
  ReproductiveHealthMiscarriage = 'rh_miscarriage',
  ReproductiveHealthFertilityTreatments = 'rh_fertility_treatments',
  ReproductiveHealthPregnancyStatus = 'rh_pregnancy_status',
  // Health Profile Basic Info
  height = 'height',
  weight = 'weight',
  bloodgroup = 'bloodgroup',
  sab = 'sab',
  ethnicity = 'ethnicity',
  gender = 'gender',
  language = 'language',
  ReminderEntryPoint = 'rm_entry_point',
  ReminderName = 'rm_name',
  ReminderDate = 'rm_date',
  ReminderAllDay = 'rm_all_day',
  ReminderTime = 'rm_time',
  ReminderFrequency = 'rm_frequency',
  ReminderNotes = 'rm_notes',
}

export enum EventShareTypes {
  RECORD = 'Record',
  TIMELINE = 'Timeline',
}
export enum EventLocationNames {
  INDIA = 'India',
}
export enum EventFlowNames {
  AddMedicalRecord = 'Add Medical Record',
  EditMedicalRecord = 'Edit Medical Record',
  ProfileGeneralInfo = 'Profile General Info',
  SignIn = 'Sign In',
  Dashboard = 'Dashboard',
  MedicalRecords = 'Medical Records',
  Navbar = 'Navbar',
  BookAssistance = 'Book Assistance',
  FamilyHistory = 'Family History',
  Profile = 'Profile',
  MyRecords = 'My Records',
  OthersRecords = "Other's Records",
  DownloadRecordClicked = 'Records Downloaded',
}

export enum EventItemText {
  BookAssistance = 'Book Assistance Now',
  ProfileCompletion = 'Profile Completion',
  RecordsInReview = 'Records in Review',
  Reminders = 'Reminders',
  RequestAssistance = 'Request assistance',
  BackToHome = 'Back to home',
}
export enum EventFlowActions {
  AddNewRecord = 'Add new Record',
  CreateReminder = 'Create Reminder',
  X = 'X',
  SeeRecordsInReview = 'See Records in Review',
  Edit = 'Edit',
  Add = 'Add',
}
export enum EventClickedElement {
  Button = 'button',
  Link = 'link',
  CTA = 'CTA',
}
export enum MedicalInformationType {
  Condition = 'Condition',
}
export enum PatientPropsNames {
  UserId = 'userId',
  Email = 'email',
  Age = 'age',
  Gender = 'gender',
  Height = 'height',
  Weight = 'weight',
  MeasurementSystem = 'measurement_system',
  RegistrationDate = 'registration_date',
  ProfileCompletionPercentage = 'profile_completion_percentage',
  PhoneNumber = 'phone_number',
  HasFamilyMembers = 'has_family_members',
  PreferredLanguage = 'preferred_language',
  Ethnicity = 'ethnicity',
}

export enum AnalyticsEventName {
  // EMR
  AddMedicalRecordStarted = 'Add medical record started',
  AddMedicalRecordUploadMethod = 'Add Medical Record Upload Method',
  AddMedicalRecordDateOfSampling = 'Add Medical Record Date of Sampling',
  AddMedicalRecordPhysician = 'Add Medical Record Physician',
  AddMedicalRecordNotes = 'Add Medical Record Notes',
  AddMedicalRecordType = 'Add Medical Record Type',
  AddMedicalRecordDocument = 'Add Medical Record Document',
  AddMedicalRecordTitle = 'Add Medical Record Title',
  AddMedicalRecordCompleted = 'Add medical record Completed',
  ViewMedicalRecord = 'View Medical Record',
  ManageBookmarkForMedicalRecords = 'Manage Bookmarks for Medical Records',
  // My Health Profile
  // PreventativeScreening
  ManagePreventativeScreeningStarted = 'Manage Preventative Screening Started',
  ManagePreventativeScreeningCompleted = 'Manage Preventative Screening Completed',
  PreventativeScreeningManaged = 'Preventative Screenings Managed',
  // Procedures
  ManageSurgeriesAndProceduresStarted = 'Manage Surgeries and Procedures Started',
  ManageSurgeriesAndProceduresCompleted = 'Manage Surgeries and Procedures Completed',
  SurgeriesManaged = 'Surgeries Managed',
  SurgeriesDeleted = 'Surgeries Deleted',

  // Allergies and Intolerances
  ManageAllergiesAndIntolerancesStarted = 'Manage Allergies and Intolerances Started',
  ManageAllergiesAndIntolerancesCompleted = 'Manage Allergies and Intolerances Completed',
  AllergyManaged = 'Allergy Managed',
  AllergyDeleted = 'Allergy Deleted',

  // Reproductive Health Managed
  ReproductiveHealthManaged = 'Reproductive Health Managed',

  // Lifestyle Nutrition Managed
  LifestyleNutritionManaged = 'Lifestyle Nutrition Managed',

  // Medications
  ManageMedicationsStarted = 'Manage Medications Started',
  ManageMedicationsCompleted = 'Manage Medications Completed',
  MedicationsAndSupplementsManaged = 'Medications And Supplements Managed',
  // Immunizations
  ManageImmunizationStarted = 'Manage Immunization Started',
  ManageImmunizationCompleted = 'Manage Immunization Completed',
  // Chart Viewed
  ChartViewed = 'Chart Viewed',
  // Questionnaire
  ManageMyHealthProfileQuestionnaire = 'Manage My Health Profile Questionnaire',
  // Emergency Contact
  ManageEmergencyContactStarted = 'Emergency Contact Started',
  ManageEmergencyContactCompleted = 'Emergency Contact Completed',
  ManageEmergencyContactDeleted = 'Emergency Contact Deleted',
  ManageEmergencyContact = 'Emergency Contact Managed',
  // Hcp
  InviteHCPtoCareTeam = 'Invite HCP to Care Team',
  RemoveFromCareTeam = 'Remove From Care Team',

  InteractWithDoctorsDetails = "Interact with Doctor's Details",
  // Healthcare proxy
  ManageHealthcareProxyStarted = 'Healthcare Proxy Started',
  ManageHealthcareProxyCompleted = 'Healthcare Proxy Completed',
  ManageHealthcareProxyDeleted = 'Healthcare Proxy Deleted',
  ManageHealthcareProxy = 'Healthcare Proxy Managed',
  // Health Insurance
  ManageHealthInsuranceStarted = 'Manage Health Insurance Started',
  ManageHealthInsuranceCompleted = 'Manage Health Insurance Completed',
  ManageHealthInsuranceDeleted = 'Health Insurance Deleted',
  // Family History
  ManageFamilyMemberStarted = 'Manage Family Member Started',
  ManageFamilyMemberCompleted = 'Manage Family Member Completed',
  // General Info
  ManageProfileSpecificInfoStarted = 'Manage Profile Specific Info Started',
  ManageProfileSpecificInfoCompleted = 'Manage Profile Specific Info Completed',
  // Sign In
  SignInPhoneNumberChanged = 'Sign In Phone Number Changed',
  OTPValidated = 'OTP Validated',
  OTPResend = 'OTP Resend',
  SignInCompleted = 'Sign In Completed',
  // Dashboard  Home
  DashboardItemInteracted = 'Dashboard Item Interacted',
  // Book Offline
  BookOfflineStarted = 'Book Offline Started',
  BookOfflineCompleted = 'Book Offline Completed',
  // Universal Events
  HideFromSharingAdded = 'Hide from Sharing Added',
  // Completion Action
  CompletionAction = 'Completion Action',
  // Add reminder
  ManageReminderStarted = 'Manage Reminder Started',
  ManageReminderCompleted = 'Manage Reminder Completed',
  // Share Medical Records
  ShareMedicalRecordsStarted = 'Share Medical Records Started',
  ShareMedicalRecordsCompleted = 'Share Medical Records Completed',
  // Share Record
  ShareRecordStarted = 'Share Record Started',
  ShareRecordCompleted = 'Share Record Completed',
  // Misc
  CrossButtonClicked = 'Cross Button Clicked',
  InformationButtonClicked = 'Information Button Clicked',

  HeightManaged = 'Height Managed',
  WeightManaged = 'Weight Managed',
  AgeManaged = 'Age Managed',
  BloodGroupManaged = 'Blood Group Managed',
  GenderManaged = 'Gender Managed',
  SexAssignedAtBirthManaged = 'Sex Assigned at Birth Managed',
  EthnicityManaged = 'Ethnicity Managed',
  PreferredLanguageManaged = 'Preferred Language Managed',
  // Medical Record Filter
  RecordsFilter = 'Records Filtered',
  VaccinesManaged = 'Vaccines Managed',
  HealthInsuranceManaged = 'Health Insurance Managed',
  DownloadRecordClicked = 'Records Downloaded',
  // Share Profile
  ShareProfileStarted = 'Share Profile Started',
  ShareProfileCompleted = 'Share Profile Completed',

  ShareProfileClicked = 'Share Profile Clicked',
  ShareProfileLinkGenerated = 'Share Profile Link Generated',
  ShareRecordLinkGenerated = 'Share Record Link Generated',
  DownloadProfileClicked = 'Download Profile Started',
  DownloadProfileCompleted = 'Download Profile Completed',
  // Manage Profile Picture
  ProfilePictureManaged = 'Profile Picture Managed',
  // Medical Records
  RecordsListViewed = 'Records List Viewed',
  RecordsInteracted = 'Records Interacted',
  RecordsClosed = 'Records Closed',
  NewRecordUploaded = 'New Record Uploaded',
  RecordsFiltered = 'Records Filtered',
  RecordsShared = 'Records Shared',
  ConsentInitiated = 'Consent Initiated',
  ConsentDeleted = 'Consent Deleted',
  // Related Person Manage Condition
  RelatedPersonManageConditionStarted = 'Related Person Manage Condition Started',
  RelatedPersonManageConditionCompleted = 'Related Person Manage Condition Completed',
  RelatedPersonConditionManaged = 'Related Person Condition Managed',
  RelatedPersonConditionDeleted = 'Related Person Condition Deleted',
  // Login
  LoginMobileNumberEntered = 'Login Mobile Number Entered',
  LoginMobileNumberError = 'Login Mobile Number Error',
  LoginStarted = 'Login Started',
  PINError1 = 'Login PIN Error 1 ',
  PINError2 = 'Login PIN Error 2',
  PINError3 = 'Login PIN Error 3',
  LoginAccountLocked = 'Login Account Locked',
  LoginMPINEntered = 'Login MPIN Entered',
  Login2FAOTPError = 'Login 2FA OTP Error',
  Login2FAOTPExpired = 'Login 2FA OTP Expired',
  Login2FAOTPResendClicked = 'Login 2FA OTP Resend Clicked',
  PINResetStarted = 'Login PIN Reset Started',
  OTPEntered = 'Login OTP Entered',
  OTPEnteredError = 'Login OTP Entered Error',
  OTPExpiredError = 'Login OTP Expired Error',
  ResendOTPClicked = 'Login Resend OTP Clicked',
  PINResetCompleted = 'Login PIN Reset Completed',
  LoginCompleted = 'Login Completed',
  // Conditions
  ConditionAddStarted = 'Condition Add Started',
  ConditionAddInProgName = 'Condition Add In Prog Name',
  ConditionAddInProgChronicity = 'Condition Add In Prog Chronicity',
  ConditionAddInProgStartDate = 'Condition Add In Prog Start Date',
  ConditionAddInProgShared = 'Condition Add In Prog Shared',
  ConditionAddInProgStatus = 'Condition Add In Prog Status',
  ConditionAddInProgEndDate = 'Condition Add In Prog End Date',
  ConditionAddInProgNotes = 'Condition Add In Prog Notes',
  ConditionAddInProgRecordsAdded = 'Condition Add In Prog Records Added',
  ConditionAddCompleted = 'Condition Add Completed',
  ConditionInteracted = 'Condition Interacted',
  ConditionEdited = 'Condition Edited',
  ConditionRemoved = 'Condition Removed',
  //  Symptoms
  SymptomsAddStarted = 'Symptoms Add Started',
  SymptomsAddInProgName = 'Symptoms Add In Prog Name',
  SymptomsAddInProgStartDate = 'Symptoms Add In Prog Start Date',
  SymptomsAddInProgStatus = 'Symptoms Add In Prog Status',
  SymptomsAddInProgEndDate = 'Symptoms Add In Prog End Date',
  SymptomsAddInProgShared = 'Symptoms Add In Prog Shared',
  SymptomsAddInProgRecordsAdded = 'Symptoms Add In Prog Records Added',
  SymptomsAddInProgNotes = 'Symptoms Add in Prog Notes',
  SymptomsAddCompleted = 'Symptoms Add Completed',
  SymptomsInteracted = 'Symptoms Interacted',
  SymptomsEdited = 'Symptoms Edited',
  SymptomsRemoved = 'Symptoms Removed',
  // Upload Records
  UploadRecordStarted = 'Upload Record Started',
  UploadRecordInProgUserSelected = 'Upload Record In Prog User Selected',
  UploadRecordInProgSuccessful = 'Upload Record In Prog Successful',
  UploadRecordInProgTypeSelected = 'Upload Record In Prog Type Selected',
  UploadRecordInProgNameAdded = 'Upload Record In Prog Name Added',
  UploadRecordInProgVisitDateAdded = 'Upload Record In Prog Visit Date Added',
  UploadRecordInProgDoctorNameAdded = 'Upload Record In Prog Doctor Name Added',
  UploadRecordInProgNotesAdded = 'Upload Record In Prog Notes Added',
  UploadRecordCompleted = 'Upload Record Completed',
  UploadRecordExited = 'Upload Record Exited',
  // Record Bookmark
  RecordBookmarked = 'Record Bookmarked',
  RecordUnbookmarked = 'Record Unbookmarked',
  // Edit MedicalRecord
  EditRecordStarted = 'Edit Record Started',
  EditRecordCompleted = 'Edit Record Completed',
  RecordRemoved = 'Record Removed',
  // Health Record Interaction
  HealthRecordHubInteracted = 'Health Record Hub Interacted',
  HealthRecordHubSearched = 'Health Record Hub Searched',
  RecordInteracted = 'Record Interacted',
  ApprovedRecordInteracted = 'Approved Record Interacted',
  RejectedRecordInteracted = 'Rejected Record Interacted',
  RejectedRecordReuploadInteracted = 'Rejected Record Re-upload Interacted',
  RejectedRecordConsentInteracted = 'Rejected Record Consent Interacted',
  ProcessingRecordInteracted = 'Processing Record Interacted',
  // Record Download
  RecordDownloaded = 'Record Downloaded',
  // Shared Medical Record
  RecordShared = 'Record Shared',
  // Medical Record Filters
  RecordsFilteredApplied = 'Records Filtered Applied',
  RecordsFilterRemoved = 'Records Filter Removed',
  // Medication Flow
  MedicationsAddStarted = 'Medications Add Started',
  MedicationsAddInProgType = 'Medications Add In Prog Type',
  MedicationsAddInProgName = 'Medications Add In Prog Name',
  MedicationsAddInProgStatus = 'Medications Add In Prog Status',
  MedicationsAddInProgDosage = 'Medications Add In Prog Dosage',
  MedicationsAddInProgFrequency = 'Medications Add In Prog Frequency',
  MedicationsAddInProgAdministered = 'Medications Add In Prog Administered',
  MedicationsAddInProgRecordsAdded = 'Medications Add In Prog Records Added',
  MedicationsAddCompleted = 'Medications Add Completed',
  MedicationsInteracted = 'Medications Interacted',
  MedicationsEdited = 'Medications Edited',
  MedicationsRemoved = 'Medications Removed',
  MedicationsAddInProgStartDate = 'Medications Add In Prog Start Date',
  MedicationsAddInProgEndDate = 'Medications Add In Prog End Date',
  MedicationsAddInProgNotes = 'Medications Add In Prog Notes',
  MedicationsAddInProgFood = 'Medications Add In Prog Food',
  MedicationsAddInProgScheduleMorning = 'Medications Add In Prog Schedule Morning',
  MedicationsAddInProgScheduleAfternoon = 'Medications Add In Prog Schedule Afternoon',
  MedicationsAddInProgScheduleEvening = 'Medications Add In Prog Schedule Evening',
  MedicationsAddInProgScheduleNight = 'Medications Add In Prog Schedule Night',
  MedicationsAddInProgScheduleDuration = 'Medications Add In Prog Schedule Duration',
  MedicationsAddInProgScheduleFood = 'Medications Add In Prog Schedule Food',
  // Supplement Flow
  SupplementsAddStarted = 'Supplements Add Started',
  SupplementsAddInProgName = 'Supplements Add In Prog Name',
  SupplementsAddInProgStartDate = 'Supplements Add In Prog Start Date',
  SupplementsAddInProgStatus = 'Supplements Add In Prog Status',
  SupplementsAddInProgEndDate = 'Supplements Add In Prog End Date',
  SupplementsAddInProgScheduleMorning = 'Supplements Add In Prog Schedule Morning',
  SupplementsAddInProgScheduleAfternoon = 'Supplements Add In Prog Schedule Afternoon',
  SupplementsAddInProgScheduleEvening = 'Supplements Add In Prog Schedule Evening',
  SupplementsAddInProgScheduleNight = 'Supplements Add In Prog Schedule Night',
  SupplementsAddInProgScheduleDuration = 'Supplements Add In Prog Schedule Duration',
  SupplementsAddInProgScheduleFood = 'Supplements Add In Prog Schedule Food',
  SupplementsAddInProgDosage = 'Supplements Add In Prog Dosage',
  SupplementsAddInProgFrequency = 'Supplements Add In Prog Frequency',
  SupplementsAddInProgAdministered = 'Supplements Add In Prog Administered',
  SupplementsAddInProgPrescribed = 'Supplements Add In Prog Prescribed',
  SupplementsAddInProgRecordsAdded = 'Supplements Add In Prog Records Added',
  SupplementsAddInProgNotes = 'Supplements Add In Prog Notes',
  SupplementsAddCompleted = 'Supplements Add Completed',
  SupplementsInteracted = 'Supplements Interacted',
  SupplementsEdited = 'Supplements Edited',
  SupplementsRemoved = 'Supplements Removed',

  // Allergies

  AllergiesAddStarted = 'Allergies Add Started',
  AllergiesAddInProgName = 'Allergies Add In Prog Name',
  AllergiesAddInProgType = 'Allergies Add In Prog Type',
  AllergiesAddInProgCriticality = 'Allergies Add In Prog Criticality',
  AllergiesAddInProgStartDate = 'Allergies Add In Prog Start Date',
  AllergiesAddInProgStatus = 'Allergies Add In Prog Status',
  AllergiesAddInProgEndDate = 'Allergies Add In Prog End Date',
  AllergiesAddInProgNotes = 'Allergies Add In Prog Notes',
  AllergiesAddInProgRecordsAdded = 'Allergies Add In Prog Records Added',
  AllergiesAddCompleted = 'Allergies Add Completed',
  AllergiesInteracted = 'Allergies Interacted',
  AllergiesEdited = 'Allergies Edited',
  AllergiesRemoved = 'Allergies Removed',

  // Setting Flow
  SettingsClicked = 'Settings Clicked',
  SettingsInteracted = 'Settings Interacted',
  MeasurementPreferencesHeightInteracted = 'Measurement Preferences Height Interacted',
  MeasurementPreferencesWeightInteracted = 'Measurement Preferences Weight Interacted',
  CommunicationPreferencesRemindersInteracted = 'Communication Preferences Reminders Interacted',
  CommunicationPreferencesMarkComInteracted = 'Communication Preferences Mark Com Interacted',
  ConsentManagerInteracted = 'Consent Manager Interacted',
  ConsentManagerRequestInteracted = 'Consent Manager Request Interacted',
  ConsentRequestDeletionStarted = 'Consent Request Deletion Started',
  ConsentRequestDeletionCompleted = 'Consent Request Deletion Completed',
  ConsentRequestRevocationStarted = 'Consent Request Revocation Started',
  ConsentRequestRevocationCompleted = 'Consent Request Revocation Completed',
  ConsentRequestApproved = 'Consent Request Approved',
  PersonalDataDeleted = 'Personal Data Deleted',
  PersonalDataDownloaded = 'Personal Data Downloaded',
  ChatButtonClicked = 'Chat Button Clicked',
  LegalPolicyInteracted = 'Legal Policy Interacted',
  FAQInteracted = 'FAQ Interacted',
  FluentSupportInteracted = 'Fluent Support Interacted',
  AccountLoggedOut = 'Account Logged Out',
  FollowUsOnPlatformInteracted = 'Follow Us On Platform Interacted',
  AccountDeleteInteracted = 'Account Delete Interacted',
  AccountDeleted = 'Account Deleted',
  // Profile Flow
  MyProfileInteracted = 'My Profile Interacted',
  MyProfileTabInteracted = 'My Profile Tab Interacted',
  GeneralInfoEdited = 'General Info Edited',
  MyHealthProfileInteracted = 'My Health Profile Interacted',

  // Health Proxy Flow
  HealthcareProxyAddStarted = 'Healthcare Proxy Add Started',
  HealthcareProxyInProgFirstName = 'Healthcare Proxy In Prog First Name',
  HealthcareProxyInProgLastName = 'Healthcare Proxy In Prog Last Name',
  HealthcareProxyInProgRelationship = 'Healthcare Proxy In Prog Relationship',
  HealthcareProxyInProgMobileNumber = 'Healthcare Proxy In Prog Mobile Number',
  HealthcareProxyInProgRecordAdded = 'Healthcare Proxy In Prog Record Added',
  HealthcareProxyCompleted = 'Healthcare Proxy Completed',
  HealthcareProxyEditStarted = 'Healthcare Proxy Edit Started',
  HealthcareProxyEditCompleted = 'Healthcare Proxy Edit Completed',
  HealthcareProxyRemoved = 'Healthcare Proxy Removed',
  // health Insurance Flow
  HealthInsuranceAddStarted = 'Health Insurance Add Started',
  HealthInsuranceInProgPolicyName = 'Health Insurance In Prog Policy Name',
  HealthInsuranceInProgPolicyNumber = 'Health Insurance In Prog Policy Number',
  HealthInsuranceInProgMobileNumber = 'Health Insurance In Prog Mobile Number',
  HealthInsuranceAddCompleted = 'Health Insurance Add Completed',
  HealthInsuranceEditStarted = 'Health Insurance Edit Started',
  HealthInsuranceEditCompleted = 'Health Insurance Edit Completed',
  HealthInsuranceRemoved = 'Health Insurance Removed',
  HealthInsuranceRecordAdded = 'Health Insurance Record Added',
  // Emergency Contact FLow
  EmergencyContactAddStarted = 'Emergency Contact Add Started',
  EmergencyContactInProgFirstName = 'Emergency Contact In Prog First Name',
  EmergencyContactInProgLastName = 'Emergency Contact In Prog Last Name',
  EmergencyContactInProgRelationship = 'Emergency Contact In Prog Relationship',
  EmergencyContactInProgMobileNumber = 'Emergency Contact In Prog Mobile Number',
  EmergencyContactCompleted = 'Emergency Contact Completed',
  EmergencyContactEditStarted = 'Emergency Contact Edit Started',
  EmergencyContactEditCompleted = 'Emergency Contact Edit Completed',
  EmergencyContactRemoved = 'Emergency Contact Removed',
  // Profile picture flow
  ProfilePictureUploadStarted = 'Profile Picture Upload Started',
  ProfilePictureUploadError = 'Profile Picture Upload Error',
  ProfilePictureUploadCompleted = 'Profile Picture Upload Completed',
  ProfilePictureRemoved = 'Profile Picture Removed',
  // Record Flow
  AddNewRecordInteracted = 'Add Record Interacted',
  EncryptedRecordInteracted = 'Encrypted Record Interacted',
  EncryptedRecordUnlocked = 'Encrypted Record Unlocked',
  EncryptedRecordPasswordError = 'Encrypted Record Password Error',
  // Family Member History FLow
  FamilyMemberHistoryAddStarted = 'Family Member Add Started',
  FamilyMemberHistoryInProgRelationship = 'Family Member In Prog Relationship',
  FamilyMemberHistoryInProgBirthDate = 'Family Member In Prog Birthdate',
  FamilyMemberHistoryInProgStatus = 'Family Member In Prog Status',
  FamilyMemberHistoryInProgBloodGroup = 'Family Member In Prog Blood Group',
  FamilyMemberHistoryInProgEthnicity = 'Family Member In Prog Ethnicity',
  FamilyMemberHistoryRequestConsentInteracted = 'Family Member Consent Request Interacted',
  FamilyMemberHistoryIntentSelected = 'Family Member Intent Selected',
  FamilyMemberHistoryStoreHealthRecords = 'Family Member Intent Selected',
  FamilyMemberHistoryBookOnlineConsults = 'Family Member Intent Selected',
  FamilyMemberHistoryShareHealthInformation = 'Family Member Intent Selected',
  FamilyMemberHistoryKeepTrackOfHealthInfo = 'Family Member Intent Selected',
  FamilyMemberHistoryOther = 'Family Member Intent Selected',
  FamilyMemberAddCompleted = 'Family Member Add Completed',
  FamilyMemberEditStarted = 'Family Member Edit Started',
  FamilyMemberEditCompleted = 'Family Member Edit Completed',
  FamilyMemberHistoryRemoved = 'Family Member Removed',
  FamilyMemberHistoryConditionInteracted = 'Family Member Condition Interacted',
  // Consent Manager FLow
  ConsentManagerAddStarted = 'Consent Manager Add Started',
  ConsentManagerInProgRelationship = 'Consent Manager In Prog Relationship',
  ConsentManagerRequestConsentInteracted = 'Consent Manager Consent Request Interacted',
  ConsentManagerIntentSelected = 'Consent Manager Intent Selected',
  ConsentManagerStoreHealthRecords = 'Consent Manager Intent Selected',
  ConsentManagerBookOnlineConsults = 'Consent Manager Intent Selected',
  ConsentManagerShareHealthInformation = 'Consent Manager Intent Selected',
  ConsentManagerKeepTrackOfHealthInfo = 'Consent Manager Intent Selected',
  ConsentManagerOther = 'Consent Manager Intent Selected',
  // My CareTeam
  CareTeamAddStarted = 'Care Team Add Started',
  CareTeamAddInProgFirstName = 'Care Team Add In Prog First Name',
  CareTeamAddInProgLastName = 'Care Team Add In Prog Last Name',
  CareTeamAddInProgSpeciality = 'Care Team Add In Prog Speciality',
  CareTeamAddInProgPhoneNumber = 'Care Team Add In Prog Phone Number',
  CareTeamAddInProgAltPhoneNumber = 'Care Team Add In Prog Alt Phone Number',
  CareTeamAddInProgEmail = 'Care Team Add In Prog Email',
  CareTeamAddInProgCity = 'Care Team Add In Prog City',
  CareTeamAddInProgPrimary = 'Care Team Add In Prog Primary',
  CareTeamAddCompleted = 'Care Team Add Completed',
  CareTeamEditStarted = 'Care Team Edit Started',
  CareTeamEditCompleted = 'Care Team Edit Completed',
  CareTeamRemoved = 'Care Team Removed',

  // Vitals Event
  VitalInteracted = 'Vital Interacted',
  VTBPAddStarted = 'VT BP Add Started',
  VTBPAddInProgSystolic = 'VT BP Add In Prog Systolic',
  VTBPAddInProgDiastolic = 'VT BP Add In Prog Diastolic',
  VTBPAddInProgDate = 'VT BP Add In Prog Date',
  VTBPAddInProgTime = 'VT BP Add In Prog Time',
  VTBPAddInProgRecorder = 'VT BP Add In Prog Recorder',
  VTBPAddInProgPosition = 'VT BP Add In Prog Position',
  VTBPAddCompleted = 'VT BP Add Completed',
  VTBPInteracted = 'VT BP Interacted',
  VTBPEdited = 'VT BP Edited',
  VTBPRemoved = 'VT BP Removed',
  VTBTAddStarted = 'VT BT Add Started',
  VTBTAddInProgTemparature = 'VT BT Add In Prog Temparature',
  VTBTAddInProgDate = 'VT BT Add In Prog Date',
  VTBTAddInProgTime = 'VT BT Add In Prog Time',
  VTBTAddCompleted = 'VT BT Add Completed',
  VTBTInteracted = 'VT BT Interacted',
  VTBTEdited = 'VT BT Edited',
  VTBTRemoved = 'VT BT Removed',
  VTPRAddStarted = 'VT PR Add Started',
  VTPRAddInProgRate = 'VT PR Add In Prog Rate',
  VTPRAddInProgDate = 'VT PR Add In Prog Date',
  VTPRAddInProgTime = 'VT PR Add In Prog Time',
  VTPRAddCompleted = 'VT PR Add Completed',
  VTPRInteracted = 'VT PR Interacted',
  VTPREdited = 'VT PR Edited',
  VTPRRemoved = 'VT PR Removed',
  VTOSLAddStarted = 'VT OSL Add Started',
  VTOSLAddInProgLevel = 'VT OSL Add In Prog Level',
  VTOSLAddInProgDate = 'VT OSL Add In Prog Date',
  VTOSLAddInProgTime = 'VT OSL Add In Prog Time',
  VTOSLAddCompleted = 'VT OSL Add Completed',
  VTOSLInteracted = 'VT OSL Interacted',
  VTOSLEdited = 'VT OSL Edited',
  VTOSLRemoved = 'VT OSL Removed',
  VTRRAddStarted = 'VT RR Add Started',
  VTRRAddInProgRate = 'VT RR Add In Prog Rate',
  VTRRAddInProgDate = 'VT RR Add In Prog Date',
  VTRRAddInProgTime = 'VT RR Add In Prog Time',
  VTRRAddCompleted = 'VT RR Add Completed',
  VTRRInteracted = 'VT RR Interacted',
  VTRREdited = 'VT RR Edited',
  VTRRRemoved = 'VT RR Removed',
  // Surgery Events
  SurgeriesAddStarted = 'Surgeries Add Started',
  SurgeriesAddInProgType = 'Surgeries Add In Prog Type',
  SurgeriesAddInProgStatus = 'Surgeries Add In Prog Status',
  SurgeriesAddInProgDate = 'Surgeries Add In Prog Date',
  SurgeriesAddInProgNotes = 'Surgeries Add In Prog Notes',
  SurgeriesAddInProgRecordsAdded = 'Surgeries Add In Prog Records Added',
  SurgeriesAddCompleted = 'Surgeries Add Completed',
  SurgeriesInteracted = 'Surgeries Interacted',
  SurgeriesEdited = 'Surgeries Edited',
  SurgeriesRemoved = 'Surgeries Removed',
  // Preventative Screening
  PreventativeScreeningAddStarted = 'Preventative Screenings Add Started',
  PreventativeScreeningAddInProgType = 'Preventative Screenings Add In Prog Type',
  PreventativeScreeningAddInProgFamilyHistory = 'Preventative Screenings Add In Prog Family History',
  PreventativeScreeningAddInProgHPVDiagnosed = 'Preventative Screenings Add In Prog HPV Diagnosed',
  PreventativeScreeningAddInProgSexuallyActive = 'Preventative Screenings Add In Prog Sexually Active',
  PreventativeScreeningAddInProgDate = 'Preventative Screenings Add In Prog Date',
  PreventativeScreeningAddInProgNotes = 'Preventative Screenings Add In Prog Notes',
  PreventativeScreeningAddInProgRecordsAdded = 'Preventative Screenings Add In Prog Records Added',
  PreventativeScreeningAddCompleted = 'Preventative Screenings Add Completed',
  PreventativeScreeningInteracted = 'Preventative Screenings Interacted',
  PreventativeScreeningEdited = 'Preventative Screenings Edited',
  PreventativeScreeningRemoved = 'Preventative Screenings Removed',
  // Vaccine
  VaccineAddStarted = 'Vaccine Add Started',
  VaccineAddInProgDate = 'Vaccine Add In Prog Date',
  VaccineAddInProgFullyVaccinated = 'Vaccine Add In Prog Fully Vaccinated',
  VaccineAddInProgDoseCount = 'Vaccine Add In Prog Dose Count',
  VaccineAddInProgRecordsAdded = 'Vaccine Add In Prog Records Added',
  VaccineAddCompleted = 'Vaccine Add Completed',
  VaccineInteracted = 'Vaccine Interacted',
  VaccineEdited = 'Vaccine Edited',
  VaccineRemoved = 'Vaccine Removed',
  // Lifestyle and Nutrition - Exercise
  LNExerciseAddStarted = 'LN Exercise Add Started',
  LNExerciseAddInProgActivity = 'LN Exercise Add In Prog Activity',
  LNExerciseAddInProgFrequency = 'LN Exercise Add In Prog Frequency',
  LNExerciseAddInProgIntenseExercise = 'LN Exercise Add In Prog Intense Exercise',
  LNExerciseAddInProgFitnessRoutine = 'LN Exercise Add In Prog Fitness Routine',
  LNExerciseAddCompleted = 'LN Exercise Add Completed',
  LNExerciseInteracted = 'LN Exercise Interacted',
  LNExerciseEdited = 'LN Exercise Edited',
  LNExerciseRemoved = 'LN Exercise Removed',
  // Lifestyle and Nutrition - Diet
  LNDietAddStarted = 'LN Diet Add Started',
  LNDietAddInProgType = 'LN Diet Add In Prog Type',
  LNDietAddInProgPreferences = 'LN Diet Add In Prog Preferences',
  LNDietAddCompleted = 'LN Diet Add Completed',
  LNDietInteracted = 'LN Diet Interacted',
  LNDietEdited = 'LN Diet Edited',
  LNDietRemoved = 'LN Diet Removed',
  // Lifestyle and Nutrition - ATC
  LNATCAddStarted = 'LN ATC Add Started',
  LNATCAddInProgAlcohol = 'LN ATC Add In Prog Alcohol',
  LNATCAddInProgTobacco = 'LN ATC Add In Prog Tobacco',
  LNATCAddInProgCaffeine = 'LN ATC Add In Prog Caffeine',
  LNATCAddCompleted = 'LN ATC Add Completed',
  LNATCInteracted = 'LN ATC Interacted',
  LNATCEdited = 'LN ATC Edited',
  LNATCRemoved = 'LN ATC Removed',
  // Lifestyle and Nutrition - MHS
  LNMHSAddStarted = 'LN MH&S Add Started',
  LNMHSAddInProgMentalCondition = 'LN MH&S Add In Prog Mental Condition',
  LNMHSAddInProgFamilyHistory = 'LN MH&S Add In Prog Family History',
  LNMHSAddInProgBurntOut = 'LN MH&S Add In Prog Burnt Out',
  LNMHSAddInProgSleep = 'LN MH&S Add In Prog Sleep',
  LNMHSAddInProgMorningFeel = 'LN MH&S Add In Prog Morning Feel',
  LNMHSAddInProgStressLevel = 'LN MH&S Add In Prog Stress Level',
  LNMHSAddCompleted = 'LN MH&S Add Completed',
  LNMHSInteracted = 'LN MH&S Interacted',
  LNMHSEdited = 'LN MH&S Edited',
  LNMHSRemoved = 'LN MH&S Removed',
  // Lifestyle and Nutrition - Occupation
  LNOccupationAddStarted = 'LN Occupation Add Started',
  LNOccupationAddInProgName = 'LN Occupation Add In Prog Name',
  LNOccupationAddInProgExtendedHours = 'LN Occupation Add In Prog Extended Hours',
  LNOccupationAddCompleted = 'LN Occupation Add Completed',
  LNOccupationInteracted = 'LN Occupation Interacted',
  LNOccupationEdited = 'LN Occupation Edited',
  LNOccupationRemoved = 'LN Occupation Removed',
  // Additional Medical Decision Maker
  AMDMAddStarted = 'AMDM Add Started',
  AMDMInProgFirstName = 'AMDM In Prog First Name',
  AMDMInProgLastName = 'AMDM In Prog Last Name',
  AMDMInProgRelationship = 'AMDM In Prog Relationship',
  AMDMInProgMobileNumber = 'AMDM In Prog Mobile Number',
  AMDMInProgRecordAdded = 'AMDM In Prog Record Added',
  AMDMAddCompleted = 'AMDM Add Completed',
  AMDMEditStarted = 'AMDM Edit Started',
  AMDMEditCompleted = 'AMDM Edit Completed',
  AMDMRemoved = 'AMDM Removed',
  // Family Member Condition events
  FMConditionAddStarted = 'FM Condition Add Started',
  FMConditionAddInProgName = 'FM Condition Add In Prog Name',
  FMConditionAddInProgChronicity = 'FM Condition Add In Prog Chronicity',
  FMConditionAddInProgDate = 'FM Condition Add In Prog Date',
  FMConditionAddInProgShared = 'FM Condition Add In Prog Shared',
  FMConditionAddInProgStatus = 'FM Condition Add In Prog Status',
  FMConditionAddInProgEndDate = 'FM Condition Add In Prog End Date',
  FMConditionAddInProgNotes = 'FM Condition Add In Prog Notes',
  FMConditionAddInProgRecordsAdded = 'FM Condition Add In Prog Records Added',
  FMConditionAddCompleted = 'FM Condition Add Completed',
  FMConditionInteracted = 'FM Condition Interacted',
  FMConditionEditStarted = 'FM Condition Edit Started',
  FMConditionEditCompleted = 'FM Condition Edit Completed',
  FMConditionRemoved = 'FM Condition Removed',
  // Reproductive Health events
  ReproductiveHealthAddStarted = 'Reproductive Health Add Started',
  ReproductiveHealthAddInProgSexuallyActive = 'Reproductive Health Add In Prog Sexually Active',
  ReproductiveHealthAddInProgMenstrualDate = 'Reproductive Health Add In Prog Menstrual Date',
  ReproductiveHealthAddInProgPregnancyHistory = 'Reproductive Health Add In Prog Pregnancy History',
  ReproductiveHealthAddInProgPregnancyTerminate = 'Reproductive Health Add In Prog Pregnancy Terminate',
  ReproductiveHealthAddInProgChildren = 'Reproductive Health Add In Prog Children',
  ReproductiveHealthAddInProgMiscarriage = 'Reproductive Health Add In Prog Miscarriage',
  ReproductiveHealthAddInProgFertilityTreatments = 'Reproductive Health Add In Prog Fertility Treatments',
  ReproductiveHealthAddInProgPregnancyStatus = 'Reproductive Health Add In Prog Pregnancy Status',
  ReproductiveHealthAddCompleted = 'Reproductive Health Add Completed',
  ReproductiveHealthInteracted = 'Reproductive Health Interacted',
  ReproductiveHealthEdited = 'Reproductive Health Edited',
  ReproductiveHealthRemoved = 'Reproductive Health Removed',
  HealthProfileBasicInfoAdded = 'Health Profile Basic Info Added',
  HealthProfileBasicInfoEdited = 'Health Profile Basic Info Edited',
  // Reminder events
  ReminderAddStarted = 'Reminder Add Started',
  ReminderAddInProgName = 'Reminder Add In Prog Name',
  ReminderAddInProgDate = 'Reminder Add In Prog Date',
  ReminderAddInProgAllDay = 'Reminder Add In Prog All Day',
  ReminderAddInProgTime = 'Reminder Add In Prog Time',
  ReminderAddInProgFrequency = 'Reminder Add In Prog Frequency',
  ReminderAddInProgNotes = 'Reminder Add In Prog Notes',
  ReminderAddCompleted = 'Reminder Add Completed',
  ReminderMarkedCompleted = 'Reminder Marked Completed',
  ReminderEditStarted = 'Reminder Edit Started',
  ReminderEditCompleted = 'Reminder Edit Completed',
  ReminderRemoved = 'Reminder Removed',
}

export enum AnalyticsFlow {
  ManageCondition = 'Manage Condition',
  ConditionManaged = 'Condition Managed',
  RelatedPersonConditionManaged = 'Related Person Condition Managed',
  ManagePreventativeScreening = 'Manage Preventative Screening',
  PreventativeScreeningManaged = 'Preventative Screenings Managed',
  ManageSurgeriesAndProcedures = 'Manage Surgeries and Procedures',
  SurgeriesManaged = 'Surgeries Managed',

  SymptomsManaged = 'Symptoms Managed',

  LifestyleNutritionManaged = 'Lifestyle Nutrition Managed',

  ManageImmunization = 'Manage Immunization',
  // ManageAllergiesAndIntolerances = 'Manage Allergies and Intolerances',
  AllergyManaged = 'Allergy Managed',
  AllergyDeleted = 'Allergy Deleted',
  ManageMedications = 'Manage Medications',
  MedicationsAndSupplementsManaged = 'Medications And Supplements Managed',
  ViewChart = 'View Chart',
  ManageMyHealthProfileQuestionnaire = 'Manage My Health Profile Questionnaire',
  ManageProfileGeneralInfo = 'Manage Profile General Info',
  ManageFamilyMember = 'Manage Family Member',
  ManageEmergencyContact = 'Emergency Contact Managed',
  ManageHealthInsurance = 'Manage Health Insurance',
  InviteHCPtoCareTeam = 'Invite HCP to Care Team',
  RemoveFromCareTeam = 'Remove From Care Team',
  MyCareTeam = 'My Care Team',
  InteractWithDoctorsDetails = "Interact with Doctor's Details",
  ManageHealthcareProxy = 'Healthcare Proxy Managed',
  ManageMedicalRecord = 'Manage Medical Record',
  ManageEditMedicalRecord = 'Manage Edit Medical Record',
  SignIn = 'Sign In',
  DashboardItemInteracted = 'Dashboard Item Interacted',
  ManageReminder = 'Manage Reminder',
  BookOffline = 'Book Offline',
  HideFromSharingAdded = 'Hide from Sharing Added',
  CompletionAction = 'Completion Action',
  ShareMedicalRecords = 'Share Medical Records',
  InformationButtonClicked = 'Information Button Clicked',
  CrossButtonClicked = 'Cross Button Clicked',
  HeightManaged = 'Height Managed',
  WeightManaged = 'Weight Managed',
  AgeManaged = 'Age Managed',
  BloodGroupManaged = 'Blood Group Managed',
  GenderManaged = 'Gender Managed',
  SexAssignedAtBirthManaged = 'Sex Assigned at Birth Managed',
  EthnicityManaged = 'Ethnicity Managed',
  PreferredLanguageManaged = 'Preferred Language Managed',
  // Manage Record Filter
  RecordsFilter = 'RecordsFilter',
  // Share Profile Clicked
  ShareProfileStarted = 'Share Profile Started',
  ShareProfileCompleted = 'Share Profile Completed',
  ShareProfileClicked = 'Share Profile Clicked',
  ShareProfileLinkGenerated = 'Share Profile Link Generated',
  ShareRecordLinkGenerated = 'Share Record Link Generated',
  DownloadRecordClicked = 'Records Downloaded',
  DownloadProfileClicked = 'Download Profile Started',
  DownloadProfileCompleted = 'Download Profile Completed',
  VaccinesManaged = 'Vaccines Managed',
  HealthInsuranceManaged = 'Health Insurance Managed',

  // Share Record
  ShareRecordStarted = 'Share Record Started',
  ShareRecordCompleted = 'Share Record Completed',
  // Manage Profile Picture
  ProfilePictureManaged = 'Profile Picture Managed',
  // Medical Records
  RecordsListViewed = 'Records List Viewed',
  RecordsInteracted = 'Records Interacted',
  RecordsClosed = 'Records Closed',
  NewRecordUploaded = 'New Record Uploaded',
  RecordsFiltered = 'Records Filtered',
  RecordsShared = 'Records Shared',
  ConsentInitiated = 'Consent Initiated',
  ConsentDeleted = 'Consent Deleted',
  //
  // New Event Flow - Post CleanUp
  //
  LoginFlow = 'Login Flow (LO)',
  ConditionFlow = 'Condition Flow (CO)',
  SymptomsFlow = 'Symptoms Flow (SY)',
  UploadRecordFlow = 'Upload Record Flow (UR)',
  BookmarkFlow = 'Record Bookmark/Unbookmarked Flow (RB)',
  EditMedicalRecordFlow = 'Record Edit (RE)',
  HealthRecordHubFlow = 'Health Record Hub (HRH)',
  RecordDownloadFlow = 'Record Download (RD)',
  SharedMedicalRecordFlow = 'Record Shared (RS)',
  MedicalRecordFilterFlow = 'Record Sorted and Filtered (RF)',
  MedicationsFlow = 'Medications (ME)',
  SupplementsFlow = 'Supplements (SU)',
  SettingsFlow = 'Settings Flow (ST)',
  AllergiesFlow = 'Allergies (AL)',
  ProfileFlow = 'Profile Interacted (PI)',
  HealthcareProxyFlow = 'Healthcare Proxy Flow (HP)',
  HealthInsuranceFlow = 'Health Insurance Flow (HI)',
  EmergencyContactFlow = 'Emergency Contact Flow (EC)',
  ProfilePictureFlow = 'Profile Picture Flow (PP)',
  AdditionalMedicalDecisionMakerFlow = 'Additional Medical Decision Maker Flow (AMDM)',
  RecordFlow = 'Records Flow (RI)',
  FamilyMemberHistoryFlow = 'Family Member Flow (FMH)',
  ReproductiveHealthFlow = 'Reproductive Health Flow (RH)',
  ConsentManagerFlow = 'Consent Manager Flow (CM)',
  VitalFlow = 'Vitals (VT)',
  SurgeriesFlow = 'Surgeries (SUR)',
  PreventativeScreeningFlow = 'Preventative Screening (PS)',
  VaccineFlow = 'Vaccines (VC)',
  LifestyleNutritionFlow = 'Lifestyle and Nutrition (LN)',
  HealthProfileBasicInfo = 'Health Profile Basic Info (HPBI)',
  ReminderFlow = 'Reminders (RM)',
}

export const FLOW_EVENTS_MAP: any = {
  [AnalyticsFlow.RelatedPersonConditionManaged]: [AnalyticsEventName.RelatedPersonConditionManaged],
  [AnalyticsFlow.PreventativeScreeningManaged]: [AnalyticsEventName.PreventativeScreeningManaged],
  [AnalyticsFlow.MedicationsAndSupplementsManaged]: [AnalyticsEventName.MedicationsAndSupplementsManaged],
  [AnalyticsFlow.ManageSurgeriesAndProcedures]: [
    AnalyticsEventName.ManageSurgeriesAndProceduresStarted,
    AnalyticsEventName.ManageSurgeriesAndProceduresCompleted,
  ],
  [AnalyticsFlow.ManageImmunization]: [
    AnalyticsEventName.ManageImmunizationStarted,
    AnalyticsEventName.ManageImmunizationCompleted,
  ],
  [AnalyticsFlow.ManagePreventativeScreening]: [
    AnalyticsEventName.ManagePreventativeScreeningStarted,
    AnalyticsEventName.ManagePreventativeScreeningCompleted,
  ],
  [AnalyticsFlow.AllergyManaged]: [
    AnalyticsEventName.AllergyManaged,
    // AnalyticsEventName.ManageAllergiesAndIntolerancesCompleted,
  ],
  [AnalyticsFlow.SurgeriesManaged]: [AnalyticsEventName.SurgeriesManaged],
  [AnalyticsFlow.AllergyDeleted]: [AnalyticsEventName.AllergyDeleted],
  [AnalyticsFlow.ReproductiveHealthFlow]: [
    AnalyticsEventName.ReproductiveHealthAddStarted,
    AnalyticsEventName.ReproductiveHealthAddInProgSexuallyActive,
    AnalyticsEventName.ReproductiveHealthAddInProgMenstrualDate,
    AnalyticsEventName.ReproductiveHealthAddInProgPregnancyHistory,
    AnalyticsEventName.ReproductiveHealthAddInProgPregnancyTerminate,
    AnalyticsEventName.ReproductiveHealthAddInProgChildren,
    AnalyticsEventName.ReproductiveHealthAddInProgMiscarriage,
    AnalyticsEventName.ReproductiveHealthAddInProgFertilityTreatments,
    AnalyticsEventName.ReproductiveHealthAddInProgPregnancyStatus,
    AnalyticsEventName.ReproductiveHealthAddCompleted,
    AnalyticsEventName.ReproductiveHealthInteracted,
    AnalyticsEventName.ReproductiveHealthEdited,
    AnalyticsEventName.ReproductiveHealthRemoved,
  ],
  [AnalyticsFlow.LifestyleNutritionManaged]: [AnalyticsEventName.LifestyleNutritionManaged],

  [AnalyticsFlow.ManageMedications]: [
    AnalyticsEventName.ManageMedicationsStarted,
    AnalyticsEventName.ManageMedicationsCompleted,
  ],
  [AnalyticsFlow.VaccinesManaged]: [AnalyticsEventName.VaccinesManaged, AnalyticsEventName.VaccinesManaged],

  [AnalyticsFlow.ViewChart]: [AnalyticsEventName.ChartViewed],
  [AnalyticsFlow.ManageMyHealthProfileQuestionnaire]: [AnalyticsEventName.ManageMyHealthProfileQuestionnaire],
  [AnalyticsFlow.ManageEmergencyContact]: [AnalyticsEventName.ManageEmergencyContact],
  [AnalyticsFlow.InviteHCPtoCareTeam]: [AnalyticsEventName.InviteHCPtoCareTeam],
  [AnalyticsFlow.RemoveFromCareTeam]: [AnalyticsEventName.RemoveFromCareTeam],
  [AnalyticsFlow.MyCareTeam]: [
    AnalyticsEventName.CareTeamAddStarted,
    AnalyticsEventName.CareTeamAddInProgFirstName,
    AnalyticsEventName.CareTeamAddInProgLastName,
    AnalyticsEventName.CareTeamAddInProgSpeciality,
    AnalyticsEventName.CareTeamAddInProgPhoneNumber,
    AnalyticsEventName.CareTeamAddInProgAltPhoneNumber,
    AnalyticsEventName.CareTeamAddInProgEmail,
    AnalyticsEventName.CareTeamAddInProgCity,
    AnalyticsEventName.CareTeamAddInProgPrimary,
    AnalyticsEventName.CareTeamAddCompleted,
    AnalyticsEventName.CareTeamEditStarted,
    AnalyticsEventName.CareTeamEditCompleted,
    AnalyticsEventName.CareTeamRemoved,
  ],
  [AnalyticsFlow.InteractWithDoctorsDetails]: [AnalyticsEventName.InteractWithDoctorsDetails],
  [AnalyticsFlow.ManageHealthcareProxy]: [AnalyticsFlow.ManageHealthcareProxy],
  [AnalyticsFlow.ManageHealthInsurance]: [
    AnalyticsEventName.ManageHealthInsuranceStarted,
    AnalyticsEventName.ManageHealthInsuranceCompleted,
    AnalyticsEventName.ManageHealthInsuranceDeleted,
  ],
  [AnalyticsFlow.ManageFamilyMember]: [
    AnalyticsEventName.ManageFamilyMemberStarted,
    AnalyticsEventName.ManageFamilyMemberCompleted,
  ],
  [AnalyticsFlow.ManageMedicalRecord]: [
    AnalyticsEventName.AddMedicalRecordStarted,
    AnalyticsEventName.AddMedicalRecordUploadMethod,
    AnalyticsEventName.AddMedicalRecordType,
    AnalyticsEventName.AddMedicalRecordDocument,
    AnalyticsEventName.AddMedicalRecordTitle,
    AnalyticsEventName.AddMedicalRecordDateOfSampling,
    AnalyticsEventName.AddMedicalRecordPhysician,
    AnalyticsEventName.AddMedicalRecordNotes,
    AnalyticsEventName.AddMedicalRecordCompleted,
    AnalyticsEventName.ViewMedicalRecord,
  ],
  [AnalyticsFlow.ManageProfileGeneralInfo]: [
    AnalyticsEventName.ManageProfileSpecificInfoStarted,
    AnalyticsEventName.ManageProfileSpecificInfoCompleted,
  ],
  [AnalyticsFlow.SignIn]: [
    AnalyticsEventName.SignInPhoneNumberChanged,
    AnalyticsEventName.OTPValidated,
    AnalyticsEventName.OTPResend,
    AnalyticsEventName.SignInCompleted,
  ],
  [AnalyticsFlow.DashboardItemInteracted]: [AnalyticsEventName.DashboardItemInteracted],
  [AnalyticsFlow.BookOffline]: [AnalyticsEventName.BookOfflineStarted, AnalyticsEventName.BookOfflineCompleted],
  [AnalyticsFlow.HideFromSharingAdded]: [AnalyticsEventName.HideFromSharingAdded],
  [AnalyticsFlow.CompletionAction]: [AnalyticsEventName.CompletionAction],
  [AnalyticsFlow.ManageReminder]: [
    AnalyticsEventName.ManageReminderStarted,
    AnalyticsEventName.ManageReminderCompleted,
  ],
  [AnalyticsFlow.ShareMedicalRecords]: [
    AnalyticsEventName.ShareMedicalRecordsStarted,
    AnalyticsEventName.ShareMedicalRecordsCompleted,
  ],
  [AnalyticsFlow.ShareProfileClicked]: [AnalyticsEventName.ShareProfileClicked],
  [AnalyticsFlow.ShareProfileLinkGenerated]: [AnalyticsEventName.ShareProfileLinkGenerated],
  [AnalyticsFlow.ShareRecordLinkGenerated]: [AnalyticsEventName.ShareRecordLinkGenerated],
  [AnalyticsFlow.ShareProfileStarted]: [AnalyticsEventName.ShareProfileStarted],
  [AnalyticsFlow.ShareProfileCompleted]: [AnalyticsEventName.ShareProfileCompleted],
  [AnalyticsFlow.DownloadRecordClicked]: [AnalyticsEventName.DownloadRecordClicked],
  [AnalyticsFlow.DownloadProfileClicked]: [AnalyticsEventName.DownloadProfileClicked],
  [AnalyticsFlow.DownloadProfileCompleted]: [AnalyticsEventName.DownloadProfileCompleted],
  [AnalyticsFlow.CrossButtonClicked]: [AnalyticsEventName.CrossButtonClicked],
  [AnalyticsFlow.InformationButtonClicked]: [AnalyticsEventName.InformationButtonClicked],
  [AnalyticsFlow.HeightManaged]: [AnalyticsEventName.HeightManaged],
  [AnalyticsFlow.WeightManaged]: [AnalyticsEventName.WeightManaged],
  [AnalyticsFlow.AgeManaged]: [AnalyticsEventName.AgeManaged],
  [AnalyticsFlow.BloodGroupManaged]: [AnalyticsEventName.BloodGroupManaged],
  [AnalyticsFlow.GenderManaged]: [AnalyticsEventName.GenderManaged],
  [AnalyticsFlow.SexAssignedAtBirthManaged]: [AnalyticsEventName.SexAssignedAtBirthManaged],
  [AnalyticsFlow.EthnicityManaged]: [AnalyticsEventName.EthnicityManaged],
  [AnalyticsFlow.PreferredLanguageManaged]: [AnalyticsEventName.PreferredLanguageManaged],
  [AnalyticsFlow.RecordsFilter]: [AnalyticsEventName.RecordsFilter],
  [AnalyticsFlow.HealthInsuranceManaged]: [AnalyticsEventName.HealthInsuranceManaged],
  [AnalyticsFlow.ProfilePictureManaged]: [AnalyticsEventName.ProfilePictureManaged],
  [AnalyticsFlow.RecordsListViewed]: [AnalyticsEventName.RecordsListViewed],
  [AnalyticsFlow.RecordsInteracted]: [AnalyticsEventName.RecordsInteracted],
  [AnalyticsFlow.RecordsClosed]: [AnalyticsEventName.RecordsClosed],
  [AnalyticsFlow.NewRecordUploaded]: [AnalyticsEventName.NewRecordUploaded],
  [AnalyticsFlow.RecordsFiltered]: [AnalyticsEventName.RecordsFiltered],
  [AnalyticsFlow.RecordsShared]: [AnalyticsEventName.RecordsShared],
  [AnalyticsFlow.ConsentDeleted]: [AnalyticsEventName.ConsentDeleted],
  [AnalyticsFlow.ShareRecordStarted]: [AnalyticsEventName.ShareRecordStarted],
  [AnalyticsFlow.ShareRecordCompleted]: [AnalyticsEventName.ShareRecordCompleted],
  [AnalyticsFlow.LoginFlow]: [
    AnalyticsEventName.LoginMobileNumberEntered,
    AnalyticsEventName.LoginMobileNumberError,
    AnalyticsEventName.LoginStarted,
    AnalyticsEventName.PINError1,
    AnalyticsEventName.PINError2,
    AnalyticsEventName.PINError3,
    AnalyticsEventName.LoginAccountLocked,
    AnalyticsEventName.LoginMPINEntered,
    AnalyticsEventName.Login2FAOTPError,
    AnalyticsEventName.Login2FAOTPExpired,
    AnalyticsEventName.Login2FAOTPResendClicked,
    AnalyticsEventName.PINResetStarted,
    AnalyticsEventName.OTPEntered,
    AnalyticsEventName.OTPEnteredError,
    AnalyticsEventName.OTPExpiredError,
    AnalyticsEventName.ResendOTPClicked,
    AnalyticsEventName.PINResetCompleted,
    AnalyticsEventName.LoginCompleted,
  ],
  [AnalyticsFlow.ConditionFlow]: [
    AnalyticsEventName.ConditionAddStarted,
    AnalyticsEventName.ConditionAddInProgName,
    AnalyticsEventName.ConditionAddInProgChronicity,
    AnalyticsEventName.ConditionAddInProgStartDate,
    AnalyticsEventName.ConditionAddInProgShared,
    AnalyticsEventName.ConditionAddInProgStatus,
    AnalyticsEventName.ConditionAddInProgEndDate,
    AnalyticsEventName.ConditionAddInProgNotes,
    AnalyticsEventName.ConditionAddInProgRecordsAdded,
    AnalyticsEventName.ConditionAddCompleted,
    AnalyticsEventName.ConditionInteracted,
    AnalyticsEventName.ConditionEdited,
    AnalyticsEventName.ConditionRemoved,
  ],
  [AnalyticsFlow.SymptomsFlow]: [
    AnalyticsEventName.SymptomsAddStarted,
    AnalyticsEventName.SymptomsAddInProgName,
    AnalyticsEventName.SymptomsAddInProgStartDate,
    AnalyticsEventName.SymptomsAddInProgEndDate,
    AnalyticsEventName.SymptomsAddInProgRecordsAdded,
    AnalyticsEventName.SymptomsAddCompleted,
    AnalyticsEventName.SymptomsInteracted,
    AnalyticsEventName.SymptomsEdited,
    AnalyticsEventName.SymptomsRemoved,
    AnalyticsEventName.SymptomsAddInProgNotes,
    AnalyticsEventName.SymptomsAddInProgStatus,
  ],
  [AnalyticsFlow.UploadRecordFlow]: [
    AnalyticsEventName.UploadRecordStarted,
    AnalyticsEventName.UploadRecordInProgUserSelected,
    AnalyticsEventName.UploadRecordInProgSuccessful,
    AnalyticsEventName.UploadRecordInProgTypeSelected,
    AnalyticsEventName.UploadRecordInProgNameAdded,
    AnalyticsEventName.UploadRecordInProgVisitDateAdded,
    AnalyticsEventName.UploadRecordInProgDoctorNameAdded,
    AnalyticsEventName.UploadRecordInProgNotesAdded,
    AnalyticsEventName.UploadRecordCompleted,
    AnalyticsEventName.UploadRecordExited,
  ],
  [AnalyticsFlow.BookmarkFlow]: [AnalyticsEventName.RecordBookmarked, AnalyticsEventName.RecordUnbookmarked],
  [AnalyticsFlow.EditMedicalRecordFlow]: [
    AnalyticsEventName.EditRecordStarted,
    AnalyticsEventName.EditRecordCompleted,
    AnalyticsEventName.RecordRemoved,
  ],
  [AnalyticsFlow.HealthRecordHubFlow]: [
    AnalyticsEventName.HealthRecordHubInteracted,
    AnalyticsEventName.HealthRecordHubSearched,
    AnalyticsEventName.RecordInteracted,
    AnalyticsEventName.EncryptedRecordInteracted,
    AnalyticsEventName.EncryptedRecordUnlocked,
    AnalyticsEventName.EncryptedRecordPasswordError,
    AnalyticsEventName.RejectedRecordInteracted,
    AnalyticsEventName.ProcessingRecordInteracted,
    AnalyticsEventName.ApprovedRecordInteracted,
    AnalyticsEventName.RejectedRecordReuploadInteracted,
    AnalyticsEventName.RejectedRecordConsentInteracted,
  ],
  [AnalyticsFlow.RecordDownloadFlow]: [AnalyticsEventName.RecordDownloaded],
  [AnalyticsFlow.SharedMedicalRecordFlow]: [AnalyticsEventName.RecordShared],
  [AnalyticsFlow.MedicalRecordFilterFlow]: [
    AnalyticsEventName.RecordsFilteredApplied,
    AnalyticsEventName.RecordsFilterRemoved,
  ],
  [AnalyticsFlow.MedicationsFlow]: [
    AnalyticsEventName.MedicationsAddStarted,
    AnalyticsEventName.MedicationsAddInProgType,
    AnalyticsEventName.MedicationsAddInProgName,
    AnalyticsEventName.MedicationsAddInProgStatus,
    AnalyticsEventName.MedicationsAddInProgDosage,
    AnalyticsEventName.MedicationsAddInProgFrequency,
    AnalyticsEventName.MedicationsAddInProgAdministered,
    AnalyticsEventName.MedicationsAddInProgRecordsAdded,
    AnalyticsEventName.MedicationsAddCompleted,
    AnalyticsEventName.MedicationsInteracted,
    AnalyticsEventName.MedicationsEdited,
    AnalyticsEventName.MedicationsRemoved,
    AnalyticsEventName.MedicationsAddInProgStartDate,
    AnalyticsEventName.MedicationsAddInProgEndDate,
    AnalyticsEventName.MedicationsAddInProgNotes,
    AnalyticsEventName.MedicationsAddInProgFood,
    AnalyticsEventName.MedicationsAddInProgScheduleMorning,
    AnalyticsEventName.MedicationsAddInProgScheduleAfternoon,
    AnalyticsEventName.MedicationsAddInProgScheduleEvening,
    AnalyticsEventName.MedicationsAddInProgScheduleNight,
    AnalyticsEventName.MedicationsAddInProgScheduleDuration,
    AnalyticsEventName.MedicationsAddInProgScheduleFood,
  ],
  [AnalyticsFlow.SupplementsFlow]: [
    AnalyticsEventName.SupplementsAddStarted,
    AnalyticsEventName.SupplementsAddInProgName,
    AnalyticsEventName.SupplementsAddInProgStartDate,
    AnalyticsEventName.SupplementsAddInProgStatus,
    AnalyticsEventName.SupplementsAddInProgEndDate,
    AnalyticsEventName.SupplementsAddInProgScheduleMorning,
    AnalyticsEventName.SupplementsAddInProgScheduleAfternoon,
    AnalyticsEventName.SupplementsAddInProgScheduleEvening,
    AnalyticsEventName.SupplementsAddInProgScheduleNight,
    AnalyticsEventName.SupplementsAddInProgScheduleDuration,
    AnalyticsEventName.SupplementsAddInProgScheduleFood,
    AnalyticsEventName.SupplementsAddInProgDosage,
    AnalyticsEventName.SupplementsAddInProgFrequency,
    AnalyticsEventName.SupplementsAddInProgAdministered,
    AnalyticsEventName.SupplementsAddInProgPrescribed,
    AnalyticsEventName.SupplementsAddInProgRecordsAdded,
    AnalyticsEventName.SupplementsAddInProgNotes,
    AnalyticsEventName.SupplementsAddCompleted,
    AnalyticsEventName.SupplementsInteracted,
    AnalyticsEventName.SupplementsEdited,
    AnalyticsEventName.SupplementsRemoved,
  ],

  [AnalyticsFlow.SettingsFlow]: [
    AnalyticsEventName.SettingsClicked,
    AnalyticsEventName.SettingsInteracted,
    AnalyticsEventName.MeasurementPreferencesHeightInteracted,
    AnalyticsEventName.MeasurementPreferencesWeightInteracted,
    AnalyticsEventName.CommunicationPreferencesRemindersInteracted,
    AnalyticsEventName.CommunicationPreferencesMarkComInteracted,
    AnalyticsEventName.ConsentManagerInteracted,
    AnalyticsEventName.ConsentManagerRequestInteracted,
    AnalyticsEventName.ConsentRequestDeletionStarted,
    AnalyticsEventName.ConsentRequestDeletionCompleted,
    AnalyticsEventName.ConsentRequestRevocationStarted,
    AnalyticsEventName.ConsentRequestRevocationCompleted,
    AnalyticsEventName.ConsentRequestApproved,
    AnalyticsEventName.PersonalDataDeleted,
    AnalyticsEventName.PersonalDataDownloaded,
    AnalyticsEventName.ChatButtonClicked,
    AnalyticsEventName.LegalPolicyInteracted,
    AnalyticsEventName.FAQInteracted,
    AnalyticsEventName.FluentSupportInteracted,
    AnalyticsEventName.AccountLoggedOut,
    AnalyticsEventName.FollowUsOnPlatformInteracted,
    AnalyticsEventName.AccountDeleteInteracted,
    AnalyticsEventName.AccountDeleted,
  ],
  [AnalyticsFlow.AllergiesFlow]: [
    AnalyticsEventName.AllergiesAddStarted,
    AnalyticsEventName.AllergiesAddInProgName,
    AnalyticsEventName.AllergiesAddInProgType,
    AnalyticsEventName.AllergiesAddInProgCriticality,
    AnalyticsEventName.AllergiesAddInProgStartDate,
    AnalyticsEventName.AllergiesAddInProgStatus,
    AnalyticsEventName.AllergiesAddInProgEndDate,
    AnalyticsEventName.AllergiesAddInProgNotes,
    AnalyticsEventName.AllergiesAddInProgRecordsAdded,
    AnalyticsEventName.AllergiesAddCompleted,
    AnalyticsEventName.AllergiesInteracted,
    AnalyticsEventName.AllergiesEdited,
    AnalyticsEventName.AllergiesRemoved,
  ],
  [AnalyticsFlow.ProfileFlow]: [
    AnalyticsEventName.MyProfileInteracted,
    AnalyticsEventName.MyProfileTabInteracted,
    AnalyticsEventName.GeneralInfoEdited,
    AnalyticsEventName.MyHealthProfileInteracted,
  ],
  [AnalyticsFlow.HealthcareProxyFlow]: [
    AnalyticsEventName.MyProfileTabInteracted,
    AnalyticsEventName.GeneralInfoEdited,
    AnalyticsEventName.HealthcareProxyAddStarted,
    AnalyticsEventName.HealthcareProxyInProgFirstName,
    AnalyticsEventName.HealthcareProxyInProgLastName,
    AnalyticsEventName.HealthcareProxyInProgRelationship,
    AnalyticsEventName.HealthcareProxyInProgMobileNumber,
    AnalyticsEventName.HealthcareProxyInProgRecordAdded,
    AnalyticsEventName.HealthcareProxyCompleted,
    AnalyticsEventName.HealthcareProxyEditStarted,
    AnalyticsEventName.HealthcareProxyEditCompleted,
    AnalyticsEventName.HealthcareProxyRemoved,
  ],
  [AnalyticsFlow.HealthInsuranceFlow]: [
    AnalyticsEventName.HealthInsuranceAddStarted,
    AnalyticsEventName.HealthInsuranceInProgPolicyName,
    AnalyticsEventName.HealthInsuranceInProgPolicyNumber,
    AnalyticsEventName.HealthInsuranceInProgMobileNumber,
    AnalyticsEventName.HealthInsuranceRecordAdded,
    AnalyticsEventName.HealthInsuranceAddCompleted,
    AnalyticsEventName.HealthInsuranceEditStarted,
    AnalyticsEventName.HealthInsuranceEditCompleted,
    AnalyticsEventName.HealthInsuranceRemoved,
  ],
  [AnalyticsFlow.EmergencyContactFlow]: [
    AnalyticsEventName.EmergencyContactAddStarted,
    AnalyticsEventName.EmergencyContactInProgFirstName,
    AnalyticsEventName.EmergencyContactInProgLastName,
    AnalyticsEventName.EmergencyContactInProgRelationship,
    AnalyticsEventName.EmergencyContactInProgMobileNumber,
    AnalyticsEventName.EmergencyContactCompleted,
    AnalyticsEventName.EmergencyContactEditStarted,
    AnalyticsEventName.EmergencyContactEditCompleted,
    AnalyticsEventName.EmergencyContactRemoved,
  ],
  [AnalyticsFlow.ProfilePictureFlow]: [
    AnalyticsEventName.ProfilePictureUploadStarted,
    AnalyticsEventName.ProfilePictureUploadCompleted,
    AnalyticsEventName.ProfilePictureRemoved,
    AnalyticsEventName.ProfilePictureUploadError,
  ],
  [AnalyticsFlow.RecordFlow]: [
    AnalyticsEventName.AddNewRecordInteracted,
    AnalyticsEventName.EncryptedRecordInteracted,
    AnalyticsEventName.EncryptedRecordUnlocked,
    AnalyticsEventName.EncryptedRecordPasswordError,
  ],
  [AnalyticsFlow.FamilyMemberHistoryFlow]: [
    AnalyticsEventName.FamilyMemberHistoryAddStarted,
    AnalyticsEventName.FamilyMemberHistoryInProgRelationship,
    AnalyticsEventName.FamilyMemberHistoryInProgBirthDate,
    AnalyticsEventName.FamilyMemberHistoryInProgStatus,
    AnalyticsEventName.FamilyMemberHistoryInProgBloodGroup,
    AnalyticsEventName.FamilyMemberHistoryInProgEthnicity,
    AnalyticsEventName.FamilyMemberHistoryRequestConsentInteracted,
    AnalyticsEventName.FamilyMemberHistoryIntentSelected,
    AnalyticsEventName.FamilyMemberHistoryKeepTrackOfHealthInfo,
    AnalyticsEventName.FamilyMemberHistoryBookOnlineConsults,
    AnalyticsEventName.FamilyMemberHistoryStoreHealthRecords,
    AnalyticsEventName.FamilyMemberHistoryShareHealthInformation,
    AnalyticsEventName.FamilyMemberHistoryOther,
    AnalyticsEventName.FamilyMemberAddCompleted,
    AnalyticsEventName.FamilyMemberEditStarted,
    AnalyticsEventName.FamilyMemberEditCompleted,
    AnalyticsEventName.FamilyMemberHistoryRemoved,
    AnalyticsEventName.FamilyMemberHistoryConditionInteracted,
    AnalyticsEventName.FMConditionAddStarted,
    AnalyticsEventName.FMConditionAddInProgName,
    AnalyticsEventName.FMConditionAddInProgChronicity,
    AnalyticsEventName.FMConditionAddInProgDate,
    AnalyticsEventName.FMConditionAddInProgShared,
    AnalyticsEventName.FMConditionAddInProgStatus,
    AnalyticsEventName.FMConditionAddInProgEndDate,
    AnalyticsEventName.FMConditionAddInProgNotes,
    AnalyticsEventName.FMConditionAddInProgRecordsAdded,
    AnalyticsEventName.FMConditionAddCompleted,
    AnalyticsEventName.FMConditionInteracted,
    AnalyticsEventName.FMConditionEditStarted,
    AnalyticsEventName.FMConditionEditCompleted,
    AnalyticsEventName.FMConditionRemoved,
  ],
  [AnalyticsFlow.ConsentManagerFlow]: [
    AnalyticsEventName.ConsentManagerAddStarted,
    AnalyticsEventName.ConsentManagerInProgRelationship,
    AnalyticsEventName.ConsentManagerRequestConsentInteracted,
    AnalyticsEventName.ConsentManagerIntentSelected,
    AnalyticsEventName.ConsentManagerKeepTrackOfHealthInfo,
    AnalyticsEventName.ConsentManagerBookOnlineConsults,
    AnalyticsEventName.ConsentManagerStoreHealthRecords,
    AnalyticsEventName.ConsentManagerShareHealthInformation,
    AnalyticsEventName.ConsentManagerOther,
  ],
  [AnalyticsFlow.VitalFlow]: [
    AnalyticsEventName.VitalInteracted,
    AnalyticsEventName.VTBPAddStarted,
    AnalyticsEventName.VTBPAddInProgSystolic,
    AnalyticsEventName.VTBPAddInProgDiastolic,
    AnalyticsEventName.VTBPAddInProgDate,
    AnalyticsEventName.VTBPAddInProgTime,
    AnalyticsEventName.VTBPAddInProgRecorder,
    AnalyticsEventName.VTBPAddInProgPosition,
    AnalyticsEventName.VTBPAddCompleted,
    AnalyticsEventName.VTBPInteracted,
    AnalyticsEventName.VTBPEdited,
    AnalyticsEventName.VTBPRemoved,
    AnalyticsEventName.VTBTAddStarted,
    AnalyticsEventName.VTBTAddInProgTemparature,
    AnalyticsEventName.VTBTAddInProgDate,
    AnalyticsEventName.VTBTAddInProgTime,
    AnalyticsEventName.VTBTAddCompleted,
    AnalyticsEventName.VTBTInteracted,
    AnalyticsEventName.VTBTEdited,
    AnalyticsEventName.VTBTRemoved,
    AnalyticsEventName.VTPRAddStarted,
    AnalyticsEventName.VTPRAddInProgRate,
    AnalyticsEventName.VTPRAddInProgDate,
    AnalyticsEventName.VTPRAddInProgTime,
    AnalyticsEventName.VTPRAddCompleted,
    AnalyticsEventName.VTPRInteracted,
    AnalyticsEventName.VTPREdited,
    AnalyticsEventName.VTPRRemoved,
    AnalyticsEventName.VTOSLAddStarted,
    AnalyticsEventName.VTOSLAddInProgLevel,
    AnalyticsEventName.VTOSLAddInProgDate,
    AnalyticsEventName.VTOSLAddInProgTime,
    AnalyticsEventName.VTOSLAddCompleted,
    AnalyticsEventName.VTOSLInteracted,
    AnalyticsEventName.VTOSLEdited,
    AnalyticsEventName.VTOSLRemoved,
    AnalyticsEventName.VTRRAddStarted,
    AnalyticsEventName.VTRRAddInProgRate,
    AnalyticsEventName.VTRRAddInProgDate,
    AnalyticsEventName.VTRRAddInProgTime,
    AnalyticsEventName.VTRRAddCompleted,
    AnalyticsEventName.VTRRInteracted,
    AnalyticsEventName.VTRREdited,
    AnalyticsEventName.VTRRRemoved,
  ],
  [AnalyticsFlow.SurgeriesFlow]: [
    AnalyticsEventName.SurgeriesAddStarted,
    AnalyticsEventName.SurgeriesAddInProgType,
    AnalyticsEventName.SurgeriesAddInProgStatus,
    AnalyticsEventName.SurgeriesAddInProgDate,
    AnalyticsEventName.SurgeriesAddInProgNotes,
    AnalyticsEventName.SurgeriesAddInProgRecordsAdded,
    AnalyticsEventName.SurgeriesAddCompleted,
    AnalyticsEventName.SurgeriesInteracted,
    AnalyticsEventName.SurgeriesEdited,
    AnalyticsEventName.SurgeriesRemoved,
  ],
  [AnalyticsFlow.PreventativeScreeningFlow]: [
    AnalyticsEventName.PreventativeScreeningAddStarted,
    AnalyticsEventName.PreventativeScreeningAddInProgType,
    AnalyticsEventName.PreventativeScreeningAddInProgFamilyHistory,
    AnalyticsEventName.PreventativeScreeningAddInProgHPVDiagnosed,
    AnalyticsEventName.PreventativeScreeningAddInProgSexuallyActive,
    AnalyticsEventName.PreventativeScreeningAddInProgDate,
    AnalyticsEventName.PreventativeScreeningAddInProgNotes,
    AnalyticsEventName.PreventativeScreeningAddInProgRecordsAdded,
    AnalyticsEventName.PreventativeScreeningAddCompleted,
    AnalyticsEventName.PreventativeScreeningInteracted,
    AnalyticsEventName.PreventativeScreeningEdited,
    AnalyticsEventName.PreventativeScreeningRemoved,
  ],
  [AnalyticsFlow.VaccineFlow]: [
    AnalyticsEventName.VaccineAddStarted,
    AnalyticsEventName.VaccineAddInProgDate,
    AnalyticsEventName.VaccineAddInProgFullyVaccinated,
    AnalyticsEventName.VaccineAddInProgDoseCount,
    AnalyticsEventName.VaccineAddInProgRecordsAdded,
    AnalyticsEventName.VaccineAddCompleted,
    AnalyticsEventName.VaccineInteracted,
    AnalyticsEventName.VaccineEdited,
    AnalyticsEventName.VaccineRemoved,
  ],
  [AnalyticsFlow.LifestyleNutritionFlow]: [
    AnalyticsEventName.LNExerciseAddStarted,
    AnalyticsEventName.LNExerciseAddInProgActivity,
    AnalyticsEventName.LNExerciseAddInProgFrequency,
    AnalyticsEventName.LNExerciseAddInProgIntenseExercise,
    AnalyticsEventName.LNExerciseAddInProgFitnessRoutine,
    AnalyticsEventName.LNExerciseAddCompleted,
    AnalyticsEventName.LNExerciseInteracted,
    AnalyticsEventName.LNExerciseEdited,
    AnalyticsEventName.LNExerciseRemoved,
    AnalyticsEventName.LNDietAddStarted,
    AnalyticsEventName.LNDietAddInProgType,
    AnalyticsEventName.LNDietAddInProgPreferences,
    AnalyticsEventName.LNDietAddCompleted,
    AnalyticsEventName.LNDietInteracted,
    AnalyticsEventName.LNDietEdited,
    AnalyticsEventName.LNDietRemoved,
    AnalyticsEventName.LNATCAddStarted,
    AnalyticsEventName.LNATCAddInProgAlcohol,
    AnalyticsEventName.LNATCAddInProgTobacco,
    AnalyticsEventName.LNATCAddInProgCaffeine,
    AnalyticsEventName.LNATCAddCompleted,
    AnalyticsEventName.LNATCInteracted,
    AnalyticsEventName.LNATCEdited,
    AnalyticsEventName.LNATCRemoved,
    AnalyticsEventName.LNMHSAddStarted,
    AnalyticsEventName.LNMHSAddInProgMentalCondition,
    AnalyticsEventName.LNMHSAddInProgFamilyHistory,
    AnalyticsEventName.LNMHSAddInProgBurntOut,
    AnalyticsEventName.LNMHSAddInProgSleep,
    AnalyticsEventName.LNMHSAddInProgMorningFeel,
    AnalyticsEventName.LNMHSAddInProgStressLevel,
    AnalyticsEventName.LNMHSAddCompleted,
    AnalyticsEventName.LNMHSInteracted,
    AnalyticsEventName.LNMHSEdited,
    AnalyticsEventName.LNMHSRemoved,
    AnalyticsEventName.LNOccupationAddStarted,
    AnalyticsEventName.LNOccupationAddInProgName,
    AnalyticsEventName.LNOccupationAddInProgExtendedHours,
    AnalyticsEventName.LNOccupationAddCompleted,
    AnalyticsEventName.LNOccupationInteracted,
    AnalyticsEventName.LNOccupationEdited,
    AnalyticsEventName.LNOccupationRemoved,
  ],
  [AnalyticsFlow.AdditionalMedicalDecisionMakerFlow]: [
    AnalyticsEventName.AMDMAddStarted,
    AnalyticsEventName.AMDMInProgFirstName,
    AnalyticsEventName.AMDMInProgLastName,
    AnalyticsEventName.AMDMInProgRelationship,
    AnalyticsEventName.AMDMInProgMobileNumber,
    AnalyticsEventName.AMDMInProgRecordAdded,
    AnalyticsEventName.AMDMAddCompleted,
    AnalyticsEventName.AMDMEditStarted,
    AnalyticsEventName.AMDMEditCompleted,
    AnalyticsEventName.AMDMRemoved,
  ],
  [AnalyticsFlow.HealthProfileBasicInfo]: [
    AnalyticsEventName.HealthProfileBasicInfoAdded,
    AnalyticsEventName.HealthProfileBasicInfoEdited,
  ],
  [AnalyticsFlow.ReminderFlow]: [
    AnalyticsEventName.ReminderAddStarted,
    AnalyticsEventName.ReminderAddInProgName,
    AnalyticsEventName.ReminderAddInProgDate,
    AnalyticsEventName.ReminderAddInProgAllDay,
    AnalyticsEventName.ReminderAddInProgTime,
    AnalyticsEventName.ReminderAddInProgFrequency,
    AnalyticsEventName.ReminderAddInProgNotes,
    AnalyticsEventName.ReminderAddCompleted,
    AnalyticsEventName.ReminderMarkedCompleted,
    AnalyticsEventName.ReminderEditStarted,
    AnalyticsEventName.ReminderEditCompleted,
  ],
};

function getOStype() {
  const { userAgent } = navigator;

  if (userAgent.indexOf('Win') !== -1) return 'Windows';
  if (userAgent.indexOf('Mac') !== -1) return 'macOs';
  if (userAgent.indexOf('Linux') !== -1) return 'Linux';
  if (userAgent.indexOf('Android') !== -1) return 'Android';
  if (userAgent.indexOf('iOS') !== -1) return 'iOS';
  return 'not recognized';
}

const checkPlatform = () => {
  const isMobile = window.innerWidth <= 786;
  return isMobile ? 'Msite' : 'WebApp';
};

function getModel() {
  const agent = window.navigator.userAgent;
  const model = agent.substring(agent.indexOf('(') + 1, agent.indexOf(')'));
  return model || 'not recognized';
}

function getCompletetePlatformDetails() {
  const os = getOStype();
  const model = getModel();
  const platform = checkPlatform();
  return { os, model, platform };
}
let isMixpanelInitialized = false;

export class AnalyticsService {
  private static _instance: AnalyticsService;

  private constructor() {
    // Initialize Mixpanel only once
    if (!isMixpanelInitialized) {
      mixpanel.init(MIXPANEL_PROJECT_TOKEN, { debug: true });
      isMixpanelInitialized = true;
    }
  }

  private idsForFlows: any = {};

  public static get instance() {
    if (!this._instance) this._instance = new AnalyticsService();
    return this._instance;
  }

  // eslint-disable-next-line class-methods-use-this
  public trackEvent(eventName: AnalyticsEventName, eventProps: any) {
    const appType = getCompletetePlatformDetails();

    // Filter properties for each analytics service
    const mixpanelFilteredProps = filterMixpanelProperties(eventProps);
    const clearTapFilteredProps = filterClearTapProperties(eventProps);

    const mixpanelFinalProps = {
      ...mixpanelFilteredProps,
      $os: appType.os,
      $model: appType.model,
      platform: appType.platform,
    };

    const clearTapFinalProps = {
      ...clearTapFilteredProps,
      $os: appType.os,
      $model: appType.model,
      platform: appType.platform,
    };

    mixpanel.track(eventName, mixpanelFinalProps);
    clevertap.event.push(eventName, clearTapFinalProps);
  }

  public trackEventInFlow(flow: AnalyticsFlow, eventName: AnalyticsEventName, eventProps: any) {
    const eventsForFlow = FLOW_EVENTS_MAP[flow];
    const appType = getCompletetePlatformDetails();
    // const platform = getOStype();

    // make sure that the event is included in the flow
    if (!eventsForFlow?.includes(eventName)) {
      throw new Error(`Event ${eventName} is not part of flow ${flow}`);
    }

    // check if event is at the start of the flow
    let clearId = false;
    let flowId = generateRandomId();
    const eventIndex = eventsForFlow.indexOf(eventName);
    if (eventIndex === 0) {
      this.idsForFlows[flow] = flowId;
    } else if (eventIndex === eventsForFlow.length - 1) {
      clearId = true;
    }
    flowId = this.idsForFlows[flow];

    // Filter properties for each analytics service
    const mixpanelFilteredProps = filterMixpanelProperties(eventProps);
    const clearTapFilteredProps = filterClearTapProperties(eventProps);

    mixpanel.track(
      eventName,
      {
        ...mixpanelFilteredProps,
        $os: appType.os,
        $model: appType.model,
        platform: appType.platform,
        [EventPropsNames.UniqueFlowID]: flowId,
      },
      () => {
        if (clearId) {
          delete this.idsForFlows[flow];
        }
      }
    );
    clevertap.event.push(eventName, {
      ...clearTapFilteredProps,
      $os: appType.os,
      $model: appType.model,
      platform: appType.platform,
      [EventPropsNames.UniqueFlowID]: flowId,
    });
  }

  // eslint-disable-next-line class-methods-use-this
  public identifyUser(patient: any, extraProps?: any) {
    if (!patient) return;
    const patientEmail = patient?.telecom
      ? patient.telecom.find((telecom: PatientTelecom) => telecom.system === 'email')?.value
      : undefined;
    const patientPhoneNumber = patient?.telecom
      ? patient.telecom.find((telecom: PatientTelecom) => telecom.system === 'phone')?.value
      : undefined;
    const formattedPhoneNumber = patientPhoneNumber ? `+91 ${patientPhoneNumber}` : undefined;
    const patientPreferredLanguage = patient?.preferred_language || undefined;
    const height = patient?.height?.[0]?.valueQuantity?.value
      ? `${patient?.height?.[0]?.valueQuantity?.value} ${patient?.height?.[0]?.valueQuantity?.unit}`
      : undefined;
    const weight = patient?.weight?.[0]?.valueQuantity?.value
      ? `${patient?.weight?.[0]?.valueQuantity?.value} ${patient?.weight?.[0]?.valueQuantity?.unit}`
      : undefined;
    const appType = getCompletetePlatformDetails();
    mixpanel.identify(patient?.gatewayUser?.id || '');
    const finalProps = {
      [PatientPropsNames.UserId]: patient?.gatewayUser?.id,
      [PatientPropsNames.Email]: patientEmail,
      [PatientPropsNames.Age]: patient.age,
      [PatientPropsNames.Gender]: patient.gender,
      [PatientPropsNames.Height]: height,
      [PatientPropsNames.Weight]: weight,
      [PatientPropsNames.MeasurementSystem]: 'metric', // todo - now is hardcoded!
      [PatientPropsNames.RegistrationDate]: patient?.meta?.lastUpdated,
      [PatientPropsNames.ProfileCompletionPercentage]: 0, // todo - now is hardcoded!
      [PatientPropsNames.PhoneNumber]: formattedPhoneNumber,
      [PatientPropsNames.PreferredLanguage]: patientPreferredLanguage,
      [PatientPropsNames.Ethnicity]: patient?.ethnicity,
      [PatientPropsNames.HasFamilyMembers]: 'false', // default value
      // Add other patient-specific properties if available
      ...extraProps,
      $os: appType.os,
      $model: appType.model,
      platform: appType.platform,
    };

    // Filter properties for each analytics service
    const mixpanelFilteredProps = filterMixpanelProperties(finalProps);
    const clearTapFilteredProps = filterClearTapProperties(finalProps);

    mixpanel.people.set(mixpanelFilteredProps);
    clevertap.onUserLogin.push({
      Site: clearTapFilteredProps,
    });
  }

  // eslint-disable-next-line class-methods-use-this
  public identifyProvider(patient: any, healthProfileProps?: any) {
    mixpanel.identify(extractId(patient?.id) || '');
    mixpanel.people.set(healthProfileProps);
  }
}
