import { SNOMED_URL } from 'src/constants/medplumConstants';
import { AnalyticsService } from './analyticsService';
import { FHIR_VALUE_SET_FACT_URL } from './constants';

export enum HealthProfilePropsNames {
  co_name = 'co_name',
  sy_name = 'sy_name',
  me_name = 'me_name',
  su_name = 'su_name',
  al_name = 'al_name',
  sur_type = 'sur_type',
  ps_type = 'ps_type',
  ps_family_history = 'ps_family_history',
  vc_type = 'vc_type',
  vt_bp_systolic = 'vt_bp_systolic',
  vt_bp_diastolic = 'vt_bp_diastolic',
  vt_bt_temparature = 'vt_bt_temparature',
  vt_pr_rate = 'vt_pr_rate',
  vt_osl_level = 'vt_osl_level',
  vt_rr_rate = 'vt_rr_rate',
  ln_exercise_entry_point = 'ln_exercise_entry_point',
  ln_exercise_activity = 'ln_exercise_activity',
  ln_exercise_frequency = 'ln_exercise_frequency',
  ln_intense_excercise = 'ln_intense_excercise',
  ln_fitness_routine = 'ln_fitness_routine',
  ln_diet_entry_point = 'ln_diet_entry_point',
  ln_diet_type = 'ln_diet_type',
  ln_diet_preference = 'ln_diet_preference',
  ln_atc_entry_point = 'ln_atc_entry_point',
  ln_atc_alcohol = 'ln_atc_alcohol',
  ln_atc_tabacco = 'ln_atc_tabacco',
  ln_atc_caffine = 'ln_atc_caffine',
  ln_mhs_entry_point = 'ln_mhs_entry_point',
  ln_mhs_mental_condition = 'ln_mhs_mental_condition',
  ln_mhs_family_history = 'ln_mhs_family_history',
  ln_mhs_burnt_out = 'ln_mhs_burnt_out',
  ln_mhs_sleep_duration = 'ln_mhs_sleep_duration',
  ln_mhs_morning_feel = 'ln_mhs_morning_feel',
  ln_mhs_stress_level = 'ln_mhs_stress_level',
  ln_occupation_entry_point = 'ln_occupation_entry_point',
  ln_occupation = 'ln_occupation',
  ln_occupation_extended_hours = 'ln_occupation_extended_hours',
  rh_sexually_active = 'rh_sexually_active',
  rh_last_menstrual_date = 'rh_last_menstrual_date',
  rh_pregnancy_history = 'rh_pregnancy_history',
  rh_pregnancy_terminated = 'rh_pregnancy_terminated',
  rh_children = 'rh_children',
  rh_miscarriage = 'rh_miscarriage',
  rh_fertility_treatments = 'rh_fertility_treatments',
  rh_pregnancy_status = 'rh_pregnancy_status',
  fm_co_name = 'fm_co_name',
  fm_relationship = 'fm_relationship',
}

export enum FamilyMemberPropsNames {
  fm_relationship = 'fm_relationship',
  fm_birthdate = 'fm_birthdate',
  fm_status = 'fm_status',
  fm_blood_group = 'fm_blood_group',
  fm_ethnicity = 'fm_ethnicity',
  fm_condition = 'fm_condition',
  fm_co_name = 'fm_co_name',
}

export enum IdentifyProviderNames {
  FamilyMember = 'FamilyMember',
  ConditionFlow = 'ConditionFlow',
  SymptomsFlow = 'SymptomsFlow',
  Medications = 'Medications',
  Supplements = 'Supplements',
  Allergies = 'Allergies',
  Surgeries = 'Surgeries',
  LifestyleAndNutrition = 'LifestyleAndNutrition',
  ReproductiveHealth = 'ReproductiveHealth',
  PreventativeScreening = 'PreventativeScreening',
  Vaccines = 'Vaccines',
  Vitals = 'Vitals',
  HealthProfileBasicInfoAdded = 'HealthProfileBasicInfoAdded',
  HealthProfileBasicInfoEdited = 'HealthProfileBasicInfoEdited',
  FamilyMemberHistory = 'FamilyMemberHistory',
  FamilyMemberHistoryConditionFlow = 'FamilyMemberHistoryConditionFlow',
}

const extractVitals = (observations: any[]) => {
  const result: Record<string, number[]> = {
    [HealthProfilePropsNames.vt_bp_systolic]: [],
    [HealthProfilePropsNames.vt_bp_diastolic]: [],
    [HealthProfilePropsNames.vt_bt_temparature]: [],
    [HealthProfilePropsNames.vt_pr_rate]: [],
    [HealthProfilePropsNames.vt_osl_level]: [],
    [HealthProfilePropsNames.vt_rr_rate]: [],
  };

  const vitalKeyMap: Record<string, string> = {
    'vi:body-temperature': HealthProfilePropsNames.vt_bt_temparature,
    'vi:pulse-rate': HealthProfilePropsNames.vt_pr_rate,
    'vi:oxygen-saturation-level': HealthProfilePropsNames.vt_osl_level,
    'vi:respiratory-rate': HealthProfilePropsNames.vt_rr_rate,
  };

  const bloodPressureCodes: Record<string, string> = {
    '8480-6': HealthProfilePropsNames.vt_bp_systolic,
    '8462-4': HealthProfilePropsNames.vt_bp_diastolic,
  };

  observations?.forEach((obs) => {
    const ids = obs.identifier?.map((i: any) => i.value) || [];

    if (ids.includes('vi:blood-pressure') && Array.isArray(obs.component)) {
      obs.component.forEach((comp: any) => {
        const code = comp.code?.coding?.[0]?.code;
        const value = comp.valueQuantity?.value;
        const key = bloodPressureCodes[code];
        if (key && value != null) result[key].push(value);
      });
    }

    ids.forEach((id: string) => {
      const key = vitalKeyMap[id];
      const value = obs.valueQuantity?.value;
      if (key && value != null) result[key].push(value);
    });
  });

  return Object.fromEntries(Object.entries(result).filter(([, v]) => v.length));
};
const mapDisplayVaccines = (data: any[]) => {
  console.log('vaccines', data);
  
};
const extractPreventativeScreening = (data: any[]) => {
  console.log('extractPreventativeScreening', data);
};
const mapDisplayFromCodingSystem = (data: any[], path: string[], system: string): string[] => {
  return (
    data
      ?.map((entry) => {
        let current = entry;
        for (let i = 0; i < path.length; i++) {
          current = current?.[path[i]];
          if (!current) return null;
        }
        return Array.isArray(current) ? current.find((c: any) => c.system === system)?.display : null;
      })
      .filter(Boolean) || []
  );
};
const mapFamilyMemberHistory = (data: any) => {
  const relationshipDisplays =
    data?.FamilyMemberHistoryList?.map((member: any) => member.relationship?.coding?.[0]?.display).filter(Boolean) ||
    [];
  return relationshipDisplays;
};
const mapFamilyMemberHistoryConditionFlow = (data: any) => {
  const conditionNames =
    data?.FamilyMemberHistoryList?.flatMap(
      (member: any) =>
        member.condition
          ?.map((condition: any) => {
            const factCoding = condition.code?.coding?.find(
              (coding: any) => coding.system === 'https://fluentinhealth.com/FHIR/ValueSet/FACT'
            );
            return factCoding?.display;
          })
          .filter(Boolean) || []
    ) || [];
  return conditionNames;
};

const extractReproductiveHealthAnswers = (data: any) => {
  const mapping = {
    [HealthProfilePropsNames.rh_sexually_active]: 'reproductive-health-sexually-active',
    [HealthProfilePropsNames.rh_last_menstrual_date]: 'reproductive-health-last-menstrual-period-date',
    [HealthProfilePropsNames.rh_pregnancy_history]: 'reproductive-health-ever-been-pregnant',
    [HealthProfilePropsNames.rh_pregnancy_terminated]: 'reproductive-health-terminated-pregnancy',
    [HealthProfilePropsNames.rh_children]: 'reproductive-health-do-you-have-children',
    [HealthProfilePropsNames.rh_miscarriage]: 'reproductive-health-you-ever-miscarried',
    [HealthProfilePropsNames.rh_fertility_treatments]: 'reproductive-health-you-undergone-fertility-treatments',
    [HealthProfilePropsNames.rh_pregnancy_status]: 'reproductive-health-current-pregnancy-status',
  };

  const items = data?.[0]?.item || [];

  return Object.entries(mapping).reduce((acc, [key, linkId]) => {
    const answer = items.find((item: any) => item.linkId === linkId)?.answer?.[0];
    acc[key] = answer?.valueBoolean ?? answer?.valueDate ?? answer?.valueString ?? answer?.valueCoding?.display ?? null;
    return acc;
  }, {} as Record<string, any>);
};

export const identifyHealthProfileUser = (healthProfilename: string, healthProfileProps?: any) => {
  const userId = localStorage.getItem('userId') || '';
  const mappingHandlers: Record<string, () => any> = {
    [IdentifyProviderNames.ConditionFlow]: () => ({
      [HealthProfilePropsNames.co_name]: mapDisplayFromCodingSystem(
        healthProfileProps,
        ['code', 'coding'],
        FHIR_VALUE_SET_FACT_URL
      ),
    }),
    [IdentifyProviderNames.SymptomsFlow]: () => ({
      [HealthProfilePropsNames.sy_name]: mapDisplayFromCodingSystem(
        healthProfileProps,
        ['code', 'coding'],
        FHIR_VALUE_SET_FACT_URL
      ),
    }),
    [IdentifyProviderNames.Medications]: () => ({
      [HealthProfilePropsNames.me_name]: healthProfileProps
        ?.filter((med: any) => med.identifier?.some((id: any) => id.value === 'Medications'))
        ?.map(
          (entry: any) => entry?.medicationCodeableConcept?.coding?.find((c: any) => c.system === SNOMED_URL)?.display
        )
        .filter(Boolean),
    }),
    [IdentifyProviderNames.Supplements]: () => ({
      [HealthProfilePropsNames.su_name]: healthProfileProps
        ?.filter((med: any) => med.identifier?.some((id: any) => id.value === 'Supplements'))
        ?.map(
          (entry: any) => entry?.medicationCodeableConcept?.coding?.find((c: any) => c.system === SNOMED_URL)?.display
        )
        .filter(Boolean),
    }),
    [IdentifyProviderNames.Allergies]: () => ({
      [HealthProfilePropsNames.al_name]: mapDisplayFromCodingSystem(
        healthProfileProps,
        ['code', 'coding'],
        FHIR_VALUE_SET_FACT_URL
      ),
    }),
    [IdentifyProviderNames.Surgeries]: () => ({
      [HealthProfilePropsNames.sur_type]: mapDisplayFromCodingSystem(
        healthProfileProps,
        ['code', 'coding'],
        FHIR_VALUE_SET_FACT_URL
      ),
    }),
    [IdentifyProviderNames.LifestyleAndNutrition]: () => healthProfileProps,
    [IdentifyProviderNames.PreventativeScreening]: () => extractPreventativeScreening(healthProfileProps),
    [IdentifyProviderNames.Vaccines]: () => ({
      [HealthProfilePropsNames.vc_type]: mapDisplayVaccines(
        healthProfileProps,
      ),
    }),
    [IdentifyProviderNames.ReproductiveHealth]: () => extractReproductiveHealthAnswers(healthProfileProps),
    [IdentifyProviderNames.Vitals]: () => extractVitals(healthProfileProps),
    [IdentifyProviderNames.HealthProfileBasicInfoAdded]: () => healthProfileProps,
    [IdentifyProviderNames.HealthProfileBasicInfoEdited]: () => healthProfileProps,
    [IdentifyProviderNames.FamilyMemberHistory]: () => ({
      [HealthProfilePropsNames.fm_relationship]: mapFamilyMemberHistory(healthProfileProps),
    }),
    [IdentifyProviderNames.FamilyMemberHistoryConditionFlow]: () => ({
      [HealthProfilePropsNames.fm_co_name]: mapFamilyMemberHistoryConditionFlow(healthProfileProps),
    }),
  };

  const handler = mappingHandlers[healthProfilename];
  const identifyProviderProps = handler ? handler() : undefined;

  if (identifyProviderProps) {
    AnalyticsService.instance.identifyProvider(userId, identifyProviderProps);
  }
};
