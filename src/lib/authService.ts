/* eslint @typescript-eslint/naming-convention: 0 */
// Package modules
import axios from 'axios';
import UniversalCookie from 'universal-cookie';
import { QueryClient } from '@tanstack/react-query';

// Local modules
// eslint-disable-next-line import/no-cycle
import { api } from './api';
import { API_GATEWAY_URL, PUBLIC_URL_PREFIX, SHARED_STATE, TEMPORARY_TOKEN_QUERY_KEY } from './constants';
import { AnalyticsService } from '@lib/analyticsService';
import { enumContentType } from 'src/app/medical-records/lib/state';

const MAX_AUTH_RETRY_COUNT = 2;

export class AuthService {
  private readonly apiKey: string;

  private token: string | null;

  private refreshToken: string | null;

  private notifyOnUserUpdate: any;

  private authenticatedUser: any;

  private queryClient: QueryClient | undefined;

  private cookies: UniversalCookie;

  private static _instance: AuthService;

  private static totalFailedAuthCount: number = 0;

  private constructor() {
    this.cookies = new UniversalCookie();

    this.apiKey = API_GATEWAY_URL;
    this.token = this.cookies.get('token') || null;
    this.refreshToken = this.cookies.get('refreshToken') || null;
  }

  public static get instance() {
    if (!AuthService._instance) {
      AuthService._instance = new AuthService();
    }

    return AuthService._instance;
  }

  setQueryClient(newQueryClient: QueryClient) {
    this.queryClient = newQueryClient;
  }

  // eslint-disable-next-line class-methods-use-this
  async login(phoneNumber: string, onSuccess?: () => {} | undefined) {
    const gatewayAuthToken: any = await api.auth.login(phoneNumber);

    if (onSuccess) {
      onSuccess();
    }

    this._updateAuth(gatewayAuthToken, gatewayAuthToken);
  }

  async loginVerify(phoneNumber: string, code: string, onSuccess?: () => {} | null) {
    const { access_token, refresh_token } = await api.auth.loginVerify(phoneNumber, code);

    if (onSuccess) {
      onSuccess();
    }

    this._updateAuth(access_token, refresh_token);
  }

  async resetPinGenerateOTP(phoneNumber: string, onSuccess?: () => {} | null) {
    const { access_token, refresh_token } = await api.auth.resetPinGenerateOTP(phoneNumber);

    if (onSuccess) {
      onSuccess();
    }

    this._updateAuth(access_token, refresh_token);
  }

  async resetPinOTPVerify(phoneNumber: string, code: string, onSuccess?: () => {} | null) {
    const { access_token, refresh_token } = await api.auth.resetPinOTPVerify(phoneNumber, code);

    if (onSuccess) {
      onSuccess();
    }

    this._updateAuth(access_token, refresh_token);
  }

  async OTP2FAVerify(phoneNumber: string, code: string, pin: string, onSuccess?: () => {} | null) {
    const { access_token, refresh_token } = await api.auth.OTP2FAVerify(phoneNumber, code, pin);

    if (onSuccess) {
      onSuccess();
    }

    this._updateAuth(access_token, refresh_token);
  }

  async resetPinSave(phoneNumber: string, pin: string, code: string, onSuccess?: () => {} | null) {
    const { access_token, refresh_token } = await api.auth.resetPinSave(phoneNumber, pin, code);

    if (onSuccess) {
      onSuccess();
    }

    this._updateAuth(access_token, refresh_token);
  }

  async refreshAccessToken() {
    if (!this.refreshToken?.length) {
      // Completely logging out the user
      await this.logout();
      return;
    }

    const { access_token, refresh_token, success } = await api.auth.refreshToken();

    if (!success) {
      // Cannot refresh token --> log user out
      await this.logout();
      return;
    }

    // Token was refreshed -- updating store state
    this._updateAuth(access_token, refresh_token);

    if (this.queryClient instanceof QueryClient) {
      this.queryClient.invalidateQueries([SHARED_STATE.IS_LOGGED_IN]);
    }
  }

  // eslint-disable-next-line class-methods-use-this
  async resendCode(phoneNumber: string) {
    const gatewayAuthToken: any = await api.auth.login(phoneNumber);
    this._updateAuth(gatewayAuthToken, gatewayAuthToken);
  }

  async logout() {
    try {
      await api.auth.logout();
    } catch (e) {
      // TODO:
    }

    // Remove token/user even if the logout API call failed.
    this.queryClient?.clear();
    this.cookies.remove('token', { path: '/' });
    this.cookies.remove('refreshToken', { path: '/' });

    // Clear localStorage items
    localStorage.removeItem('userId');
    localStorage.removeItem('username');

    this.token = null;
    this.refreshToken = null;
    this.authenticatedUser = null;
    this.notifyOnUserUpdate?.(null);
    // AnalyticsService.instance.logout();
  }

  // Determine whether or not we have both an auth token and an authenticated user.
  // If we have an auth token but not a user entity, try to fetch the user.
  async isLoggedIn() {
    if (window.location.pathname.startsWith(PUBLIC_URL_PREFIX)) {
      return this._publicIsLoggedIn();
    }
    return this._privateIsLoggedIn();
  }

  private async _privateIsLoggedIn() {
    // Update the token with the latest cookie token.
    const latestToken = this.cookies.get('token') || null;

    if (this.token !== latestToken) {
      this.token = latestToken;
      this.authenticatedUser = null; // Re-fetched below.
    }

    if (this.token === null) {
      return false;
    }

    if (!this.authenticatedUser) {
      try {
        const username = localStorage.getItem('username');
        this.authenticatedUser = username && (await api.auth.me(username));

        if (this.authenticatedUser) {
          AnalyticsService.instance.identifyUser(this.authenticatedUser);
        }
      } catch (e) {
        this.token = null;
        this.authenticatedUser = null;
      }

      if (this.notifyOnUserUpdate) {
        this.notifyOnUserUpdate(this.authenticatedUser);
      }
    }

    const isLoggedIn = !!this.authenticatedUser;

    this.queryClient?.setQueryData([SHARED_STATE.IS_LOGGED_IN], isLoggedIn);
    return isLoggedIn;
  }

  private async _publicIsLoggedIn() {
    // Update the token with the token from the url.
    const { temporaryToken } = AuthService;

    if (this.token !== temporaryToken) {
      this.token = temporaryToken;
      this.authenticatedUser = null; // Re-fetched below.
    }

    if (this.token === null) {
      return false;
    }

    if (!this.authenticatedUser) {
      this.token = null;
      this.authenticatedUser = null;

      if (this.notifyOnUserUpdate) {
        this.notifyOnUserUpdate(this.authenticatedUser);
      }
    }

    const isLoggedIn = !!this.authenticatedUser;

    this.queryClient?.setQueryData([SHARED_STATE.IS_LOGGED_IN], isLoggedIn);

    return isLoggedIn;
  }

  private _updateAuth(token: string, refreshToken: string) {
    this.cookies.set('token', token, { path: '/' });
    this.cookies.set('refreshToken', refreshToken, { path: '/' });
    this.token = token;
    this.refreshToken = refreshToken;
  }

  public static get temporaryToken() {
    return new URLSearchParams(window.location.search).get(TEMPORARY_TOKEN_QUERY_KEY);
  }

  // The callback will be called when the authenticated user is updated.
  subscribe(callback: any) {
    this.notifyOnUserUpdate = callback;
  }

  getAuthenticatedUser() {
    return this.authenticatedUser;
  }

  setAuthenticatedUser(user: any) {
    this.authenticatedUser = user;
  }

  withAuthHeader(headers = {}) {
    return {
      Authorization: `Bearer ${this.token}`,
      'Content-Type': enumContentType.JSON,
      ...headers,
    };
  }

  withApiKeyHeader(headers = {}) {
    return {
      Authorization: `ApiKey ${this.apiKey}`,
      ...headers,
    };
  }

  // eslint-disable-next-line class-methods-use-this
  withApiGatewayAuthHeader(headers = {}) {
    return {
      'Content-Type': 'application/x-www-form-urlencoded',
      ...headers,
    };
  }

  public static get failedAuthCount() {
    return this.totalFailedAuthCount;
  }

  public static set failedAuthCount(count: number) {
    this.totalFailedAuthCount = count;
  }
}

// Token refresh logic
axios.interceptors.response.use(
  function onFulfilled(response) {
    // Any status code that lie within the range of 2xx cause this function to trigger
    // Do something with response data
    return response;
  },
  async function onRejected(error) {
    const { config, response } = error;
    // Any status codes that falls outside the range of 2xx cause this function to trigger
    // Do something with response error

    // On a public page, we don't need to refresh the user's token, so we can return a response immediately.
    if (window.location.pathname.startsWith(PUBLIC_URL_PREFIX)) {
      return response;
    }

    // Handle Unauthorized error and refresh token
    if (
      AuthService.failedAuthCount < MAX_AUTH_RETRY_COUNT &&
      [401, 403].includes(response?.status) &&
      config.url.includes(API_GATEWAY_URL)
    ) {
      // Increase the number of failed token refresh attempts.
      AuthService.failedAuthCount++;

      try {
        // Refresh token
        await AuthService.instance.refreshAccessToken();

        // Get previous headers without old Authorization header
        const { Authorization, ...withoutAuth } = config.headers;

        // Update config headers with updated token
        config.headers = AuthService.instance.withAuthHeader(withoutAuth);

        // Repeat request
        const result = await axios(config);

        // Reset the counter after successful authorization
        AuthService.failedAuthCount = 0;

        // Returns the result of a repeated request
        return await Promise.resolve(result);
      } catch (err) {
        return Promise.reject(err);
      }
    }

    return Promise.reject(error);
  }
);
