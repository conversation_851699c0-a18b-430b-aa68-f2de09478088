/**
 * Analytics Filtering Configuration
 *
 * This file contains filtering configurations for different analytics services
 * to ensure sensitive health data is not sent to external analytics platforms.
 */

/**
 * Properties to filter out from Clevertap analytics events
 * These contain sensitive health information that should not be tracked
 */
export enum ClevertapFilteredProperties {
  // Family and Condition Names
  fm_co_name = 'fm_co_name',
  co_name = 'co_name',
  sy_name = 'sy_name',
  me_name = 'me_name',
  su_name = 'su_name',
  al_name = 'al_name',

  // Surgery and Procedure Types
  sur_type = 'sur_type',

  // Lifestyle and Nutrition - Exercise
  ln_exercise_activity = 'ln_exercise_activity',

  // Lifestyle and Nutrition - Diet
  ln_diet_type = 'ln_diet_type',
  ln_diet_preference = 'ln_diet_preference',

  // Lifestyle and Nutrition - Alcohol, Tobacco, Caffeine
  ln_atc_alcohol = 'ln_atc_alcohol',
  ln_atc_tabacco = 'ln_atc_tabacco',
  ln_atc_caffine = 'ln_atc_caffine',

  // Lifestyle and Nutrition - Mental Health & Sleep
  ln_mhs_mental_condition = 'ln_mhs_mental_condition',
  ln_mhs_family_history = 'ln_mhs_family_history',
  ln_mhs_burnt_out = 'ln_mhs_burnt_out',
  ln_mhs_sleep_duration = 'ln_mhs_sleep_duration',
  ln_mhs_morning_feel = 'ln_mhs_morning_feel',

  // Reproductive Health
  rh_sexually_active = 'rh_sexually_active',
  rh_last_menstrual_date = 'rh_last_menstrual_date',
  rh_pregnancy_history = 'rh_pregnancy_history',
  rh_pregnancy_terminated = 'rh_pregnancy_terminated',
  rh_children = 'rh_children',
  rh_miscarriage = 'rh_miscarriage',
  rh_fertility_treatments = 'rh_fertility_treatments',
  rh_pregnancy_status = 'rh_pregnancy_status',

  // Preventive Screening
  ps_type = 'ps_type',
  ps_family_history = 'ps_family_history',
  ps_hpv = 'ps_hpv',
  ps_sexually_active = 'ps_sexually_active',

  // Vaccine Information
  vc_type = 'vc_type',

  // Vital Signs
  vt_bp_systolic = 'vt_bp_systolic',
  vt_bp_diastolic = 'vt_bp_diastolic',
  vt_bt_temparature = 'vt_bt_temparature',
  vt_pr_rate = 'vt_pr_rate',
  vt_osl_level = 'vt_osl_level',
  vt_rr_rate = 'vt_rr_rate',
}

/**
 * Array of Clevertap properties to filter out
 * Generated from the enum for easy iteration
 */
export const CLEVERTAP_FILTERED_PROPERTIES = Object.values(ClevertapFilteredProperties);

/**
 * Placeholder for future Mixpanel filtering properties
 * This can be expanded when Mixpanel filtering is needed
 */
export enum MixpanelFilteredProperties {
  // Personal Identifiers
  first_name = 'first_name',
  last_name = 'last_name',
  email = 'email',
  mobile_number = 'mobile_number',

  // User Types
  sab = 'sab',
  sr_user = 'sr_user',
  crm_user = 'crm_user',

  // Consent Information
  consentee_email = 'consentee_email',
  consentee_mobile_number = 'consentee_mobile_number',

  // Emergency Contact
  ec_first_name = 'ec_first_name',
  ec_last_name = 'ec_last_name',
  ec_mobile_number = 'ec_mobile_number',

  // Advanced Medical Directive
  amdm_first_name = 'amdm_first_name',
  amdm_last_name = 'amdm_last_name',
  amdm_mobile_number = 'amdm_mobile_number',

  // Health Insurance
  hi_policy_number = 'hi_policy_number',
  hi_mobile_number = 'hi_mobile_number',

  // Care Team
  ct_first_name = 'ct_first_name',
  ct_last_name = 'ct_last_name',
  ct_phone_number = 'ct_phone_number',
  ct_alt_phone_number = 'ct_alt_phone_number',
  ct_email = 'ct_email',
}

/**
 * Array of Mixpanel properties to filter out
 * Currently empty but ready for future use
 */
export const MIXPANEL_FILTERED_PROPERTIES = Object.values(MixpanelFilteredProperties);

/**
 * Analytics Service Types
 */
export enum AnalyticsService {
  CLEVERTAP = 'clevertap',
  MIXPANEL = 'mixpanel',
}

/**
 * Filters out sensitive properties from event props for Clevertap
 * @param eventProps - The original event properties object
 * @returns A new object with Clevertap filtered properties removed
 */
export function filterClevertapProperties(eventProps: any): any {
  if (!eventProps || typeof eventProps !== 'object') {
    return eventProps;
  }

  const filteredProps = { ...eventProps };

  // Remove properties that exist in the Clevertap filtered list
  CLEVERTAP_FILTERED_PROPERTIES.forEach((property) => {
    if (property in filteredProps) {
      delete filteredProps[property];
    }
  });

  return filteredProps;
}

/**
 * Filters out sensitive properties from event props for Mixpanel
 * @param eventProps - The original event properties object
 * @returns A new object with Mixpanel filtered properties removed
 */
export function filterMixpanelProperties(eventProps: any): any {
  if (!eventProps || typeof eventProps !== 'object') {
    return eventProps;
  }

  const filteredProps = { ...eventProps };

  // Remove properties that exist in the Mixpanel filtered list
  MIXPANEL_FILTERED_PROPERTIES.forEach((property) => {
    if (property in filteredProps) {
      delete filteredProps[property];
    }
  });

  return filteredProps;
}

/**
 * Generic function to filter properties based on analytics service
 * @param eventProps - The original event properties object
 * @param service - The analytics service to filter for
 * @returns A new object with service-specific filtered properties removed
 */
export function filterAnalyticsProperties(eventProps: any, service: AnalyticsService): any {
  switch (service) {
    case AnalyticsService.CLEVERTAP:
      return filterClevertapProperties(eventProps);
    case AnalyticsService.MIXPANEL:
      return filterMixpanelProperties(eventProps);
    default:
      return eventProps;
  }
}
